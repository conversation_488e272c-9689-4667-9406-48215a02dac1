// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityNotificationBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialCardView btnBack;

  @NonNull
  public final Chip chipReceived;

  @NonNull
  public final Chip chipSent;

  @NonNull
  public final LinearLayout layoutEmpty;

  @NonNull
  public final RecyclerView rvNotifications;

  @NonNull
  public final TextView tvNewNotifications;

  @NonNull
  public final TextView tvPendingApprovals;

  private ActivityNotificationBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialCardView btnBack, @NonNull Chip chipReceived, @NonNull Chip chipSent,
      @NonNull LinearLayout layoutEmpty, @NonNull RecyclerView rvNotifications,
      @NonNull TextView tvNewNotifications, @NonNull TextView tvPendingApprovals) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.chipReceived = chipReceived;
    this.chipSent = chipSent;
    this.layoutEmpty = layoutEmpty;
    this.rvNotifications = rvNotifications;
    this.tvNewNotifications = tvNewNotifications;
    this.tvPendingApprovals = tvPendingApprovals;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityNotificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityNotificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_notification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityNotificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnBack;
      MaterialCardView btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.chipReceived;
      Chip chipReceived = ViewBindings.findChildViewById(rootView, id);
      if (chipReceived == null) {
        break missingId;
      }

      id = R.id.chipSent;
      Chip chipSent = ViewBindings.findChildViewById(rootView, id);
      if (chipSent == null) {
        break missingId;
      }

      id = R.id.layoutEmpty;
      LinearLayout layoutEmpty = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmpty == null) {
        break missingId;
      }

      id = R.id.rvNotifications;
      RecyclerView rvNotifications = ViewBindings.findChildViewById(rootView, id);
      if (rvNotifications == null) {
        break missingId;
      }

      id = R.id.tvNewNotifications;
      TextView tvNewNotifications = ViewBindings.findChildViewById(rootView, id);
      if (tvNewNotifications == null) {
        break missingId;
      }

      id = R.id.tvPendingApprovals;
      TextView tvPendingApprovals = ViewBindings.findChildViewById(rootView, id);
      if (tvPendingApprovals == null) {
        break missingId;
      }

      return new ActivityNotificationBinding((LinearLayout) rootView, btnBack, chipReceived,
          chipSent, layoutEmpty, rvNotifications, tvNewNotifications, tvPendingApprovals);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
