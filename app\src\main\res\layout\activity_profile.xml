<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/gradient_primary"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="20dp">

            <ImageView
                android:id="@+id/btnBack"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:tint="@color/text_white"
                android:clickable="true"
                android:focusable="true" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="پروفایل کاربری"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_white"
                android:gravity="center"
                android:fontFamily="sans-serif-medium" />

            <View
                android:layout_width="40dp"
                android:layout_height="40dp" />

        </LinearLayout>

        <!-- کارت بانکی با عکس پروفایل -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:layout_marginTop="-10dp">

            <!-- کارت بانکی -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardBank"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/gradient_primary"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- ایکون عکس پروفایل و نام کاربری در بالا و سمت راست -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="end">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="55dp"
                                android:layout_height="55dp"
                                app:cardCornerRadius="27.5dp"
                                app:cardElevation="6dp"
                                app:strokeColor="@color/surface_color"
                                app:strokeWidth="2dp">

                                <ImageView
                                    android:id="@+id/ivCardProfileImage"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:src="@drawable/ic_person_modern"
                                    android:background="@color/surface_color"
                                    android:padding="10dp"
                                    android:scaleType="centerCrop"
                                    android:tint="@color/primary_color" />

                            </com.google.android.material.card.MaterialCardView>

                            <!-- نام کاربری زیر عکس -->
                            <TextView
                                android:id="@+id/tvCardUserName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text=""
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_white"
                                android:fontFamily="sans-serif-medium"
                                android:gravity="center"
                                android:maxLines="1"
                                android:ellipsize="end" />

                        </LinearLayout>

                    </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                    <!-- شماره تماس به شکل کارت بانکی -->
                    <TextView
                        android:id="@+id/tvCardPhone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="00000 0000 0000 0000"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_white"
                        android:layout_marginVertical="12dp"
                        android:fontFamily="monospace"
                        android:letterSpacing="0.1"
                        android:elevation="2dp" />

                <!-- سه گزینه موجودی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <!-- موجودی کارت -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="موجودی کارت"
                            android:textSize="12sp"
                            android:textColor="@color/text_white"
                            android:alpha="0.9"
                            android:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/tvCardBalance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="۰ تومان"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_white"
                            android:fontFamily="sans-serif-medium" />

                    </LinearLayout>

                    <!-- موجودی نقدی -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="موجودی نقدی"
                            android:textSize="12sp"
                            android:textColor="@color/text_white"
                            android:alpha="0.9"
                            android:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/tvCashBalance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="۰ تومان"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_white"
                            android:fontFamily="sans-serif-medium" />

                    </LinearLayout>

                    <!-- سهم شراکت -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="سهم شراکت"
                            android:textSize="12sp"
                            android:textColor="@color/text_white"
                            android:alpha="0.9"
                            android:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/tvPartnershipShare"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="۰ تومان"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_white"
                            android:fontFamily="sans-serif-medium" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>



            <!-- عکس پروفایل روی کارت -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="87.5dp"
                android:orientation="vertical"
                android:gravity="center">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    app:cardCornerRadius="50dp"
                    app:cardElevation="16dp"
                    app:strokeColor="@color/surface_color"
                    app:strokeWidth="4dp"
                    android:elevation="16dp">

                    <ImageView
                        android:id="@+id/ivProfileImage"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/ic_person_modern"
                        android:background="@color/surface_color"
                        android:padding="20dp"
                        android:scaleType="centerCrop" />

                </com.google.android.material.card.MaterialCardView>

                <!-- دکمه انتخاب/ویرایش عکس -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSelectImage"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="انتخاب و تنظیم عکس"
                    android:textSize="11sp"
                    android:layout_marginTop="8dp"
                    android:layout_gravity="center"
                    app:cornerRadius="18dp"
                    app:icon="@drawable/ic_camera"
                    app:iconSize="16dp"
                    android:minWidth="0dp"
                    android:paddingHorizontal="16dp"
                    android:backgroundTint="@color/primary_color"
                    android:textColor="@android:color/white"
                    style="@style/Widget.Material3.Button" />

            </LinearLayout>

        </RelativeLayout>

        <!-- فرم اطلاعات - لب به لب زیر کارت -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- عنوان -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="اطلاعات شخصی"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="20dp"
                    android:fontFamily="sans-serif-medium" />

                <!-- نام و نام خانوادگی -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="نام و نام خانوادگی"
                    app:startIconDrawable="@drawable/ic_person_modern"
                    app:startIconTint="@color/primary_color"
                    app:hintTextColor="@color/text_secondary"
                    app:boxStrokeColor="@color/primary_color"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etFullName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPersonName"
                        android:maxLines="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- ایمیل -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="ایمیل"
                    app:startIconDrawable="@drawable/ic_email"
                    app:startIconTint="@color/primary_color"
                    app:hintTextColor="@color/text_secondary"
                    app:boxStrokeColor="@color/primary_color"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress"
                        android:maxLines="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- شماره تماس -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="شماره تماس"
                    app:startIconDrawable="@drawable/ic_phone"
                    app:startIconTint="@color/primary_color"
                    app:hintTextColor="@color/text_secondary"
                    app:boxStrokeColor="@color/primary_color"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPhone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone"
                        android:maxLines="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif" />

                </com.google.android.material.textfield.TextInputLayout>



                <!-- پیام راهنما -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="✓ اطلاعات به صورت خودکار ذخیره می‌شوند"
                    android:textSize="14sp"
                    android:textColor="@color/success_color"
                    android:gravity="center"
                    android:padding="16dp"
                    android:background="@drawable/rounded_background"
                    android:backgroundTint="@color/success_light"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

</LinearLayout>
