package com.example.ma.data.local

import android.content.Context
import androidx.room.*
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.ma.data.local.dao.UserDao
import com.example.ma.data.local.dao.TransactionDao
import com.example.ma.data.local.dao.NotificationDao
import com.example.ma.data.local.entity.UserEntity
import com.example.ma.data.local.entity.TransactionEntity
import com.example.ma.data.local.entity.NotificationEntity
import com.example.ma.data.local.entity.SyncStatus
import com.example.ma.data.local.entity.TransactionType
import com.example.ma.data.local.entity.TransactionStatus
import com.example.ma.data.local.entity.NotificationStatus

/**
 * Room Database برای اپلیکیشن
 */
@Database(
    entities = [
        UserEntity::class,
        TransactionEntity::class,
        NotificationEntity::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun userDao(): UserDao
    abstract fun transactionDao(): TransactionDao
    abstract fun notificationDao(): NotificationDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        private const val DATABASE_NAME = "ma_database"
        
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    DATABASE_NAME
                )
                    .addCallback(DatabaseCallback())
                    .addMigrations(MIGRATION_1_2)
                    .fallbackToDestructiveMigration() // فقط در development
                    .build()
                
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * Migration از نسخه 1 به 2 (مثال)
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // مثال: اضافه کردن ستون جدید
                // database.execSQL("ALTER TABLE users ADD COLUMN new_column TEXT")
            }
        }
        
        /**
         * Callback برای مقداردهی اولیه دیتابیس
         */
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // مقداردهی اولیه دیتابیس در صورت نیاز
            }
            
            override fun onOpen(db: SupportSQLiteDatabase) {
                super.onOpen(db)
                // تنظیمات هنگام باز شدن دیتابیس
                db.execSQL("PRAGMA foreign_keys=ON")
            }
        }
        
        /**
         * پاک کردن دیتابیس (برای تست)
         */
        fun clearDatabase(context: Context) {
            synchronized(this) {
                INSTANCE?.close()
                context.deleteDatabase(DATABASE_NAME)
                INSTANCE = null
            }
        }
    }
}

/**
 * Type Converters برای Room
 */
class Converters {
    
    @TypeConverter
    fun fromSyncStatus(status: SyncStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toSyncStatus(status: String): SyncStatus {
        return SyncStatus.valueOf(status)
    }
    
    @TypeConverter
    fun fromTransactionType(type: TransactionType): String {
        return type.name
    }
    
    @TypeConverter
    fun toTransactionType(type: String): TransactionType {
        return TransactionType.valueOf(type)
    }
    
    @TypeConverter
    fun fromTransactionStatus(status: TransactionStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toTransactionStatus(status: String): TransactionStatus {
        return TransactionStatus.valueOf(status)
    }
    
    @TypeConverter
    fun fromNotificationStatus(status: NotificationStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toNotificationStatus(status: String): NotificationStatus {
        return NotificationStatus.valueOf(status)
    }
}
