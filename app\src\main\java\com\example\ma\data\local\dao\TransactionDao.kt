package com.example.ma.data.local.dao

import androidx.room.*
import com.example.ma.data.local.entity.TransactionEntity
import com.example.ma.data.local.entity.TransactionType
import com.example.ma.data.local.entity.TransactionStatus
import com.example.ma.data.local.entity.SyncStatus
import kotlinx.coroutines.flow.Flow

/**
 * DAO برای عملیات تراکنش‌ها
 */
@Dao
interface TransactionDao {
    
    /**
     * دریافت همه تراکنش‌ها
     */
    @Query("SELECT * FROM transactions ORDER BY created_at DESC")
    fun getAllTransactions(): Flow<List<TransactionEntity>>
    
    /**
     * دریافت تراکنش‌های کاربر
     */
    @Query("SELECT * FROM transactions WHERE user_id = :userId ORDER BY created_at DESC")
    fun getTransactionsByUser(userId: String): Flow<List<TransactionEntity>>
    
    /**
     * دریافت تراکنش‌ها بر اساس نوع
     */
    @Query("SELECT * FROM transactions WHERE transaction_type = :type ORDER BY created_at DESC")
    fun getTransactionsByType(type: TransactionType): Flow<List<TransactionEntity>>
    
    /**
     * دریافت تراکنش‌ها بر اساس وضعیت
     */
    @Query("SELECT * FROM transactions WHERE status = :status ORDER BY created_at DESC")
    fun getTransactionsByStatus(status: TransactionStatus): Flow<List<TransactionEntity>>
    
    /**
     * دریافت تراکنش‌های در انتظار تایید
     */
    @Query("SELECT * FROM transactions WHERE status = 'PENDING' ORDER BY created_at DESC")
    fun getPendingTransactions(): Flow<List<TransactionEntity>>
    
    /**
     * دریافت تراکنش‌های تایید شده
     */
    @Query("SELECT * FROM transactions WHERE status = 'APPROVED' ORDER BY created_at DESC")
    fun getApprovedTransactions(): Flow<List<TransactionEntity>>
    
    /**
     * دریافت تراکنش‌های نیازمند همگام‌سازی
     */
    @Query("SELECT * FROM transactions WHERE sync_status != 'SYNCED'")
    suspend fun getTransactionsNeedingSync(): List<TransactionEntity>
    
    /**
     * دریافت تراکنش بر اساس ID
     */
    @Query("SELECT * FROM transactions WHERE id = :transactionId")
    suspend fun getTransactionById(transactionId: String): TransactionEntity?
    
    /**
     * درج تراکنش جدید
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTransaction(transaction: TransactionEntity): Long
    
    /**
     * درج چندین تراکنش
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTransactions(transactions: List<TransactionEntity>): List<Long>
    
    /**
     * بروزرسانی تراکنش
     */
    @Update
    suspend fun updateTransaction(transaction: TransactionEntity): Int
    
    /**
     * حذف تراکنش
     */
    @Delete
    suspend fun deleteTransaction(transaction: TransactionEntity): Int
    
    /**
     * بروزرسانی وضعیت تراکنش
     */
    @Query("UPDATE transactions SET status = :status, updated_at = :timestamp WHERE id = :transactionId")
    suspend fun updateTransactionStatus(
        transactionId: String, 
        status: TransactionStatus, 
        timestamp: Long = System.currentTimeMillis()
    ): Int
    
    /**
     * تایید تراکنش
     */
    @Query("UPDATE transactions SET status = 'APPROVED', approved_by = :approvedBy, approved_at = :timestamp, updated_at = :timestamp WHERE id = :transactionId")
    suspend fun approveTransaction(
        transactionId: String, 
        approvedBy: String, 
        timestamp: Long = System.currentTimeMillis()
    ): Int
    
    /**
     * بروزرسانی وضعیت همگام‌سازی
     */
    @Query("UPDATE transactions SET sync_status = :status, last_sync = :timestamp WHERE id = :transactionId")
    suspend fun updateSyncStatus(
        transactionId: String, 
        status: SyncStatus, 
        timestamp: Long = System.currentTimeMillis()
    ): Int
    
    /**
     * تنظیم remote ID
     */
    @Query("UPDATE transactions SET remote_id = :remoteId WHERE id = :transactionId")
    suspend fun setRemoteId(transactionId: String, remoteId: String): Int
    
    /**
     * محاسبه مجموع فروش‌ها
     */
    @Query("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE transaction_type = 'SALE' AND status = 'APPROVED'")
    suspend fun getTotalSales(): Double
    
    /**
     * محاسبه مجموع هزینه‌ها
     */
    @Query("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE transaction_type = 'EXPENSE' AND status = 'APPROVED'")
    suspend fun getTotalExpenses(): Double
    
    /**
     * محاسبه مجموع برداشت‌ها
     */
    @Query("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE transaction_type = 'WITHDRAWAL' AND status = 'APPROVED'")
    suspend fun getTotalWithdrawals(): Double
    
    /**
     * محاسبه فروش‌های کاربر
     */
    @Query("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = :userId AND transaction_type = 'SALE' AND status = 'APPROVED'")
    suspend fun getUserSales(userId: String): Double
    
    /**
     * محاسبه هزینه‌های کاربر
     */
    @Query("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = :userId AND transaction_type = 'EXPENSE' AND status = 'APPROVED'")
    suspend fun getUserExpenses(userId: String): Double
    
    /**
     * محاسبه برداشت‌های کاربر
     */
    @Query("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = :userId AND transaction_type = 'WITHDRAWAL' AND status = 'APPROVED'")
    suspend fun getUserWithdrawals(userId: String): Double
    
    /**
     * جستجو در تراکنش‌ها
     */
    @Query("SELECT * FROM transactions WHERE description LIKE :query ORDER BY created_at DESC")
    fun searchTransactions(query: String): Flow<List<TransactionEntity>>
    
    /**
     * دریافت تراکنش‌ها در بازه زمانی
     */
    @Query("SELECT * FROM transactions WHERE created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    fun getTransactionsInRange(startTime: Long, endTime: Long): Flow<List<TransactionEntity>>
    
    /**
     * پاک کردن همه تراکنش‌ها
     */
    @Query("DELETE FROM transactions")
    suspend fun clearAllTransactions(): Int
    
    /**
     * شمارش تراکنش‌ها
     */
    @Query("SELECT COUNT(*) FROM transactions")
    suspend fun getTransactionCount(): Int
    
    /**
     * شمارش تراکنش‌های در انتظار
     */
    @Query("SELECT COUNT(*) FROM transactions WHERE status = 'PENDING'")
    suspend fun getPendingTransactionCount(): Int
}
