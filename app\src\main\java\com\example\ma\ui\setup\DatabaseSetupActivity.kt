package com.example.ma.ui.setup

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.utils.DatabaseSetup
import kotlinx.coroutines.launch

/**
 * Activity برای راه‌اندازی دیتابیس
 */
class DatabaseSetupActivity : AppCompatActivity() {

    private lateinit var tvSqlScript: TextView
    private lateinit var btnCopyScript: Button
    private lateinit var btnTestConnection: Button
    private lateinit var btnCreateSampleData: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_database_setup)

        initializeViews()
        setupClickListeners()
        displaySQLScript()
    }

    private fun initializeViews() {
        tvSqlScript = findViewById(R.id.tvSqlScript)
        btnCopyScript = findViewById(R.id.btnCopyScript)
        btnTestConnection = findViewById(R.id.btnTestConnection)
        btnCreateSampleData = findViewById(R.id.btnCreateSampleData)
    }

    private fun setupClickListeners() {
        btnCopyScript.setOnClickListener {
            copyScriptToClipboard()
        }

        btnTestConnection.setOnClickListener {
            testDatabaseConnection()
        }

        btnCreateSampleData.setOnClickListener {
            createSampleData()
        }
    }

    private fun displaySQLScript() {
        val script = DatabaseSetup.getSQLScript()
        tvSqlScript.text = script
    }

    private fun copyScriptToClipboard() {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("SQL Script", DatabaseSetup.getSQLScript())
        clipboard.setPrimaryClip(clip)
        Toast.makeText(this, "اسکریپت SQL کپی شد", Toast.LENGTH_SHORT).show()
    }

    private fun testDatabaseConnection() {
        lifecycleScope.launch {
            try {
                val success = DatabaseSetup.createTables()
                runOnUiThread {
                    if (success) {
                        Toast.makeText(this@DatabaseSetupActivity, "اتصال موفق - جداول آماده", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this@DatabaseSetupActivity, "خطا در اتصال یا ایجاد جداول", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    Toast.makeText(this@DatabaseSetupActivity, "خطا: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun createSampleData() {
        lifecycleScope.launch {
            try {
                val success = DatabaseSetup.insertSampleData()
                runOnUiThread {
                    if (success) {
                        Toast.makeText(this@DatabaseSetupActivity, "داده‌های نمونه ایجاد شد", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this@DatabaseSetupActivity, "خطا در ایجاد داده‌های نمونه", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    Toast.makeText(this@DatabaseSetupActivity, "خطا: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
}
