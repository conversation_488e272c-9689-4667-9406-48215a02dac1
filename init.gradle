// تنظیمات سراسری Gradle برای دور زدن تحریم‌ها

// تنظیم proxy های متعدد
def proxyList = [
    [host: "************", port: 80],
    [host: "***************", port: 5326],
    [host: "***********", port: 18080],
    [host: "**************", port: 6289],
    [host: "*************", port: 3129],
    [host: "**************", port: 6816],
    [host: "************", port: 3128],
    [host: "*************", port: 1000]
]

// تنظیم اولین proxy
def currentProxy = proxyList[0]
System.setProperty("http.proxyHost", currentProxy.host)
System.setProperty("http.proxyPort", currentProxy.port.toString())
System.setProperty("https.proxyHost", currentProxy.host)
System.setProperty("https.proxyPort", currentProxy.port.toString())

allprojects {
    repositories {
        // منابع چینی اولویت اول
        maven { 
            url 'https://maven.aliyun.com/repository/google'
            allowInsecureProtocol = false
        }
        maven { 
            url 'https://maven.aliyun.com/repository/central'
            allowInsecureProtocol = false
        }
        maven { 
            url 'https://maven.aliyun.com/repository/gradle-plugin'
            allowInsecureProtocol = false
        }
        maven { 
            url 'https://maven.aliyun.com/repository/public'
            allowInsecureProtocol = false
        }
        maven { 
            url 'https://repo.huaweicloud.com/repository/maven/'
            allowInsecureProtocol = false
        }
        maven { 
            url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/'
            allowInsecureProtocol = false
        }
        
        // منابع اصلی به عنوان fallback
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

// تنظیمات JVM برای بهبود عملکرد
gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
    }
}
