# ✅ طراحی مجدد کارت پروفایل

## 🎯 **تغییرات اعمال شده:**

### **1. سایز متن‌ها:**
```xml
<!-- نام کاربری: 50% بزرگتر -->
<TextView android:id="@+id/tvCardUserName"
    android:textSize="21sp"    <!-- قبل: 14sp -->
    android:layout_weight="1" />

<!-- "میکده": دو برابر بزرگتر -->
<TextView android:text="میکده"
    android:textSize="24sp"    <!-- قبل: 12sp -->
    android:gravity="end" />
```

### **2. ایکون عکس گرد وسط:**
```xml
<!-- ایکون 3 برابر بزرگتر از عکس‌های دیگه -->
<MaterialCardView
    android:layout_width="165dp"     <!-- 55dp × 3 -->
    android:layout_height="165dp"
    app:cardCornerRadius="82.5dp"    <!-- کاملاً گرد -->
    android:layout_marginTop="-20dp" <!-- 1/8 از کادر بیرون -->
    app:strokeWidth="6dp">

    <ImageView android:id="@+id/ivCardCenterProfileImage"
        android:padding="30dp" />
</MaterialCardView>
```

### **3. موقعیت‌بندی:**
- **نام کاربری:** سمت چپ
- **ایکون عکس:** دقیقاً وسط  
- **"میکده":** سمت راست
- **ایکون:** 1/8 از کادر آبی بیرون می‌زنه

### **4. کد ProfileActivity:**
```kotlin
// اضافه شده:
private lateinit var ivCardCenterProfileImage: ImageView

// هر دو عکس همزمان بروزرسانی می‌شوند:
setProfileImageWithCorrectSettings(ivProfileImage, bitmap)
setProfileImageWithCorrectSettings(ivCardCenterProfileImage, bitmap)
```

## 📐 **مشخصات طراحی:**

### **سایزها:**
- **نام کاربری:** 21sp (50% افزایش)
- **"میکده":** 24sp (100% افزایش)  
- **ایکون مرکزی:** 165dp (3× سایز عادی)
- **حاشیه بالا:** -20dp (1/8 بیرون از کادر)

### **ظاهر:**
- **ایکون:** کاملاً گرد با stroke سفید
- **موقعیت:** شناور روی کارت
- **همگام‌سازی:** با عکس پروفایل اصلی

## 🎨 **نتیجه نهایی:**
✅ **نام کاربری بزرگتر و واضح‌تر**
✅ **"میکده" برجسته و خوانا**
✅ **ایکون مرکزی بزرگ و جذاب**
✅ **طراحی متعادل و حرفه‌ای**
✅ **همگام‌سازی کامل عکس‌ها**

**کارت پروفایل حالا ظاهر مدرن و جذابی دارد!** 🎉
