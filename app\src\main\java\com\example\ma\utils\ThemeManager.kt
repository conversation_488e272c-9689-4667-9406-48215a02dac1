package com.example.ma.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.appcompat.app.AppCompatDelegate

/**
 * مدیریت تم‌های اپلیکیشن (روشن/تیره)
 */
object ThemeManager {
    
    private const val PREFS_NAME = "theme_prefs"
    private const val KEY_THEME_MODE = "theme_mode"
    
    const val THEME_LIGHT = 0
    const val THEME_DARK = 1
    const val THEME_SYSTEM = 2
    
    /**
     * دریافت تم فعلی
     */
    fun getCurrentTheme(context: Context): Int {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getInt(KEY_THEME_MODE, THEME_SYSTEM)
    }
    
    /**
     * تنظیم تم جدید
     */
    fun setTheme(context: Context, themeMode: Int) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentTheme = prefs.getInt(KEY_THEME_MODE, THEME_SYSTEM)

        // فقط اگر تم تغییر کرده باشد، ذخیره و اعمال کن
        if (currentTheme != themeMode) {
            prefs.edit().putInt(KEY_THEME_MODE, themeMode).apply()
            applyTheme(themeMode)
        }
    }
    
    /**
     * اعمال تم
     */
    fun applyTheme(themeMode: Int) {
        when (themeMode) {
            THEME_LIGHT -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            THEME_DARK -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
            THEME_SYSTEM -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
        }
    }
    
    /**
     * اعمال تم ذخیره شده
     */
    fun applySavedTheme(context: Context) {
        val currentTheme = getCurrentTheme(context)
        applyTheme(currentTheme)
    }
    
    /**
     * دریافت نام تم
     */
    fun getThemeName(context: Context, themeMode: Int): String {
        return when (themeMode) {
            THEME_LIGHT -> "تم روشن"
            THEME_DARK -> "تم مشکی"
            THEME_SYSTEM -> "پیروی از سیستم"
            else -> "نامشخص"
        }
    }
    
    /**
     * بررسی اینکه آیا تم تیره فعال است
     */
    fun isDarkTheme(context: Context): Boolean {
        val currentTheme = getCurrentTheme(context)
        return when (currentTheme) {
            THEME_DARK -> true
            THEME_LIGHT -> false
            THEME_SYSTEM -> {
                val nightModeFlags = context.resources.configuration.uiMode and 
                    android.content.res.Configuration.UI_MODE_NIGHT_MASK
                nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES
            }
            else -> false
        }
    }
}
