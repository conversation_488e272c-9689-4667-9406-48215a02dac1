package com.example.ma.integration

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.ma.data.local.AppDatabase
import com.example.ma.data.local.entity.*
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * تست‌های یکپارچگی برای دیتابیس
 */
@RunWith(AndroidJUnit4::class)
class DatabaseIntegrationTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var database: AppDatabase

    @Before
    fun setup() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            AppDatabase::class.java
        ).allowMainThreadQueries().build()
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun userTransactionRelationship_shouldWorkCorrectly() = runTest {
        // Given - Create a user
        val user = UserEntity(
            id = "user1",
            username = "testuser",
            displayName = "Test User",
            fullName = null,
            email = null,
            phone = null
        )
        database.userDao().insertUser(user)

        // When - Create transactions for the user
        val transactions = listOf(
            TransactionEntity(
                id = "trans1",
                userId = "user1",
                transactionType = TransactionType.SALE,
                amount = 100.0,
                description = "Sale 1",
                status = TransactionStatus.APPROVED
            ),
            TransactionEntity(
                id = "trans2",
                userId = "user1",
                transactionType = TransactionType.EXPENSE,
                amount = 50.0,
                description = "Expense 1",
                status = TransactionStatus.APPROVED
            )
        )
        database.transactionDao().insertTransactions(transactions)

        // Then - Verify relationships
        val userTransactions = database.transactionDao().getTransactionsByUser("user1").first()
        assertEquals(2, userTransactions.size)

        val userSales = database.transactionDao().getUserSales("user1")
        assertEquals(100.0, userSales, 0.01)

        val userExpenses = database.transactionDao().getUserExpenses("user1")
        assertEquals(50.0, userExpenses, 0.01)
    }

    @Test
    fun notificationUserRelationship_shouldWorkCorrectly() = runTest {
        // Given - Create users
        val user1 = UserEntity(
            id = "user1",
            username = "sender",
            displayName = "Sender User",
            fullName = null,
            email = null,
            phone = null
        )
        val user2 = UserEntity(
            id = "user2",
            username = "receiver",
            displayName = "Receiver User",
            fullName = null,
            email = null,
            phone = null
        )
        database.userDao().insertUsers(listOf(user1, user2))

        // When - Create notification
        val notification = NotificationEntity(
            id = "notif1",
            fromUserId = "user1",
            toUserId = "user2",
            transactionType = TransactionType.SALE,
            amount = 100.0,
            description = "Sale notification",
            status = NotificationStatus.PENDING
        )
        database.notificationDao().insertNotification(notification)

        // Then - Verify relationships
        val receiverNotifications = database.notificationDao().getNotificationsForUser("user2").first()
        assertEquals(1, receiverNotifications.size)
        assertEquals("user1", receiverNotifications[0].fromUserId)

        val senderNotifications = database.notificationDao().getNotificationsSentByUser("user1").first()
        assertEquals(1, senderNotifications.size)
        assertEquals("user2", senderNotifications[0].toUserId)
    }

    @Test
    fun cascadeDelete_shouldWorkCorrectly() = runTest {
        // Given - Create user with transactions and notifications
        val user = UserEntity(
            id = "user1",
            username = "testuser",
            displayName = "Test User",
            fullName = null,
            email = null,
            phone = null
        )
        database.userDao().insertUser(user)

        val transaction = TransactionEntity(
            id = "trans1",
            userId = "user1",
            transactionType = TransactionType.SALE,
            amount = 100.0,
            description = "Test transaction"
        )
        database.transactionDao().insertTransaction(transaction)

        val notification = NotificationEntity(
            id = "notif1",
            fromUserId = "user1",
            toUserId = "user1",
            transactionType = TransactionType.SALE,
            amount = 100.0,
            description = "Test notification"
        )
        database.notificationDao().insertNotification(notification)

        // When - Delete user
        database.userDao().deleteUser(user)

        // Then - Related data should be deleted (cascade)
        val remainingTransactions = database.transactionDao().getTransactionsByUser("user1").first()
        assertEquals(0, remainingTransactions.size)

        val remainingNotifications = database.notificationDao().getNotificationsForUser("user1").first()
        assertEquals(0, remainingNotifications.size)
    }

    @Test
    fun syncStatusTracking_shouldWorkCorrectly() = runTest {
        // Given - Create entities with different sync statuses
        val users = listOf(
            UserEntity(
                id = "user1",
                username = "user1",
                displayName = "User 1",
                fullName = null,
                email = null,
                phone = null,
                syncStatus = SyncStatus.SYNCED
            ),
            UserEntity(
                id = "user2",
                username = "user2",
                displayName = "User 2",
                fullName = null,
                email = null,
                phone = null,
                syncStatus = SyncStatus.PENDING
            ),
            UserEntity(
                id = "user3",
                username = "user3",
                displayName = "User 3",
                fullName = null,
                email = null,
                phone = null,
                syncStatus = SyncStatus.FAILED
            )
        )
        database.userDao().insertUsers(users)

        // When - Query users needing sync
        val usersNeedingSync = database.userDao().getUsersNeedingSync()

        // Then - Should return only non-synced users
        assertEquals(2, usersNeedingSync.size)
        assertTrue(usersNeedingSync.any { it.id == "user2" })
        assertTrue(usersNeedingSync.any { it.id == "user3" })
        assertFalse(usersNeedingSync.any { it.id == "user1" })
    }

    @Test
    fun transactionStatistics_shouldCalculateCorrectly() = runTest {
        // Given - Create users and transactions
        val users = listOf(
            UserEntity(
                id = "user1",
                username = "user1",
                displayName = "User 1",
                fullName = null,
                email = null,
                phone = null
            ),
            UserEntity(
                id = "user2",
                username = "user2",
                displayName = "User 2",
                fullName = null,
                email = null,
                phone = null
            )
        )
        database.userDao().insertUsers(users)

        val transactions = listOf(
            // User 1 transactions
            TransactionEntity(
                id = "trans1",
                userId = "user1",
                transactionType = TransactionType.SALE,
                amount = 100.0,
                description = "Sale 1",
                status = TransactionStatus.APPROVED
            ),
            TransactionEntity(
                id = "trans2",
                userId = "user1",
                transactionType = TransactionType.EXPENSE,
                amount = 30.0,
                description = "Expense 1",
                status = TransactionStatus.APPROVED
            ),
            // User 2 transactions
            TransactionEntity(
                id = "trans3",
                userId = "user2",
                transactionType = TransactionType.SALE,
                amount = 200.0,
                description = "Sale 2",
                status = TransactionStatus.APPROVED
            ),
            TransactionEntity(
                id = "trans4",
                userId = "user2",
                transactionType = TransactionType.WITHDRAWAL,
                amount = 50.0,
                description = "Withdrawal 1",
                status = TransactionStatus.APPROVED
            ),
            // Pending transaction (should not be counted)
            TransactionEntity(
                id = "trans5",
                userId = "user1",
                transactionType = TransactionType.SALE,
                amount = 500.0,
                description = "Pending Sale",
                status = TransactionStatus.PENDING
            )
        )
        database.transactionDao().insertTransactions(transactions)

        // When - Calculate statistics
        val totalSales = database.transactionDao().getTotalSales()
        val totalExpenses = database.transactionDao().getTotalExpenses()
        val totalWithdrawals = database.transactionDao().getTotalWithdrawals()

        val user1Sales = database.transactionDao().getUserSales("user1")
        val user1Expenses = database.transactionDao().getUserExpenses("user1")
        val user2Sales = database.transactionDao().getUserSales("user2")
        val user2Withdrawals = database.transactionDao().getUserWithdrawals("user2")

        // Then - Verify calculations
        assertEquals(300.0, totalSales, 0.01) // 100 + 200 (pending not counted)
        assertEquals(30.0, totalExpenses, 0.01)
        assertEquals(50.0, totalWithdrawals, 0.01)

        assertEquals(100.0, user1Sales, 0.01)
        assertEquals(30.0, user1Expenses, 0.01)
        assertEquals(200.0, user2Sales, 0.01)
        assertEquals(50.0, user2Withdrawals, 0.01)
    }

    @Test
    fun searchFunctionality_shouldWorkCorrectly() = runTest {
        // Given - Create test data
        val users = listOf(
            UserEntity(
                id = "user1",
                username = "john_doe",
                displayName = "John Doe",
                fullName = "John Smith Doe",
                email = null,
                phone = null
            ),
            UserEntity(
                id = "user2",
                username = "jane_smith",
                displayName = "Jane Smith",
                fullName = "Jane Mary Smith",
                email = null,
                phone = null
            )
        )
        database.userDao().insertUsers(users)

        val transactions = listOf(
            TransactionEntity(
                id = "trans1",
                userId = "user1",
                transactionType = TransactionType.SALE,
                amount = 100.0,
                description = "Water bottle sale to customer A"
            ),
            TransactionEntity(
                id = "trans2",
                userId = "user2",
                transactionType = TransactionType.EXPENSE,
                amount = 50.0,
                description = "Office supplies purchase"
            )
        )
        database.transactionDao().insertTransactions(transactions)

        // When - Search users
        val userSearchResults = database.userDao().searchUsers("%john%").first()
        assertEquals(1, userSearchResults.size)
        assertEquals("john_doe", userSearchResults[0].username)

        // When - Search transactions
        val transactionSearchResults = database.transactionDao().searchTransactions("%bottle%").first()
        assertEquals(1, transactionSearchResults.size)
        assertEquals("trans1", transactionSearchResults[0].id)
    }
}
