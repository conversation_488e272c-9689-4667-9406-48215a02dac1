package com.example.ma.ui.notifications;

/**
 * Adapter برای نمایش اعلانات با عکس پروفایل کاربران
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010 \n\u0002\b\u0003\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\'(B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u001a\u0010\u000e\u001a\u00020\u000f2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\fJ\u0010\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u0013H\u0002J\u0010\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u0006H\u0002J\b\u0010\u0016\u001a\u00020\u0017H\u0016J\u0018\u0010\u0018\u001a\u00020\u000f2\u0006\u0010\u0019\u001a\u00020\u00022\u0006\u0010\u001a\u001a\u00020\u0017H\u0016J\u0018\u0010\u001b\u001a\u00020\u00022\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0005\u001a\u00020\u0017H\u0016J\u000e\u0010\u001e\u001a\u00020\u000f2\u0006\u0010\u001f\u001a\u00020\tJ\u000e\u0010 \u001a\u00020\u000f2\u0006\u0010!\u001a\u00020\u0006J\u0018\u0010\"\u001a\u00020\u000f2\u0006\u0010\u0019\u001a\u00020\u00022\u0006\u0010#\u001a\u00020\u0006H\u0002J \u0010$\u001a\u00020\u000f2\u0018\u0010%\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\f0&R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\n\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/example/ma/ui/notifications/NotificationAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/ma/ui/notifications/NotificationAdapter$NotificationViewHolder;", "context", "Landroid/content/Context;", "viewType", "", "(Landroid/content/Context;Ljava/lang/String;)V", "actionListener", "Lcom/example/ma/ui/notifications/NotificationAdapter$OnNotificationActionListener;", "notifications", "", "", "", "addNotification", "", "notification", "formatCurrency", "amount", "", "formatTime", "timestamp", "getItemCount", "", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "setOnNotificationActionListener", "listener", "setViewType", "newViewType", "setupStatus", "status", "updateNotifications", "newNotifications", "", "NotificationViewHolder", "OnNotificationActionListener", "app_debug"})
public final class NotificationAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder> {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private java.lang.String viewType;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.util.Map<java.lang.String, java.lang.Object>> notifications = null;
    @org.jetbrains.annotations.Nullable
    private com.example.ma.ui.notifications.NotificationAdapter.OnNotificationActionListener actionListener;
    
    public NotificationAdapter(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    java.lang.String viewType) {
        super();
    }
    
    public final void setOnNotificationActionListener(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.notifications.NotificationAdapter.OnNotificationActionListener listener) {
    }
    
    public final void setViewType(@org.jetbrains.annotations.NotNull
    java.lang.String newViewType) {
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder holder, int position) {
    }
    
    @java.lang.Override
    public int getItemCount() {
        return 0;
    }
    
    private final void setupStatus(com.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder holder, java.lang.String status) {
    }
    
    private final java.lang.String formatCurrency(double amount) {
        return null;
    }
    
    private final java.lang.String formatTime(java.lang.String timestamp) {
        return null;
    }
    
    public final void updateNotifications(@org.jetbrains.annotations.NotNull
    java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> newNotifications) {
    }
    
    public final void addNotification(@org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, ? extends java.lang.Object> notification) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u000f\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0007R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0007R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0007R\u0011\u0010\u0012\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0007R\u0011\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0007R\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u001a\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u0011\u0010\u001c\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0019R\u0011\u0010\u001e\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0019R\u0011\u0010 \u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0019R\u0011\u0010\"\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0019R\u0011\u0010$\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0019\u00a8\u0006&"}, d2 = {"Lcom/example/ma/ui/notifications/NotificationAdapter$NotificationViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "btnApprove", "getBtnApprove", "()Landroid/view/View;", "btnReject", "getBtnReject", "cardStatus", "getCardStatus", "ivNotificationType", "Landroid/widget/ImageView;", "getIvNotificationType", "()Landroid/widget/ImageView;", "layoutActions", "getLayoutActions", "layoutDescription", "getLayoutDescription", "layoutProductCount", "getLayoutProductCount", "tvAmount", "Landroid/widget/TextView;", "getTvAmount", "()Landroid/widget/TextView;", "tvDescription", "getTvDescription", "tvProductCount", "getTvProductCount", "tvSenderName", "getTvSenderName", "tvStatus", "getTvStatus", "tvTime", "getTvTime", "tvTransactionType", "getTvTransactionType", "app_debug"})
    public static final class NotificationViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull
        private final android.widget.ImageView ivNotificationType = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvSenderName = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvTransactionType = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvStatus = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View cardStatus = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvAmount = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvProductCount = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View layoutProductCount = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvDescription = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View layoutDescription = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvTime = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View layoutActions = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View btnApprove = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View btnReject = null;
        
        public NotificationViewHolder(@org.jetbrains.annotations.NotNull
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.ImageView getIvNotificationType() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvSenderName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvTransactionType() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvStatus() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getCardStatus() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvAmount() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvProductCount() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getLayoutProductCount() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvDescription() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getLayoutDescription() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvTime() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getLayoutActions() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getBtnApprove() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getBtnReject() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0018\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\t"}, d2 = {"Lcom/example/ma/ui/notifications/NotificationAdapter$OnNotificationActionListener;", "", "onApprove", "", "notificationId", "", "position", "", "onReject", "app_debug"})
    public static abstract interface OnNotificationActionListener {
        
        public abstract void onApprove(@org.jetbrains.annotations.NotNull
        java.lang.String notificationId, int position);
        
        public abstract void onReject(@org.jetbrains.annotations.NotNull
        java.lang.String notificationId, int position);
    }
}