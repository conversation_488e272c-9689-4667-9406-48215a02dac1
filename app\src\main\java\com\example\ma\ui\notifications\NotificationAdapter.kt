package com.example.ma.ui.notifications

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter برای نمایش اعلانات با عکس پروفایل کاربران
 */
class NotificationAdapter(
    private val context: Context
) : RecyclerView.Adapter<NotificationAdapter.NotificationViewHolder>() {

    private val notifications = mutableListOf<Map<String, Any>>()

    interface OnNotificationActionListener {
        fun onApprove(notificationId: String, position: Int)
        fun onReject(notificationId: String, position: Int)
    }

    private var actionListener: OnNotificationActionListener? = null

    fun setOnNotificationActionListener(listener: OnNotificationActionListener) {
        this.actionListener = listener
    }

    class NotificationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivNotificationType: ImageView = itemView.findViewById(R.id.ivNotificationType)
        val tvSenderName: TextView = itemView.findViewById(R.id.tvSenderName)
        val tvTransactionType: TextView = itemView.findViewById(R.id.tvTransactionType)
        val tvStatus: TextView = itemView.findViewById(R.id.tvStatus)
        val cardStatus: View = itemView.findViewById(R.id.cardStatus)
        val tvAmount: TextView = itemView.findViewById(R.id.tvAmount)
        val tvProductCount: TextView = itemView.findViewById(R.id.tvProductCount)
        val layoutProductCount: View = itemView.findViewById(R.id.layoutProductCount)
        val tvDescription: TextView = itemView.findViewById(R.id.tvDescription)
        val layoutDescription: View = itemView.findViewById(R.id.layoutDescription)
        val tvTime: TextView = itemView.findViewById(R.id.tvTime)
        val layoutActions: View = itemView.findViewById(R.id.layoutActions)
        val btnApprove: View = itemView.findViewById(R.id.btnApprove)
        val btnReject: View = itemView.findViewById(R.id.btnReject)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_notification, parent, false)
        return NotificationViewHolder(view)
    }

    override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
        val notification = notifications[position]
        
        // اطلاعات فرستنده
        val senderName = notification["sender_name"] as? String ?: "کاربر ناشناس"
        holder.tvSenderName.text = senderName
        
        // نوع تراکنش
        val transactionType = notification["transaction_type"] as? String ?: "تراکنش"
        val typeText = when (transactionType) {
            "sale" -> "درخواست تایید فروش"
            "purchase" -> "درخواست تایید خرید"
            "withdrawal" -> "درخواست تایید برداشت"
            "deposit" -> "درخواست تایید واریز"
            else -> "درخواست تایید $transactionType"
        }
        holder.tvTransactionType.text = typeText
        
        // آیکون نوع تراکنش
        val iconRes = when (transactionType) {
            "sale" -> R.drawable.ic_trending_up
            "purchase" -> R.drawable.ic_trending_down
            "withdrawal" -> R.drawable.ic_account_balance
            "deposit" -> R.drawable.ic_account_balance
            else -> R.drawable.ic_receipt
        }
        holder.ivNotificationType.setImageResource(iconRes)
        
        // مبلغ
        val amount = notification["amount"] as? Double ?: 0.0
        holder.tvAmount.text = formatCurrency(amount)
        
        // تعداد محصول (اختیاری)
        val productCount = notification["product_count"] as? Int
        if (productCount != null && productCount > 0) {
            holder.layoutProductCount.visibility = View.VISIBLE
            holder.tvProductCount.text = "$productCount عدد"
        } else {
            holder.layoutProductCount.visibility = View.GONE
        }
        
        // توضیحات (اختیاری)
        val description = notification["description"] as? String
        if (!description.isNullOrEmpty()) {
            holder.layoutDescription.visibility = View.VISIBLE
            holder.tvDescription.text = description
        } else {
            holder.layoutDescription.visibility = View.GONE
        }
        
        // وضعیت
        val status = notification["status"] as? String ?: "pending"
        setupStatus(holder, status)
        
        // زمان
        val timestamp = notification["created_at"] as? String
        if (timestamp != null) {
            holder.tvTime.text = formatTime(timestamp)
        } else {
            holder.tvTime.text = "نامشخص"
        }
    }

    override fun getItemCount(): Int = notifications.size

    private fun setupStatus(holder: NotificationViewHolder, status: String) {
        when (status) {
            "pending" -> {
                holder.tvStatus.text = "در انتظار"
                holder.cardStatus.setBackgroundColor(context.getColor(R.color.warning_color))
                holder.layoutActions.visibility = View.VISIBLE
            }
            "approved" -> {
                holder.tvStatus.text = "تایید شده"
                holder.cardStatus.setBackgroundColor(context.getColor(R.color.success_color))
                holder.layoutActions.visibility = View.GONE
            }
            "rejected" -> {
                holder.tvStatus.text = "رد شده"
                holder.cardStatus.setBackgroundColor(context.getColor(R.color.error_color))
                holder.layoutActions.visibility = View.GONE
            }
            else -> {
                holder.tvStatus.text = "نامشخص"
                holder.cardStatus.setBackgroundColor(context.getColor(R.color.text_hint))
                holder.layoutActions.visibility = View.GONE
            }
        }
        
        // تنظیم کلیک دکمه‌ها
        if (status == "pending") {
            val currentNotification = notifications[holder.adapterPosition]
            val notificationId = currentNotification["id"] as? String ?: ""

            holder.btnApprove.setOnClickListener {
                actionListener?.onApprove(notificationId, holder.adapterPosition)
            }

            holder.btnReject.setOnClickListener {
                actionListener?.onReject(notificationId, holder.adapterPosition)
            }
        }
    }
    
    private fun formatCurrency(amount: Double): String {
        return String.format("%,.0f تومان", amount)
    }
    
    private fun formatTime(timestamp: String): String {
        return try {
            val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
            val date = sdf.parse(timestamp)
            val now = Date()
            val diff = now.time - (date?.time ?: 0)
            
            when {
                diff < 60000 -> "همین الان"
                diff < 3600000 -> "${diff / 60000} دقیقه پیش"
                diff < 86400000 -> "${diff / 3600000} ساعت پیش"
                else -> "${diff / 86400000} روز پیش"
            }
        } catch (e: Exception) {
            "نامشخص"
        }
    }

    fun updateNotifications(newNotifications: List<Map<String, Any>>) {
        notifications.clear()
        notifications.addAll(newNotifications)
        notifyDataSetChanged()
    }
    
    fun addNotification(notification: Map<String, Any>) {
        notifications.add(0, notification)
        notifyItemInserted(0)
    }
}
