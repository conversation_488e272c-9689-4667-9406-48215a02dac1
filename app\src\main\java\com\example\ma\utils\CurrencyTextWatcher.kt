package com.example.ma.utils

import android.text.Editable
import android.text.TextWatcher
import android.widget.TextView

/**
 * TextWatcher برای نمایش real-time قیمت به حروف و فرمت کردن با جداکننده
 */
class CurrencyTextWatcher(
    private val displayTextView: TextView,
    private val showWithWords: Boolean = true,
    private val formatInput: Boolean = false
) : TextWatcher {

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

    override fun afterTextChanged(s: Editable?) {
        val input = s.toString().trim()

        if (input.isEmpty()) {
            displayTextView.text = ""
            displayTextView.visibility = android.view.View.GONE
            return
        }

        try {
            // حذف کاما و سایر کاراکترهای غیرعددی
            val cleanInput = input.replace(",", "").replace("٬", "")

            if (cleanInput.isEmpty() || !cleanInput.all { it.isDigit() }) {
                displayTextView.text = ""
                displayTextView.visibility = android.view.View.GONE
                return
            }

            val amount = cleanInput.toLong()

            val displayText = if (showWithWords) {
                CurrencyFormatter.toWordsOnly(amount)
            } else {
                CurrencyFormatter.formatToToman(amount.toDouble())
            }

            displayTextView.text = displayText
            displayTextView.visibility = android.view.View.VISIBLE

        } catch (e: NumberFormatException) {
            displayTextView.text = ""
            displayTextView.visibility = android.view.View.GONE
        } catch (e: Exception) {
            displayTextView.text = ""
            displayTextView.visibility = android.view.View.GONE
        }
    }
}
