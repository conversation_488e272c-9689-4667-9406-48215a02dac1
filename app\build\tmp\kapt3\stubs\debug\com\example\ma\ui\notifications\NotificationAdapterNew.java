package com.example.ma.ui.notifications;

/**
 * Adapter جدید برای نمایش اعلانات با طراحی کامل
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010 \n\u0002\b\u0003\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002./B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0010\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0006H\u0002J\u0018\u0010\u0010\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u0006H\u0002J\b\u0010\u0013\u001a\u00020\u0014H\u0016J\u0010\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0006H\u0002J\u0010\u0010\u0016\u001a\u00020\u00062\u0006\u0010\u0017\u001a\u00020\u0006H\u0002J\u0010\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u0006H\u0002J\u0018\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0012\u001a\u00020\u0006H\u0002J\u0018\u0010\u001d\u001a\u00020\u001a2\u0006\u0010\u001e\u001a\u00020\u00022\u0006\u0010\u001f\u001a\u00020\u0014H\u0016J\u0018\u0010 \u001a\u00020\u00022\u0006\u0010!\u001a\u00020\"2\u0006\u0010\u0005\u001a\u00020\u0014H\u0016J\u000e\u0010#\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020\tJ\u000e\u0010%\u001a\u00020\u001a2\u0006\u0010&\u001a\u00020\u0006J$\u0010\'\u001a\u00020\u001a2\u0006\u0010\u001e\u001a\u00020\u00022\u0012\u0010(\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\fH\u0002J\u0018\u0010)\u001a\u00020\u001a2\u0006\u0010\u001e\u001a\u00020\u00022\u0006\u0010*\u001a\u00020\u0006H\u0002J \u0010+\u001a\u00020\u001a2\u0018\u0010,\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\f0-R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\n\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00060"}, d2 = {"Lcom/example/ma/ui/notifications/NotificationAdapterNew;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/ma/ui/notifications/NotificationAdapterNew$NotificationViewHolder;", "context", "Landroid/content/Context;", "viewType", "", "(Landroid/content/Context;Ljava/lang/String;)V", "actionListener", "Lcom/example/ma/ui/notifications/NotificationAdapterNew$OnNotificationActionListener;", "notifications", "", "", "", "formatTime", "dateString", "getAccountText", "paymentType", "userId", "getItemCount", "", "getPaymentTypeText", "getTransactionTypeText", "type", "getUserDisplayName", "loadProfileImage", "", "imageView", "Landroid/widget/ImageView;", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "setOnNotificationActionListener", "listener", "setViewType", "newViewType", "setupActionButtons", "notification", "setupStatus", "status", "updateNotifications", "newNotifications", "", "NotificationViewHolder", "OnNotificationActionListener", "app_debug"})
public final class NotificationAdapterNew extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder> {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private java.lang.String viewType;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.util.Map<java.lang.String, java.lang.Object>> notifications = null;
    @org.jetbrains.annotations.Nullable
    private com.example.ma.ui.notifications.NotificationAdapterNew.OnNotificationActionListener actionListener;
    
    public NotificationAdapterNew(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    java.lang.String viewType) {
        super();
    }
    
    public final void setOnNotificationActionListener(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.notifications.NotificationAdapterNew.OnNotificationActionListener listener) {
    }
    
    public final void setViewType(@org.jetbrains.annotations.NotNull
    java.lang.String newViewType) {
    }
    
    public final void updateNotifications(@org.jetbrains.annotations.NotNull
    java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> newNotifications) {
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder holder, int position) {
    }
    
    @java.lang.Override
    public int getItemCount() {
        return 0;
    }
    
    private final java.lang.String getUserDisplayName(java.lang.String userId) {
        return null;
    }
    
    private final java.lang.String getTransactionTypeText(java.lang.String type) {
        return null;
    }
    
    private final java.lang.String getAccountText(java.lang.String paymentType, java.lang.String userId) {
        return null;
    }
    
    private final java.lang.String getPaymentTypeText(java.lang.String paymentType) {
        return null;
    }
    
    /**
     * بارگذاری عکس پروفایل
     */
    private final void loadProfileImage(android.widget.ImageView imageView, java.lang.String userId) {
    }
    
    private final void setupStatus(com.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder holder, java.lang.String status) {
    }
    
    private final void setupActionButtons(com.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder holder, java.util.Map<java.lang.String, ? extends java.lang.Object> notification) {
    }
    
    private final java.lang.String formatTime(java.lang.String dateString) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0017\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0007R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0007R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0007R\u0011\u0010\u0012\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0007R\u0011\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0007R\u0011\u0010\u0016\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0007R\u0011\u0010\u0018\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u001c\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001bR\u0011\u0010\u001e\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001bR\u0011\u0010 \u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001bR\u0011\u0010\"\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001bR\u0011\u0010$\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001bR\u0011\u0010&\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001bR\u0011\u0010(\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001bR\u0011\u0010*\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u001bR\u0011\u0010,\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\u001bR\u0011\u0010.\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u001b\u00a8\u00060"}, d2 = {"Lcom/example/ma/ui/notifications/NotificationAdapterNew$NotificationViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "btnApprove", "getBtnApprove", "()Landroid/view/View;", "btnReject", "getBtnReject", "cardStatus", "getCardStatus", "ivProfilePicture", "Landroid/widget/ImageView;", "getIvProfilePicture", "()Landroid/widget/ImageView;", "layoutAccount", "getLayoutAccount", "layoutActions", "getLayoutActions", "layoutDescription", "getLayoutDescription", "layoutQuantity", "getLayoutQuantity", "tvAccount", "Landroid/widget/TextView;", "getTvAccount", "()Landroid/widget/TextView;", "tvAccountLabel", "getTvAccountLabel", "tvAmount", "getTvAmount", "tvDate", "getTvDate", "tvDescription", "getTvDescription", "tvDescriptionLabel", "getTvDescriptionLabel", "tvQuantity", "getTvQuantity", "tvQuantityLabel", "getTvQuantityLabel", "tvSenderName", "getTvSenderName", "tvStatus", "getTvStatus", "tvTransactionType", "getTvTransactionType", "app_debug"})
    public static final class NotificationViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull
        private final android.widget.ImageView ivProfilePicture = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvSenderName = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View cardStatus = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvStatus = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvTransactionType = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvAmount = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View layoutQuantity = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvQuantityLabel = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvQuantity = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View layoutAccount = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvAccountLabel = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvAccount = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View layoutDescription = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvDescriptionLabel = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvDescription = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView tvDate = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View layoutActions = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View btnApprove = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View btnReject = null;
        
        public NotificationViewHolder(@org.jetbrains.annotations.NotNull
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.ImageView getIvProfilePicture() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvSenderName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getCardStatus() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvStatus() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvTransactionType() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvAmount() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getLayoutQuantity() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvQuantityLabel() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvQuantity() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getLayoutAccount() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvAccountLabel() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvAccount() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getLayoutDescription() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvDescriptionLabel() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvDescription() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getTvDate() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getLayoutActions() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getBtnApprove() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getBtnReject() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0007"}, d2 = {"Lcom/example/ma/ui/notifications/NotificationAdapterNew$OnNotificationActionListener;", "", "onApprove", "", "notificationId", "", "onReject", "app_debug"})
    public static abstract interface OnNotificationActionListener {
        
        public abstract void onApprove(@org.jetbrains.annotations.NotNull
        java.lang.String notificationId);
        
        public abstract void onReject(@org.jetbrains.annotations.NotNull
        java.lang.String notificationId);
    }
}