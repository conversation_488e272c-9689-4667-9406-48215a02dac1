package com.example.ma.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import com.example.ma.R
import com.example.ma.utils.ThemeManager
import com.google.android.material.button.MaterialButton

/**
 * Dialog برای انتخاب تم اپلیکیشن
 */
class ThemeSelectionDialog : DialogFragment() {

    private lateinit var rgThemeMode: RadioGroup
    private lateinit var rbLightTheme: RadioButton
    private lateinit var rbDarkTheme: RadioButton
    private lateinit var rbSystemTheme: RadioButton

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val view = layoutInflater.inflate(R.layout.dialog_theme_selection, null)
        
        setupViews(view)
        loadCurrentTheme()
        
        return AlertDialog.Builder(requireContext())
            .setTitle("🎨 انتخاب تم")
            .setView(view)
            .setPositiveButton("اعمال") { _, _ ->
                applySelectedTheme()
            }
            .setNegativeButton("لغو", null)
            .create()
    }

    private fun setupViews(view: android.view.View) {
        rgThemeMode = view.findViewById(R.id.rgThemeMode)
        rbLightTheme = view.findViewById(R.id.rbLightTheme)
        rbDarkTheme = view.findViewById(R.id.rbDarkTheme)
        rbSystemTheme = view.findViewById(R.id.rbSystemTheme)
    }

    private fun loadCurrentTheme() {
        val currentTheme = ThemeManager.getCurrentTheme(requireContext())

        // پاک کردن انتخاب‌های قبلی
        rgThemeMode.clearCheck()

        // انتخاب تم فعلی
        when (currentTheme) {
            ThemeManager.THEME_LIGHT -> {
                rbLightTheme.isChecked = true
                rgThemeMode.check(R.id.rbLightTheme)
            }
            ThemeManager.THEME_DARK -> {
                rbDarkTheme.isChecked = true
                rgThemeMode.check(R.id.rbDarkTheme)
            }
            ThemeManager.THEME_SYSTEM -> {
                rbSystemTheme.isChecked = true
                rgThemeMode.check(R.id.rbSystemTheme)
            }
        }
    }

    private fun applySelectedTheme() {
        val checkedId = rgThemeMode.checkedRadioButtonId

        val selectedTheme = when (checkedId) {
            R.id.rbLightTheme -> ThemeManager.THEME_LIGHT
            R.id.rbDarkTheme -> ThemeManager.THEME_DARK
            R.id.rbSystemTheme -> ThemeManager.THEME_SYSTEM
            else -> {
                // اگر هیچ گزینه‌ای انتخاب نشده، تم سیستم را انتخاب کن
                Toast.makeText(requireContext(), "لطفاً یک گزینه انتخاب کنید", Toast.LENGTH_SHORT).show()
                return
            }
        }

        val currentTheme = ThemeManager.getCurrentTheme(requireContext())

        // ذخیره و اعمال تم (حتی اگر تغییری نکرده باشد)
        ThemeManager.setTheme(requireContext(), selectedTheme)

        val themeName = ThemeManager.getThemeName(requireContext(), selectedTheme)

        if (selectedTheme != currentTheme) {
            Toast.makeText(requireContext(), "تم تغییر کرد: $themeName", Toast.LENGTH_SHORT).show()

            // بستن dialog
            dismiss()

            // اعمال فوری تم
            requireActivity().recreate()
        } else {
            Toast.makeText(requireContext(), "تم فعلی: $themeName", Toast.LENGTH_SHORT).show()
            dismiss()
        }
    }

    companion object {
        fun newInstance(): ThemeSelectionDialog {
            return ThemeSelectionDialog()
        }
    }
}
