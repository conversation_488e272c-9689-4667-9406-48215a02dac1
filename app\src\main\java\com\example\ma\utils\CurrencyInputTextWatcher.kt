package com.example.ma.utils

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText

/**
 * TextWatcher برای فرمت کردن ورودی با جداکننده ۳ رقمی
 */
class CurrencyInputTextWatcher(
    private val editText: EditText
) : TextWatcher {

    private var isFormatting = false

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

    override fun afterTextChanged(s: Editable?) {
        if (isFormatting) return
        if (s == null) return

        isFormatting = true

        try {
            val currentText = s.toString()
            val input = currentText.replace(",", "").replace("٬", "")

            if (input.isNotEmpty()) {
                // بررسی که فقط عدد باشد
                if (input.all { it.isDigit() }) {
                    try {
                        val number = input.toLong()
                        val formatted = CurrencyFormatter.formatWithoutUnit(number)

                        // فقط اگر متن تغییر کرده باشد، فرمت کن
                        if (currentText != formatted) {
                            val cursorPosition = editText.selectionStart

                            // جایگزینی محتوا
                            s.replace(0, s.length, formatted)

                            // محاسبه موقعیت جدید cursor
                            val commaCount = formatted.count { it == ',' }
                            val originalCommaCount = currentText.count { it == ',' }
                            val commaDiff = commaCount - originalCommaCount

                            val newPosition = (cursorPosition + commaDiff).coerceIn(0, formatted.length)

                            // تنظیم cursor
                            editText.post {
                                try {
                                    if (newPosition <= editText.text?.length ?: 0) {
                                        editText.setSelection(newPosition)
                                    }
                                } catch (e: Exception) {
                                    // در صورت خطا، cursor را در انتها قرار می‌دهیم
                                    try {
                                        editText.setSelection(editText.text?.length ?: 0)
                                    } catch (e2: Exception) {
                                        // نادیده بگیر
                                    }
                                }
                            }
                        }
                    } catch (e: NumberFormatException) {
                        // عدد خیلی بزرگ است، متن را تغییر نمی‌دهیم
                    }
                } else {
                    // اگر کاراکتر غیرعددی وجود دارد، آن را حذف کن
                    val cleanInput = input.filter { it.isDigit() }
                    if (cleanInput != input) {
                        s.replace(0, s.length, cleanInput)
                        editText.post {
                            try {
                                editText.setSelection(cleanInput.length)
                            } catch (e: Exception) {
                                // نادیده بگیر
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // سایر خطاها را نادیده می‌گیریم
        } finally {
            isFormatting = false
        }
    }
}
