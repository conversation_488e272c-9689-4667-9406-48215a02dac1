package com.example.ma.data.remote;

/**
 * کلاینت Supabase ساده برای ارتباط با API
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010$\n\u0002\b\u0006\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0016\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\u0012\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0019\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\'\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00110\u00152\u0006\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0018J!\u0010\u0019\u001a\u00020\u00112\u0006\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u0004H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0018J!\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u0004H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0018J/\u0010\u001b\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u001c\u001a\u00020\u00042\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001eH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001fJ\u0010\u0010 \u001a\u00020\u00042\u0006\u0010!\u001a\u00020\u0004H\u0002J9\u0010\"\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\u00042\u0006\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020\u00042\u0006\u0010\'\u001a\u00020\u00042\u0006\u0010(\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010)J9\u0010*\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\u00042\u0006\u0010+\u001a\u00020\u00042\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020-2\u0006\u0010(\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010/J9\u00100\u001a\u00020\u00112\u0006\u00101\u001a\u00020\u00042\u0006\u00102\u001a\u00020\u00042\u0006\u00103\u001a\u00020\u00042\u0006\u0010$\u001a\u00020%2\u0006\u0010(\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00104J9\u00105\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\u00042\u0006\u0010$\u001a\u00020%2\u0006\u0010,\u001a\u00020-2\u0006\u00106\u001a\u00020\u00042\u0006\u0010(\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00107J!\u00108\u001a\u00020\u00112\u0006\u00109\u001a\u00020\u00042\u0006\u0010:\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0018J1\u0010;\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\u00042\u0006\u0010$\u001a\u00020%2\u0006\u0010<\u001a\u00020\u00042\u0006\u0010(\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010=J9\u0010>\u001a\b\u0012\u0004\u0012\u0002H?0\u0015\"\u0004\b\u0000\u0010?2\u0006\u0010@\u001a\u00020A2\u0012\u0010B\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u0002H?0CH\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010DJ$\u0010E\u001a\u00020F2\u0006\u0010G\u001a\u00020\u00042\u0014\u0010H\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0012\u0004\u0012\u00020F0CJ\u001d\u0010I\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020%0\u001eH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010JJ#\u0010K\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001e0LH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010JJ\b\u0010M\u001a\u00020\u0007H\u0002J\u0011\u0010N\u001a\u00020-H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010JJC\u0010O\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001e0L2\u0006\u0010#\u001a\u00020\u00042\n\b\u0002\u0010P\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010Q\u001a\u0004\u0018\u00010\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010RJG\u0010S\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001e0L2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010Q\u001a\u0004\u0018\u00010\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010RJ\'\u0010T\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u001e2\u0006\u0010\u0012\u001a\u00020\u0004H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J%\u0010U\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020-0\u001e2\u0006\u0010#\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013JC\u0010V\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001e0L2\u0006\u0010#\u001a\u00020\u00042\n\b\u0002\u0010P\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010Q\u001a\u0004\u0018\u00010\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010RJ\u0019\u0010W\u001a\u00020%2\u0006\u0010#\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\u0019\u0010X\u001a\u00020%2\u0006\u0010#\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\u0019\u0010Y\u001a\u00020%2\u0006\u0010#\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013JC\u0010Z\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001e0L2\u0006\u0010#\u001a\u00020\u00042\n\b\u0002\u0010P\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010Q\u001a\u0004\u0018\u00010\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010RJ\u0011\u0010[\u001a\u00020%H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010JJ\u0011\u0010\\\u001a\u00020%H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010JJ\u0011\u0010]\u001a\u00020%H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010JJ\'\u0010^\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u001e2\u0006\u0010#\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\'\u0010_\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u001e2\u0006\u0010\u0016\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\u001b\u0010`\u001a\u0004\u0018\u00010\u00042\u0006\u0010#\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J:\u0010a\u001a\b\u0012\u0004\u0012\u0002H?0\u0015\"\u0004\b\u0000\u0010?2\u0006\u0010b\u001a\u00020c2\b\u0010d\u001a\u0004\u0018\u00010\u00042\u0012\u0010B\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u0002H?0CH\u0002J\u000e\u0010e\u001a\u00020F2\u0006\u0010f\u001a\u00020\u0007J=\u0010g\u001a\u00020\u00112\u0006\u00101\u001a\u00020\u00042\u0006\u00102\u001a\u00020\u00042\u0006\u0010&\u001a\u00020\u00042\u0012\u0010h\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001eH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010iJ%\u0010j\u001a\u00020\u00112\u0012\u0010k\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001eH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010lJ\u0019\u0010m\u001a\u00020\u00112\u0006\u0010n\u001a\u00020oH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010pJ*\u0010q\u001a\u00020F2\u0006\u0010G\u001a\u00020\u00042\u0006\u0010h\u001a\u00020\u00012\u0012\u0010H\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020F0CJ\u0019\u0010r\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J9\u0010s\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\u00042\u0006\u0010t\u001a\u00020%2\u0006\u0010u\u001a\u00020%2\u0006\u0010v\u001a\u00020%2\u0006\u0010w\u001a\u00020%H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010xJ\u0011\u0010y\u001a\u00020\u0011H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010JJ!\u0010z\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00042\u0006\u0010Q\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0018J1\u0010{\u001a\u00020\u00112\u0006\u00109\u001a\u00020\u00042\u0006\u0010#\u001a\u00020\u00042\u0006\u0010$\u001a\u00020%2\u0006\u0010Q\u001a\u00020\u0004H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010|J*\u0010}\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\u00042\u0006\u0010~\u001a\u00020\u00042\u0006\u0010\u007f\u001a\u00020\u0001H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0003\u0010\u0080\u0001J#\u0010\u0081\u0001\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\u00042\u0007\u0010\u0082\u0001\u001a\u00020\u0004H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0018J#\u0010\u0083\u0001\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\u00042\u0007\u0010\u0084\u0001\u001a\u00020\u0004H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0018J\'\u0010\u0085\u0001\u001a\u0004\u0018\u00010\u00042\u0006\u0010#\u001a\u00020\u00042\b\u0010\u0086\u0001\u001a\u00030\u0087\u0001H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0003\u0010\u0088\u0001R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\b\u001a\n \n*\u0004\u0018\u00010\t0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n \n*\u0004\u0018\u00010\t0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u0089\u0001"}, d2 = {"Lcom/example/ma/data/remote/SupabaseClient;", "", "()V", "SUPABASE_ANON_KEY", "", "SUPABASE_URL", "applicationContext", "Landroid/content/Context;", "arrayMapType", "Ljava/lang/reflect/Type;", "kotlin.jvm.PlatformType", "client", "Lokhttp3/OkHttpClient;", "gson", "Lcom/google/gson/Gson;", "mapType", "approveNotification", "", "notificationId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "authenticateUser", "Lcom/example/ma/data/model/ApiResult;", "username", "password", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "authenticateUserDirect", "authenticateUserDirectResult", "callFunction", "functionName", "params", "", "(Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "convertPersianDateToEnglish", "persianDate", "createExpense", "userId", "amount", "", "type", "category", "description", "(Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInventoryChange", "changeType", "quantity", "", "currentStock", "(Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createNotification", "fromUserId", "toUserId", "transactionType", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;DLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createSale", "paymentType", "(Ljava/lang/String;DILjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createTable", "tableName", "schema", "createWithdrawal", "withdrawalType", "(Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "executeRequest", "T", "request", "Lokhttp3/Request;", "parser", "Lkotlin/Function1;", "(Lokhttp3/Request;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "get", "", "endpoint", "callback", "getAccountBalances", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllTransactions", "", "getApplicationContext", "getCurrentStock", "getFilteredNotifications", "filter", "status", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFilteredTransactions", "getNotificationById", "getNotificationCounts", "getNotifications", "getPersonalExpenses", "getPersonalSales", "getPersonalWithdrawals", "getSentNotifications", "getTotalExpenses", "getTotalSales", "getTotalWithdrawals", "getUser", "getUserInfo", "getUserProfileImage", "handleHttpResponse", "response", "Lokhttp3/Response;", "responseBody", "initialize", "context", "insertNotification", "data", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertSale", "saleData", "(Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertTransaction", "transaction", "Lcom/example/ma/data/model/Transaction;", "(Lcom/example/ma/data/model/Transaction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "post", "rejectNotification", "updateAccountBalance", "profitShare", "totalSales", "totalExpensesPaid", "totalWithdrawals", "(Ljava/lang/String;DDDDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateFinancialCalculations", "updateNotificationStatus", "updateTransactionStatus", "(Ljava/lang/String;Ljava/lang/String;DLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserField", "field", "value", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserProfileImage", "imageBase64", "updateUserProfileImageUrl", "imageUrl", "uploadProfileImage", "imageData", "", "(Ljava/lang/String;[BLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class SupabaseClient {
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String SUPABASE_URL = null;
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String SUPABASE_ANON_KEY = null;
    @org.jetbrains.annotations.Nullable
    private static android.content.Context applicationContext;
    @org.jetbrains.annotations.NotNull
    private static final okhttp3.OkHttpClient client = null;
    @org.jetbrains.annotations.NotNull
    private static final com.google.gson.Gson gson = null;
    private static final java.lang.reflect.Type arrayMapType = null;
    private static final java.lang.reflect.Type mapType = null;
    @org.jetbrains.annotations.NotNull
    public static final com.example.ma.data.remote.SupabaseClient INSTANCE = null;
    
    private SupabaseClient() {
        super();
    }
    
    public final void initialize(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
    }
    
    private final android.content.Context getApplicationContext() {
        return null;
    }
    
    /**
     * احراز هویت کاربر با Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object authenticateUser(@org.jetbrains.annotations.NotNull
    java.lang.String username, @org.jetbrains.annotations.NotNull
    java.lang.String password, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.ma.data.model.ApiResult<java.lang.Boolean>> $completion) {
        return null;
    }
    
    /**
     * احراز هویت مستقیم با query - نسخه جدید
     */
    private final java.lang.Object authenticateUserDirectResult(java.lang.String username, java.lang.String password, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * احراز هویت مستقیم با query
     */
    private final java.lang.Object authenticateUserDirect(java.lang.String username, java.lang.String password, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * دریافت اطلاعات کاربر از Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getUserInfo(@org.jetbrains.annotations.NotNull
    java.lang.String username, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, ? extends java.lang.Object>> $completion) {
        return null;
    }
    
    /**
     * ایجاد فروش جدید در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createSale(@org.jetbrains.annotations.NotNull
    java.lang.String userId, double amount, int quantity, @org.jetbrains.annotations.NotNull
    java.lang.String paymentType, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * ایجاد هزینه جدید در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createExpense(@org.jetbrains.annotations.NotNull
    java.lang.String userId, double amount, @org.jetbrains.annotations.NotNull
    java.lang.String type, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * ایجاد برداشت جدید در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createWithdrawal(@org.jetbrains.annotations.NotNull
    java.lang.String userId, double amount, @org.jetbrains.annotations.NotNull
    java.lang.String withdrawalType, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * ایجاد تغییر موجودی انبار در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createInventoryChange(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    java.lang.String changeType, int quantity, int currentStock, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * دریافت اعلانات کاربر از Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getNotifications(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.Nullable
    java.lang.String filter, @org.jetbrains.annotations.Nullable
    java.lang.String status, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> $completion) {
        return null;
    }
    
    /**
     * ایجاد اعلان جدید در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createNotification(@org.jetbrains.annotations.NotNull
    java.lang.String fromUserId, @org.jetbrains.annotations.NotNull
    java.lang.String toUserId, @org.jetbrains.annotations.NotNull
    java.lang.String transactionType, double amount, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * تایید اعلان در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object approveNotification(@org.jetbrains.annotations.NotNull
    java.lang.String notificationId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * رد اعلان در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object rejectNotification(@org.jetbrains.annotations.NotNull
    java.lang.String notificationId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * پردازش پاسخ HTTP و تبدیل به ApiResult
     */
    private final <T extends java.lang.Object>com.example.ma.data.model.ApiResult<T> handleHttpResponse(okhttp3.Response response, java.lang.String responseBody, kotlin.jvm.functions.Function1<? super java.lang.String, ? extends T> parser) {
        return null;
    }
    
    /**
     * اجرای درخواست HTTP با error handling کامل
     */
    private final <T extends java.lang.Object>java.lang.Object executeRequest(okhttp3.Request request, kotlin.jvm.functions.Function1<? super java.lang.String, ? extends T> parser, kotlin.coroutines.Continuation<? super com.example.ma.data.model.ApiResult<? extends T>> $completion) {
        return null;
    }
    
    /**
     * دریافت اطلاعات اعلان بر اساس ID
     */
    private final java.lang.Object getNotificationById(java.lang.String notificationId, kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, ? extends java.lang.Object>> $completion) {
        return null;
    }
    
    /**
     * بروزرسانی وضعیت تراکنش در جدول مربوطه
     */
    private final java.lang.Object updateTransactionStatus(java.lang.String tableName, java.lang.String userId, double amount, java.lang.String status, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * بروزرسانی محاسبات مالی
     */
    private final java.lang.Object updateFinancialCalculations(kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * دریافت فروش‌های شخصی کاربر
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getPersonalSales(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    /**
     * دریافت هزینه‌های شخصی کاربر از Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getPersonalExpenses(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    /**
     * دریافت برداشت‌های شخصی کاربر از Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getPersonalWithdrawals(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    /**
     * دریافت کل فروش‌ها از Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getTotalSales(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    /**
     * دریافت کل هزینه‌ها از Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getTotalExpenses(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    /**
     * دریافت کل برداشت‌ها
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getTotalWithdrawals(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    /**
     * بروزرسانی موجودی حساب در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateAccountBalance(@org.jetbrains.annotations.NotNull
    java.lang.String userId, double profitShare, double totalSales, double totalExpensesPaid, double totalWithdrawals, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * دریافت عکس پروفایل کاربر
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getUserProfileImage(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * آپلود عکس پروفایل به Supabase Storage
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object uploadProfileImage(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    byte[] imageData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * بروزرسانی URL عکس پروفایل در جدول users
     */
    private final java.lang.Object updateUserProfileImageUrl(java.lang.String userId, java.lang.String imageUrl, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * ایجاد جدول (برای راه‌اندازی اولیه)
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createTable(@org.jetbrains.annotations.NotNull
    java.lang.String tableName, @org.jetbrains.annotations.NotNull
    java.lang.String schema, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * درخواست GET ساده
     */
    public final void get(@org.jetbrains.annotations.NotNull
    java.lang.String endpoint, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * درخواست POST ساده
     */
    public final void post(@org.jetbrains.annotations.NotNull
    java.lang.String endpoint, @org.jetbrains.annotations.NotNull
    java.lang.Object data, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * فراخوانی تابع در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object callFunction(@org.jetbrains.annotations.NotNull
    java.lang.String functionName, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, ? extends java.lang.Object> params, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * دریافت اطلاعات کاربر
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getUser(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, ? extends java.lang.Object>> $completion) {
        return null;
    }
    
    /**
     * دریافت موجودی حساب‌ها
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getAccountBalances(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, java.lang.Double>> $completion) {
        return null;
    }
    
    /**
     * دریافت همه تراکنش‌ها
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getAllTransactions(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> $completion) {
        return null;
    }
    
    /**
     * دریافت تراکنش‌های فیلتر شده
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getFilteredTransactions(@org.jetbrains.annotations.Nullable
    java.lang.String userId, @org.jetbrains.annotations.Nullable
    java.lang.String type, @org.jetbrains.annotations.Nullable
    java.lang.String status, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> $completion) {
        return null;
    }
    
    /**
     * دریافت موجودی فعلی انبار
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getCurrentStock(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    /**
     * دریافت اعلانات فیلتر شده
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getFilteredNotifications(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.Nullable
    java.lang.String filter, @org.jetbrains.annotations.Nullable
    java.lang.String status, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> $completion) {
        return null;
    }
    
    /**
     * دریافت اعلانات ارسال شده توسط کاربر
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getSentNotifications(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.Nullable
    java.lang.String filter, @org.jetbrains.annotations.Nullable
    java.lang.String status, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> $completion) {
        return null;
    }
    
    /**
     * دریافت تعداد اعلانات
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getNotificationCounts(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, java.lang.Integer>> $completion) {
        return null;
    }
    
    /**
     * بروزرسانی وضعیت اعلان در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateNotificationStatus(@org.jetbrains.annotations.NotNull
    java.lang.String notificationId, @org.jetbrains.annotations.NotNull
    java.lang.String status, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * بروزرسانی فیلد کاربر در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateUserField(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    java.lang.String field, @org.jetbrains.annotations.NotNull
    java.lang.Object value, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * بروزرسانی عکس پروفایل کاربر
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateUserProfileImage(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    java.lang.String imageBase64, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * ثبت تراکنش جدید در Supabase
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertTransaction(@org.jetbrains.annotations.NotNull
    com.example.ma.data.model.Transaction transaction, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * تبدیل تاریخ فارسی به انگلیسی
     */
    private final java.lang.String convertPersianDateToEnglish(java.lang.String persianDate) {
        return null;
    }
    
    /**
     * ثبت فروش جدید در Supabase با retry
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertSale(@org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, ? extends java.lang.Object> saleData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * درج اعلان جدید
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertNotification(@org.jetbrains.annotations.NotNull
    java.lang.String fromUserId, @org.jetbrains.annotations.NotNull
    java.lang.String toUserId, @org.jetbrains.annotations.NotNull
    java.lang.String type, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, ? extends java.lang.Object> data, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}