package com.example.ma.utils

import com.example.ma.data.remote.SupabaseClient

/**
 * مدیریت محاسبات real-time شراکت
 */
object CalculationManager {

    /**
     * محاسبه سود و سهم هر شریک بر اساس فرمول شراکت
     */
    suspend fun calculatePartnershipBalances(): Map<String, Double> {
        return try {
            // مرحله 1: دریافت کل فروش‌ها و هزینه‌ها
            val totalSales = SupabaseClient.getTotalSales()
            val totalExpenses = SupabaseClient.getTotalExpenses()
            val netProfit = totalSales - totalExpenses

            // مرحله 2: محاسبه سهم مساوی
            val equalShare = netProfit / 2

            // مرحله 3: محاسبه اختلاف هزینه‌های پرداختی
            val miladExpenses = SupabaseClient.getPersonalExpenses("miladnasiri")
            val aliExpenses = SupabaseClient.getPersonalExpenses("alikakai")
            val contributionDiff = aliExpenses - miladExpenses

            // مرحله 4: تعدیل سهم‌ها
            val miladFinalShare = equalShare - contributionDiff
            val aliFinalShare = equalShare + contributionDiff

            // مرحله 5: کسر برداشت‌های شخصی
            val miladWithdrawals = SupabaseClient.getPersonalWithdrawals("miladnasiri")
            val aliWithdrawals = SupabaseClient.getPersonalWithdrawals("alikakai")

            val miladBalance = miladFinalShare - miladWithdrawals
            val aliBalance = aliFinalShare - aliWithdrawals

            mapOf(
                "total_sales" to totalSales,
                "total_expenses" to totalExpenses,
                "net_profit" to netProfit,
                "milad_expenses" to miladExpenses,
                "ali_expenses" to aliExpenses,
                "milad_withdrawals" to miladWithdrawals,
                "ali_withdrawals" to aliWithdrawals,
                "milad_final_share" to miladFinalShare,
                "ali_final_share" to aliFinalShare,
                "milad_balance" to miladBalance,
                "ali_balance" to aliBalance
            )

        } catch (e: Exception) {
            e.printStackTrace()
            emptyMap()
        }
    }

    /**
     * بروزرسانی موجودی حساب‌ها در دیتابیس
     */
    suspend fun updateAccountBalances(): Boolean {
        return try {
            val calculations = calculatePartnershipBalances()
            
            if (calculations.isNotEmpty()) {
                // بروزرسانی موجودی میلاد
                val miladSuccess = try {
                    SupabaseClient.updateAccountBalance(
                        userId = "miladnasiri",
                        profitShare = calculations["milad_balance"] ?: 0.0,
                        totalSales = calculations["total_sales"] ?: 0.0,
                        totalExpensesPaid = calculations["milad_expenses"] ?: 0.0,
                        totalWithdrawals = calculations["milad_withdrawals"] ?: 0.0
                    )
                    true
                } catch (e: Exception) {
                    false
                }

                // بروزرسانی موجودی علی
                val aliSuccess = try {
                    SupabaseClient.updateAccountBalance(
                        userId = "alikakai",
                        profitShare = calculations["ali_balance"] ?: 0.0,
                        totalSales = calculations["total_sales"] ?: 0.0,
                        totalExpensesPaid = calculations["ali_expenses"] ?: 0.0,
                        totalWithdrawals = calculations["ali_withdrawals"] ?: 0.0
                    )
                    true
                } catch (e: Exception) {
                    false
                }

                miladSuccess && aliSuccess
            } else {
                false
            }

        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }



    /**
     * بررسی صحت محاسبات
     */
    suspend fun validateCalculations(): Map<String, Any> {
        return try {
            val calculations = calculatePartnershipBalances()
            
            val totalSales = calculations["total_sales"] ?: 0.0
            val totalExpenses = calculations["total_expenses"] ?: 0.0
            val netProfit = calculations["net_profit"] ?: 0.0
            val miladBalance = calculations["milad_balance"] ?: 0.0
            val aliBalance = calculations["ali_balance"] ?: 0.0
            
            // بررسی تعادل
            val totalDistributed = miladBalance + aliBalance + 
                (calculations["milad_withdrawals"] ?: 0.0) + 
                (calculations["ali_withdrawals"] ?: 0.0)
            
            val isBalanced = Math.abs(netProfit - totalDistributed) < 0.01
            
            mapOf(
                "is_balanced" to isBalanced,
                "net_profit" to netProfit,
                "total_distributed" to totalDistributed,
                "difference" to (netProfit - totalDistributed),
                "calculations" to calculations
            )
            
        } catch (e: Exception) {
            e.printStackTrace()
            mapOf("is_balanced" to false, "error" to (e.message ?: "خطای نامشخص"))
        }
    }

    /**
     * محاسبه سود شخصی هر شریک
     */
    suspend fun calculatePersonalProfit(userId: String): Double {
        return try {
            val calculations = calculatePartnershipBalances()

            when (userId) {
                "miladnasiri" -> calculations["milad_balance"] as? Double ?: 0.0
                "alikakai" -> calculations["ali_balance"] as? Double ?: 0.0
                else -> 0.0
            }
        } catch (e: Exception) {
            e.printStackTrace()
            0.0
        }
    }
}
