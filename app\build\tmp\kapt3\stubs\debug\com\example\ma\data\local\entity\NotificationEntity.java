package com.example.ma.data.local.entity;

/**
 * Entity برای جدول اعلانات
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\'\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u007f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u000e\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0013\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0015J\t\u0010+\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010,\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u0010 J\t\u0010-\u001a\u00020\u000eH\u00c6\u0003J\t\u0010.\u001a\u00020\u0013H\u00c6\u0003J\u000b\u0010/\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u00100\u001a\u00020\u0003H\u00c6\u0003J\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\t\u00102\u001a\u00020\u0007H\u00c6\u0003J\t\u00103\u001a\u00020\tH\u00c6\u0003J\t\u00104\u001a\u00020\u0003H\u00c6\u0003J\t\u00105\u001a\u00020\fH\u00c6\u0003J\t\u00106\u001a\u00020\u000eH\u00c6\u0003J\t\u00107\u001a\u00020\u000eH\u00c6\u0003J\u0094\u0001\u00108\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u0011\u001a\u00020\u000e2\b\b\u0002\u0010\u0012\u001a\u00020\u00132\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u00109J\u0013\u0010:\u001a\u00020;2\b\u0010<\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010=\u001a\u00020>H\u00d6\u0001J\t\u0010?\u001a\u00020\u0003H\u00d6\u0001R\u0016\u0010\b\u001a\u00020\t8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0016\u0010\r\u001a\u00020\u000e8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0016\u0010\n\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0016\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001bR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001bR\u0016\u0010\u0011\u001a\u00020\u000e8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0019R\u001a\u0010\u0010\u001a\u0004\u0018\u00010\u000e8\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010!\u001a\u0004\b\u001f\u0010 R\u0018\u0010\u0014\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001bR\u0016\u0010\u000b\u001a\u00020\f8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0016\u0010\u0012\u001a\u00020\u00138\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0016\u0010\u0005\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001bR\u0016\u0010\u0006\u001a\u00020\u00078\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0016\u0010\u000f\u001a\u00020\u000e8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u0019\u00a8\u0006@"}, d2 = {"Lcom/example/ma/data/local/entity/NotificationEntity;", "", "id", "", "fromUserId", "toUserId", "transactionType", "Lcom/example/ma/data/local/entity/TransactionType;", "amount", "", "description", "status", "Lcom/example/ma/data/local/entity/NotificationStatus;", "createdAt", "", "updatedAt", "readAt", "lastSync", "syncStatus", "Lcom/example/ma/data/local/entity/SyncStatus;", "remoteId", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/example/ma/data/local/entity/TransactionType;DLjava/lang/String;Lcom/example/ma/data/local/entity/NotificationStatus;JJLjava/lang/Long;JLcom/example/ma/data/local/entity/SyncStatus;Ljava/lang/String;)V", "getAmount", "()D", "getCreatedAt", "()J", "getDescription", "()Ljava/lang/String;", "getFromUserId", "getId", "getLastSync", "getReadAt", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getRemoteId", "getStatus", "()Lcom/example/ma/data/local/entity/NotificationStatus;", "getSyncStatus", "()Lcom/example/ma/data/local/entity/SyncStatus;", "getToUserId", "getTransactionType", "()Lcom/example/ma/data/local/entity/TransactionType;", "getUpdatedAt", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/example/ma/data/local/entity/TransactionType;DLjava/lang/String;Lcom/example/ma/data/local/entity/NotificationStatus;JJLjava/lang/Long;JLcom/example/ma/data/local/entity/SyncStatus;Ljava/lang/String;)Lcom/example/ma/data/local/entity/NotificationEntity;", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
@androidx.room.Entity(tableName = "notifications", foreignKeys = {@androidx.room.ForeignKey(entity = com.example.ma.data.local.entity.UserEntity.class, parentColumns = {"id"}, childColumns = {"from_user_id"}, onDelete = 5), @androidx.room.ForeignKey(entity = com.example.ma.data.local.entity.UserEntity.class, parentColumns = {"id"}, childColumns = {"to_user_id"}, onDelete = 5)}, indices = {@androidx.room.Index(value = {"from_user_id"}), @androidx.room.Index(value = {"to_user_id"}), @androidx.room.Index(value = {"status"}), @androidx.room.Index(value = {"created_at"})})
public final class NotificationEntity {
    @androidx.room.PrimaryKey
    @androidx.room.ColumnInfo(name = "id")
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @androidx.room.ColumnInfo(name = "from_user_id")
    @org.jetbrains.annotations.NotNull
    private final java.lang.String fromUserId = null;
    @androidx.room.ColumnInfo(name = "to_user_id")
    @org.jetbrains.annotations.NotNull
    private final java.lang.String toUserId = null;
    @androidx.room.ColumnInfo(name = "transaction_type")
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.data.local.entity.TransactionType transactionType = null;
    @androidx.room.ColumnInfo(name = "amount")
    private final double amount = 0.0;
    @androidx.room.ColumnInfo(name = "description")
    @org.jetbrains.annotations.NotNull
    private final java.lang.String description = null;
    @androidx.room.ColumnInfo(name = "status")
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.data.local.entity.NotificationStatus status = null;
    @androidx.room.ColumnInfo(name = "created_at")
    private final long createdAt = 0L;
    @androidx.room.ColumnInfo(name = "updated_at")
    private final long updatedAt = 0L;
    @androidx.room.ColumnInfo(name = "read_at")
    @org.jetbrains.annotations.Nullable
    private final java.lang.Long readAt = null;
    @androidx.room.ColumnInfo(name = "last_sync")
    private final long lastSync = 0L;
    @androidx.room.ColumnInfo(name = "sync_status")
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.data.local.entity.SyncStatus syncStatus = null;
    @androidx.room.ColumnInfo(name = "remote_id")
    @org.jetbrains.annotations.Nullable
    private final java.lang.String remoteId = null;
    
    public NotificationEntity(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String fromUserId, @org.jetbrains.annotations.NotNull
    java.lang.String toUserId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionType transactionType, double amount, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.NotificationStatus status, long createdAt, long updatedAt, @org.jetbrains.annotations.Nullable
    java.lang.Long readAt, long lastSync, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.SyncStatus syncStatus, @org.jetbrains.annotations.Nullable
    java.lang.String remoteId) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFromUserId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getToUserId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionType getTransactionType() {
        return null;
    }
    
    public final double getAmount() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.NotificationStatus getStatus() {
        return null;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    public final long getUpdatedAt() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long getReadAt() {
        return null;
    }
    
    public final long getLastSync() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.SyncStatus getSyncStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getRemoteId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long component10() {
        return null;
    }
    
    public final long component11() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.SyncStatus component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionType component4() {
        return null;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.NotificationStatus component7() {
        return null;
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long component9() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.NotificationEntity copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String fromUserId, @org.jetbrains.annotations.NotNull
    java.lang.String toUserId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionType transactionType, double amount, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.NotificationStatus status, long createdAt, long updatedAt, @org.jetbrains.annotations.Nullable
    java.lang.Long readAt, long lastSync, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.SyncStatus syncStatus, @org.jetbrains.annotations.Nullable
    java.lang.String remoteId) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}