2025-07-12 16:26:08.680 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474329794, downTime=474329794, phoneEventTime=۰۲:۵۶:۰۸.۶۶۹ } moveCount:0
2025-07-12 16:26:08.766 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474329881, downTime=474329794, phoneEventTime=۰۲:۵۶:۰۸.۷۵۶ } moveCount:1
2025-07-12 16:26:08.782 29176-29176 System.out              com.example.ma                       I  🔍 Debug - Original price: '700,000'
2025-07-12 16:26:08.783 29176-29176 System.out              com.example.ma                       I  🔍 Debug - Original price bytes: 55, 48, 48, 44, 48, 48, 48
2025-07-12 16:26:08.783 29176-29176 System.out              com.example.ma                       I  🔍 Debug - Clean price: '700000'
2025-07-12 16:26:08.784 29176-29176 System.out              com.example.ma                       I  🔍 Debug - Clean price bytes: 55, 48, 48, 48, 48, 48
2025-07-12 16:26:08.786 29176-29176 System.out              com.example.ma                       I  🔍 Debug - Final price: 700000.0
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: currentUsername = Alikakai
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: شروع ثبت تراکنش
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: amount = 3500000.0
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: description = فروش 5 بطری - نقدی - علی کاکایی - توضیحات: Gfdy
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: userId = Alikakai
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: شروع
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: userId = Alikakai
2025-07-12 16:26:08.789 29176-29176 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: amount = 3500000.0
2025-07-12 16:26:08.790 29176-29176 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: ثبت تراکنش در دیتابیس
2025-07-12 16:26:08.792 29176-29213 System.out              com.example.ma                       I  🔍 TransactionRepository.insertTransaction: شروع ثبت تراکنش
2025-07-12 16:26:08.792 29176-29213 System.out              com.example.ma                       I  🔍 TransactionRepository.insertTransaction: transaction = Transaction(id=temp_1752362768789, userId=Alikakai, type=sale, amount=3500000.0, description=فروش 5 بطری - نقدی - علی کاکایی - توضیحات: Gfdy, category=فروش بطری, status=pending, createdAt=۲۰۲۵-۰۷-۱۳T۰۲:۵۶:۰۸Z, approvedBy=null, approvedAt=null)
2025-07-12 16:26:08.793 29176-29213 System.out              com.example.ma                       I  🔍 SupabaseClient.insertTransaction: شروع
2025-07-12 16:26:08.793 29176-29213 System.out              com.example.ma                       I  🔍 SupabaseClient.insertTransaction: transaction = Transaction(id=temp_1752362768789, userId=Alikakai, type=sale, amount=3500000.0, description=فروش 5 بطری - نقدی - علی کاکایی - توضیحات: Gfdy, category=فروش بطری, status=pending, createdAt=۲۰۲۵-۰۷-۱۳T۰۲:۵۶:۰۸Z, approvedBy=null, approvedAt=null)
2025-07-12 16:26:08.794 29176-29213 System.out              com.example.ma                       I  🔍 SupabaseClient.insertTransaction: jsonData = {"id":"temp_1752362768789","user_id":"Alikakai","type":"sale","amount":3500000.0,"description":"فروش 5 بطری - نقدی - علی کاکایی - توضیحات: Gfdy","category":"فروش بطری","status":"pending","created_at":"۲۰۲۵-۰۷-۱۳T۰۲:۵۶:۰۸Z"}
2025-07-12 16:26:09.234 29176-29213 System.out              com.example.ma                       I  🔍 SupabaseClient.insertTransaction: response code = 400
2025-07-12 16:26:09.234 29176-29213 System.out              com.example.ma                       I  🔍 SupabaseClient.insertTransaction: response body = {"code":"22007","details":null,"hint":null,"message":"invalid input syntax for type timestamp with time zone: \"۲۰۲۵-۰۷-۱۳T۰۲:۵۶:۰۸Z\""}
2025-07-12 16:26:09.234 29176-29213 System.out              com.example.ma                       I  🔍 SupabaseClient.insertTransaction: result = false
2025-07-12 16:26:09.234 29176-29213 System.out              com.example.ma                       I  🔍 TransactionRepository.insertTransaction: result = false
2025-07-12 16:26:09.234 29176-29176 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: insertTransaction result = false
2025-07-12 16:26:09.235 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: success = false
2025-07-12 16:26:11.890 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=*********, downTime=*********, phoneEventTime=۰۲:۵۶:۱۱.۸۸۱ } moveCount:0
2025-07-12 16:26:12.196 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474333312, downTime=*********, phoneEventTime=۰۲:۵۶:۱۲.۱۸۷ } moveCount:17
2025-07-12 16:26:14.592 29176-29213 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:14.592 29176-29213 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:26:14.593 29176-29213 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Alikakai
2025-07-12 16:26:14.594 29176-29213 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:14.594 29176-29213 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:26:14.594 29176-29213 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Miladnasiri
2025-07-12 16:26:14.594 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:14.601 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:14.640 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474335759, downTime=474335759, phoneEventTime=۰۲:۵۶:۱۴.۶۳۴ } moveCount:0
2025-07-12 16:26:14.726 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474335847, downTime=474335759, phoneEventTime=۰۲:۵۶:۱۴.۷۲۲ } moveCount:0
2025-07-12 16:26:14.840 29176-29176 ActivityThread          com.example.ma                       I  HardwareRenderer preload  done
2025-07-12 16:26:14.897 29176-29176 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:15.011 29176-29176 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:15.022 29176-29212 System.out              com.example.ma                       I  🌐 SupabaseClient.get() - درخواست GET
2025-07-12 16:26:15.022 29176-29212 System.out              com.example.ma                       I  📝 Endpoint: notifications?limit=1
2025-07-12 16:26:15.023 29176-29212 System.out              com.example.ma                       I  🔗 Full URL: https://secoqjdcrszjseedprqk.supabase.co/notifications?limit=1
2025-07-12 16:26:15.027 29176-29176 SecurityManager         com.example.ma                       D  checkAccessControl flag0
2025-07-12 16:26:15.028 29176-29176 System.out              com.example.ma                       I  🔍 NotificationActivity: Current username = Alikakai
2025-07-12 16:26:15.028 29176-29176 System.out              com.example.ma                       I  🔄 Starting real-time notification sync for user: Alikakai
2025-07-12 16:26:15.028 29176-29176 SupabaseRealtime        com.example.ma                       D  Connecting to Supabase Realtime...
2025-07-12 16:26:15.035 29176-29176 SupabaseRealtime        com.example.ma                       D  Subscribed to notifications for user: Alikakai
2025-07-12 16:26:15.121 29176-29176 libc                    com.example.ma                       W  Access denied finding property "vendor.display.enable_optimal_refresh_rate"
2025-07-12 16:26:15.119 29176-29176 com.example.ma          com.example.ma                       W  type=1400 audit(0.0:499980): avc:  denied  { read } for  name="u:object_r:vendor_display_prop:s0" dev="tmpfs" ino=550 scontext=u:r:untrusted_app:s0:c222,c257,c512,c768 tcontext=u:object_r:vendor_display_prop:s0 tclass=file permissive=0 app=com.example.ma
2025-07-12 16:26:15.122 29176-29176 libc                    com.example.ma                       W  Access denied finding property "vendor.gpp.create_frc_extension"
2025-07-12 16:26:15.171 29176-29176 VRI[Notifi...nActivity] com.example.ma                       D  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4945 android.view.ViewRootImpl.doTraversal:3556 android.view.ViewRootImpl$TraversalRunnable.run:11567 android.view.Choreographer$CallbackRecord.run:1747 android.view.Choreographer$CallbackRecord.run:1756 
2025-07-12 16:26:15.173 29176-29176 VRI[Notifi...nActivity] com.example.ma                       D  vri.Setup new sync=wmsSync-VRI[NotificationActivity]#20
2025-07-12 16:26:15.185 29176-29212 System.out              com.example.ma                       I  🌐 Response Code: 404
2025-07-12 16:26:15.185 29176-29212 System.out              com.example.ma                       I  ❌ خطا در درخواست GET: 404
2025-07-12 16:26:15.248 29176-29210 HWUI                    com.example.ma                       D  makeCurrent grContext:0xb4000073766622d0 reset mTextureAvailable
2025-07-12 16:26:15.256 29176-29176 VRI[Notifi...nActivity] com.example.ma                       D  vri.reportDrawFinished
2025-07-12 16:26:15.258 29176-29176 System.out              com.example.ma                       I  🔍 NotificationActivity: Current username = Alikakai
2025-07-12 16:26:15.260 29176-29176 System.out              com.example.ma                       I  🔍 NotificationActivity: Current username = Alikakai
2025-07-12 16:26:15.260 29176-29212 System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-12 16:26:15.260 29176-29212 System.out              com.example.ma                       I  📝 User: Alikakai, Filter: all, Status: all
2025-07-12 16:26:15.263 29176-29176 System.out              com.example.ma                       I  🔍 NotificationActivity: Current username = Alikakai
2025-07-12 16:26:15.263 29176-29176 System.out              com.example.ma                       I  🔄 Starting real-time notification sync for user: Alikakai
2025-07-12 16:26:15.263 29176-29176 SupabaseRealtime        com.example.ma                       D  Already connected or connecting
2025-07-12 16:26:15.264 29176-29176 SupabaseRealtime        com.example.ma                       D  Subscribed to notifications for user: Alikakai
2025-07-12 16:26:15.301 29176-29176 EditorStubImpl          com.example.ma                       D  Type = 2, RectF = RectF(842.0, 630.0, 952.0, 740.0), mInsertionHandleData = [HandleData{mIsShow=false, mPosition=RectF(842.0, 630.0, 952.0, 740.0)}]
2025-07-12 16:26:15.365 29176-29176 UserSceneDetector       com.example.ma                       D  invoke error.
2025-07-12 16:26:15.407 29176-29176 Looper                  com.example.ma                       W  PerfMonitor doFrame : time=42ms vsyncFrame=16406601 latency=310ms procState=-1 historyMsgCount=14 (msgIndex=1 wall=209ms seq=6592 late=211ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver) (msgIndex=13 wall=97ms seq=6604 late=220ms h=android.view.ViewRootImpl$ViewRootHandler w=6)
2025-07-12 16:26:15.415 29176-29176 HandWritingStubImpl     com.example.ma                       I  refreshLastKeyboardType: 1
2025-07-12 16:26:15.415 29176-29176 HandWritingStubImpl     com.example.ma                       I  getCurrentKeyboardType: 1
2025-07-12 16:26:15.618 29176-29210 RenderInspector         com.example.ma                       W  DequeueBuffer time out on com.example.ma/com.example.ma.MainActivity, count=1, avg=20 ms, max=20 ms.
2025-07-12 16:26:15.699 29176-29212 System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-12 16:26:15.699 29176-29212 System.out              com.example.ma                       I  📄 Response Body: []
2025-07-12 16:26:15.819 29176-29176 VRI[MainActivity]       com.example.ma                       D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-12 16:26:15.830 29176-29176 EditorStubImpl          com.example.ma                       D  Type = 2, RectF = RectF(842.0, 630.0, 952.0, 740.0), mInsertionHandleData = [HandleData{mIsShow=false, mPosition=RectF(842.0, 630.0, 952.0, 740.0)}]
2025-07-12 16:26:15.851 29176-29176 ActivityThread          com.example.ma                       D  Fail to check app heapsize due to java.lang.NoSuchMethodException: dalvik.system.VMRuntime.getBlockingGcCountForAlloc []
2025-07-12 16:26:15.941 29176-30542 SupabaseRealtime        com.example.ma                       D  WebSocket connected
2025-07-12 16:26:15.944 29176-30542 SupabaseRealtime        com.example.ma                       D  Sending message: {"topic":"realtime:public:notifications:to_user_id\u003deq.Alikakai","event":"phx_join","payload":{},"ref":"1"}
2025-07-12 16:26:15.945 29176-30542 SupabaseRealtime        com.example.ma                       D  Sending message: {"topic":"realtime:public:notifications:to_user_id\u003deq.Alikakai","event":"phx_join","payload":{},"ref":"2"}
2025-07-12 16:26:16.099 29176-30542 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":"1","event":"phx_reply","payload":{"status":"ok","response":{"postgres_changes":[{"id":104431128,"filter":"to_user_id=eq.Alikakai","schema":"public","table":"notifications"}]}},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:16.105 29176-30542 SupabaseRealtime        com.example.ma                       D  Subscription reply: {status=ok, response={postgres_changes=[{id=1.04431128E8, filter=to_user_id=eq.Alikakai, schema=public, table=notifications}]}}
2025-07-12 16:26:16.218 29176-30542 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":"2","event":"phx_reply","payload":{"status":"ok","response":{"postgres_changes":[{"id":104431128,"filter":"to_user_id=eq.Alikakai","schema":"public","table":"notifications"}]}},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:16.220 29176-30542 SupabaseRealtime        com.example.ma                       D  Subscription reply: {status=ok, response={postgres_changes=[{id=1.04431128E8, filter=to_user_id=eq.Alikakai, schema=public, table=notifications}]}}
2025-07-12 16:26:16.220 29176-30542 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"phx_close","payload":{},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:16.807 29176-30542 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"system","payload":{"message":"{:error, \"Unable to subscribe to changes with given parameters. Please check Realtime is enabled for the given connect parameters: [filter: to_user_id=eq.Alikakai, schema: public, table: notifications]\"}","status":"error","extension":"postgres_changes","channel":"public:notifications:to_user_id=eq.Alikakai"},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:16.810 29176-30542 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"presence_state","payload":{},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:20.105 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474341221, downTime=474341221, phoneEventTime=۰۲:۵۶:۲۰.۰۹۷ } moveCount:0
2025-07-12 16:26:20.153 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474341273, downTime=474341221, phoneEventTime=۰۲:۵۶:۲۰.۱۴۸ } moveCount:0
2025-07-12 16:26:21.635 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474342751, downTime=474342751, phoneEventTime=۰۲:۵۶:۲۱.۶۲۶ } moveCount:0
2025-07-12 16:26:21.722 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474342839, downTime=474342751, phoneEventTime=۰۲:۵۶:۲۱.۷۱۵ } moveCount:1
2025-07-12 16:26:21.767 29176-29176 SupabaseRealtime        com.example.ma                       D  Disconnecting from Supabase Realtime...
2025-07-12 16:26:21.809 29176-30456 ActivityThread          com.example.ma                       D  handleBoundsCompatInfoChanged remove: name=com.example.ma
2025-07-12 16:26:21.832 29176-29176 SecurityManager         com.example.ma                       D  checkAccessControl flag0
2025-07-12 16:26:21.853 29176-29176 libc                    com.example.ma                       W  Access denied finding property "vendor.display.enable_optimal_refresh_rate"
2025-07-12 16:26:21.853 29176-29176 libc                    com.example.ma                       W  Access denied finding property "vendor.gpp.create_frc_extension"
2025-07-12 16:26:21.851 29176-29176 com.example.ma          com.example.ma                       W  type=1400 audit(0.0:499989): avc:  denied  { read } for  name="u:object_r:vendor_display_prop:s0" dev="tmpfs" ino=550 scontext=u:r:untrusted_app:s0:c222,c257,c512,c768 tcontext=u:object_r:vendor_display_prop:s0 tclass=file permissive=0 app=com.example.ma
2025-07-12 16:26:21.855 29176-29176 VRI[MainActivity]       com.example.ma                       D  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4945 android.view.ViewRootImpl.doTraversal:3556 android.view.ViewRootImpl$TraversalRunnable.run:11567 android.view.Choreographer$CallbackRecord.run:1747 android.view.Choreographer$CallbackRecord.run:1756 
2025-07-12 16:26:21.856 29176-29176 VRI[MainActivity]       com.example.ma                       D  vri.Setup new sync=wmsSync-VRI[MainActivity]#22
2025-07-12 16:26:21.919 29176-30542 SupabaseRealtime        com.example.ma                       D  WebSocket closing: 1000 - 
2025-07-12 16:26:21.919 29176-30542 SupabaseRealtime        com.example.ma                       D  WebSocket closed: 1000 - 
2025-07-12 16:26:21.925 29176-29176 VRI[MainActivity]       com.example.ma                       D  vri.reportDrawFinished
2025-07-12 16:26:22.088 29176-29176 HandWritingStubImpl     com.example.ma                       I  refreshLastKeyboardType: 1
2025-07-12 16:26:22.092 29176-29176 HandWritingStubImpl     com.example.ma                       I  getCurrentKeyboardType: 1
2025-07-12 16:26:22.468 29176-29176 VRI[Notifi...nActivity] com.example.ma                       D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-12 16:26:22.485 29176-29210 HWUI                    com.example.ma                       D  endAllActiveAnimators on 0xb4000073466b6380 (MaterialCardView) with handle 0xb40000749669a280
2025-07-12 16:26:22.500 29176-29176 SupabaseRealtime        com.example.ma                       D  Disconnecting from Supabase Realtime...
2025-07-12 16:26:22.501 29176-29176 WindowOnBackDispatcher  com.example.ma                       W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda12@7ba7b31
2025-07-12 16:26:22.515 29176-29176 ActivityThread          com.example.ma                       D  Fail to check app heapsize due to java.lang.NoSuchMethodException: dalvik.system.VMRuntime.getBlockingGcCountForAlloc []
2025-07-12 16:26:23.788 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474344904, downTime=474344904, phoneEventTime=۰۲:۵۶:۲۳.۷۷۹ } moveCount:0
2025-07-12 16:26:24.356 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474345474, downTime=474344904, phoneEventTime=۰۲:۵۶:۲۴.۳۵۰ } moveCount:33
2025-07-12 16:26:24.358 29176-29176 SplineOverScroller      com.example.ma                       D  mFlingDuration: 2070,mFlingDistance: 1971.16259765625,mVelocity: 4288
2025-07-12 16:26:24.596 29176-29224 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:24.596 29176-29224 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:26:24.596 29176-29224 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Alikakai
2025-07-12 16:26:24.597 29176-29224 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:24.597 29176-29224 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:26:24.597 29176-29224 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Miladnasiri
2025-07-12 16:26:24.597 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:24.602 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:25.433 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474346551, downTime=474346551, phoneEventTime=۰۲:۵۶:۲۵.۴۲۷ } moveCount:0
2025-07-12 16:26:25.797 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474346917, downTime=474346551, phoneEventTime=۰۲:۵۶:۲۵.۷۹۳ } moveCount:22
2025-07-12 16:26:26.967 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474348085, downTime=474348085, phoneEventTime=۰۲:۵۶:۲۶.۹۶۱ } moveCount:0
2025-07-12 16:26:27.060 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474348176, downTime=474348085, phoneEventTime=۰۲:۵۶:۲۷.۰۵۱ } moveCount:1
2025-07-12 16:26:29.604 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474350718, downTime=474350718, phoneEventTime=۰۲:۵۶:۲۹.۵۹۴ } moveCount:0
2025-07-12 16:26:29.668 29176-29176 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474350788, downTime=474350718, phoneEventTime=۰۲:۵۶:۲۹.۶۶۳ } moveCount:0
2025-07-12 16:26:29.791 29176-29176 ActivityThread          com.example.ma                       I  HardwareRenderer preload  done
2025-07-12 16:26:29.847 29176-29176 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:30.006 29176-29176 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:30.022 29176-29212 System.out              com.example.ma                       I  🌐 SupabaseClient.get() - درخواست GET
2025-07-12 16:26:30.022 29176-29212 System.out              com.example.ma                       I  📝 Endpoint: notifications?limit=1
2025-07-12 16:26:30.022 29176-29212 System.out              com.example.ma                       I  🔗 Full URL: https://secoqjdcrszjseedprqk.supabase.co/notifications?limit=1
2025-07-12 16:26:30.028 29176-29176 SecurityManager         com.example.ma                       D  checkAccessControl flag0
2025-07-12 16:26:30.031 29176-29176 System.out              com.example.ma                       I  🔍 NotificationActivity: Current username = Alikakai
2025-07-12 16:26:30.031 29176-29176 System.out              com.example.ma                       I  🔄 Starting real-time notification sync for user: Alikakai
2025-07-12 16:26:30.031 29176-29176 SupabaseRealtime        com.example.ma                       D  Connecting to Supabase Realtime...
2025-07-12 16:26:30.039 29176-29176 SupabaseRealtime        com.example.ma                       D  Subscribed to notifications for user: Alikakai
2025-07-12 16:26:30.062 29176-29176 UserSceneDetector       com.example.ma                       D  invoke error.
2025-07-12 16:26:30.128 29176-29176 libc                    com.example.ma                       W  Access denied finding property "vendor.display.enable_optimal_refresh_rate"
2025-07-12 16:26:30.129 29176-29176 libc                    com.example.ma                       W  Access denied finding property "vendor.gpp.create_frc_extension"
2025-07-12 16:26:30.127 29176-29176 com.example.ma          com.example.ma                       W  type=1400 audit(0.0:499994): avc:  denied  { read } for  name="u:object_r:vendor_display_prop:s0" dev="tmpfs" ino=550 scontext=u:r:untrusted_app:s0:c222,c257,c512,c768 tcontext=u:object_r:vendor_display_prop:s0 tclass=file permissive=0 app=com.example.ma
2025-07-12 16:26:30.186 29176-29176 VRI[Notifi...nActivity] com.example.ma                       D  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4945 android.view.ViewRootImpl.doTraversal:3556 android.view.ViewRootImpl$TraversalRunnable.run:11567 android.view.Choreographer$CallbackRecord.run:1747 android.view.Choreographer$CallbackRecord.run:1756 
2025-07-12 16:26:30.186 29176-29176 VRI[Notifi...nActivity] com.example.ma                       D  vri.Setup new sync=wmsSync-VRI[NotificationActivity]#24
2025-07-12 16:26:30.189 29176-29212 System.out              com.example.ma                       I  🌐 Response Code: 404
2025-07-12 16:26:30.189 29176-29212 System.out              com.example.ma                       I  ❌ خطا در درخواست GET: 404
2025-07-12 16:26:30.257 29176-29210 HWUI                    com.example.ma                       D  makeCurrent grContext:0xb4000073766622d0 reset mTextureAvailable
2025-07-12 16:26:30.266 29176-29176 VRI[Notifi...nActivity] com.example.ma                       D  vri.reportDrawFinished
2025-07-12 16:26:30.268 29176-29176 System.out              com.example.ma                       I  🔍 NotificationActivity: Current username = Alikakai
2025-07-12 16:26:30.269 29176-29212 System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-12 16:26:30.269 29176-29212 System.out              com.example.ma                       I  📝 User: Alikakai, Filter: all, Status: all
2025-07-12 16:26:30.269 29176-29176 System.out              com.example.ma                       I  🔍 NotificationActivity: Current username = Alikakai
2025-07-12 16:26:30.270 29176-29176 System.out              com.example.ma                       I  🔍 NotificationActivity: Current username = Alikakai
2025-07-12 16:26:30.270 29176-29176 System.out              com.example.ma                       I  🔄 Starting real-time notification sync for user: Alikakai
2025-07-12 16:26:30.270 29176-29176 SupabaseRealtime        com.example.ma                       D  Already connected or connecting
2025-07-12 16:26:30.270 29176-29176 SupabaseRealtime        com.example.ma                       D  Subscribed to notifications for user: Alikakai
2025-07-12 16:26:30.318 29176-29176 EditorStubImpl          com.example.ma                       D  Type = 2, RectF = RectF(842.0, 630.0, 952.0, 740.0), mInsertionHandleData = [HandleData{mIsShow=false, mPosition=RectF(842.0, 630.0, 952.0, 740.0)}]
2025-07-12 16:26:30.386 29176-29176 UserSceneDetector       com.example.ma                       D  invoke error.
2025-07-12 16:26:30.423 29176-29176 Looper                  com.example.ma                       W  PerfMonitor doFrame : time=37ms vsyncFrame=16406951 latency=312ms procState=-1 historyMsgCount=15 (msgIndex=1 wall=205ms seq=7522 late=272ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver) (msgIndex=13 wall=112ms seq=7534 late=212ms h=android.view.ViewRootImpl$ViewRootHandler w=6)
2025-07-12 16:26:30.432 29176-29176 HandWritingStubImpl     com.example.ma                       I  refreshLastKeyboardType: 1
2025-07-12 16:26:30.433 29176-29176 HandWritingStubImpl     com.example.ma                       I  getCurrentKeyboardType: 1
2025-07-12 16:26:30.562 29176-29210 RenderInspector         com.example.ma                       W  DequeueBuffer time out on com.example.ma/com.example.ma.MainActivity, count=1, avg=18 ms, max=18 ms.
2025-07-12 16:26:30.703 29176-29212 System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-12 16:26:30.703 29176-29212 System.out              com.example.ma                       I  📄 Response Body: []
2025-07-12 16:26:30.765 29176-30571 SupabaseRealtime        com.example.ma                       D  WebSocket connected
2025-07-12 16:26:30.767 29176-30571 SupabaseRealtime        com.example.ma                       D  Sending message: {"topic":"realtime:public:notifications:to_user_id\u003deq.Alikakai","event":"phx_join","payload":{},"ref":"1"}
2025-07-12 16:26:30.768 29176-30571 SupabaseRealtime        com.example.ma                       D  Sending message: {"topic":"realtime:public:notifications:to_user_id\u003deq.Alikakai","event":"phx_join","payload":{},"ref":"2"}
2025-07-12 16:26:30.828 29176-29176 VRI[MainActivity]       com.example.ma                       D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-12 16:26:30.840 29176-29176 EditorStubImpl          com.example.ma                       D  Type = 2, RectF = RectF(842.0, 630.0, 952.0, 740.0), mInsertionHandleData = [HandleData{mIsShow=false, mPosition=RectF(842.0, 630.0, 952.0, 740.0)}]
2025-07-12 16:26:30.853 29176-29176 ActivityThread          com.example.ma                       D  Fail to check app heapsize due to java.lang.NoSuchMethodException: dalvik.system.VMRuntime.getBlockingGcCountForAlloc []
2025-07-12 16:26:30.932 29176-30571 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":"1","event":"phx_reply","payload":{"status":"ok","response":{"postgres_changes":[{"id":104431128,"filter":"to_user_id=eq.Alikakai","schema":"public","table":"notifications"}]}},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:30.933 29176-30571 SupabaseRealtime        com.example.ma                       D  Subscription reply: {status=ok, response={postgres_changes=[{id=1.04431128E8, filter=to_user_id=eq.Alikakai, schema=public, table=notifications}]}}
2025-07-12 16:26:31.064 29176-30571 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":"2","event":"phx_reply","payload":{"status":"ok","response":{"postgres_changes":[{"id":104431128,"filter":"to_user_id=eq.Alikakai","schema":"public","table":"notifications"}]}},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:31.066 29176-30571 SupabaseRealtime        com.example.ma                       D  Subscription reply: {status=ok, response={postgres_changes=[{id=1.04431128E8, filter=to_user_id=eq.Alikakai, schema=public, table=notifications}]}}
2025-07-12 16:26:31.066 29176-30571 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"phx_close","payload":{},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:31.507 29176-30571 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"system","payload":{"message":"{:error, \"Unable to subscribe to changes with given parameters. Please check Realtime is enabled for the given connect parameters: [filter: to_user_id=eq.Alikakai, schema: public, table: notifications]\"}","status":"error","extension":"postgres_changes","channel":"public:notifications:to_user_id=eq.Alikakai"},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:31.509 29176-30571 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"presence_state","payload":{},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:34.603 29176-29212 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:34.603 29176-29212 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:26:34.603 29176-29212 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Alikakai
2025-07-12 16:26:34.604 29176-29212 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:34.604 29176-29212 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:26:34.604 29176-29212 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Miladnasiri
2025-07-12 16:26:34.605 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:34.612 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:38.319 29176-30571 SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"system","payload":{"message":"{:error, \"Unable to subscribe to changes with given parameters. Please check Realtime is enabled for the given connect parameters: [filter: to_user_id=eq.Alikakai, schema: public, table: notifications]\"}","status":"error","extension":"postgres_changes","channel":"public:notifications:to_user_id=eq.Alikakai"},"topic":"realtime:public:notifications:to_user_id=eq.Alikakai"}
2025-07-12 16:26:41.213 29176-29176 SupabaseRealtime        com.example.ma                       D  Disconnecting from Supabase Realtime...
2025-07-12 16:26:41.373 29176-30571 SupabaseRealtime        com.example.ma                       D  WebSocket closing: 1000 - 
2025-07-12 16:26:41.373 29176-30571 SupabaseRealtime        com.example.ma                       D  WebSocket closed: 1000 - 
2025-07-12 16:26:42.197 29176-29176 VRI[Notifi...nActivity] com.example.ma                       D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-12 16:26:42.238 29176-29176 ActivityThread          com.example.ma                       D  Fail to check app heapsize due to java.lang.NoSuchMethodException: dalvik.system.VMRuntime.getBlockingGcCountForAlloc []
2025-07-12 16:26:44.611 29176-29213 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:44.612 29176-29213 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:26:44.613 29176-29213 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Alikakai
2025-07-12 16:26:44.616 29176-29213 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:44.616 29176-29213 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:26:44.616 29176-29213 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Miladnasiri
2025-07-12 16:26:44.617 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:44.636 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:50.119 28937-28937 ActivityThread          com.example.ma                       I  HardwareRenderer preload  done
2025-07-12 16:26:50.176 28937-30455 AppScoutStateMachine    com.example.ma                       D  28937-ScoutStateMachinecreated
2025-07-12 16:26:50.279 28937-28937 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:50.449 28937-28937 System.out              com.example.ma                       I  🔍 isLoggedIn() چک شد: true, currentUserId: 930b5d13-0408-4c57-965b-235c5532b35a
2025-07-12 16:26:50.449 28937-28937 System.out              com.example.ma                       I  🔍 LoginActivity.onCreate() - isUserLoggedIn: true
2025-07-12 16:26:50.449 28937-28937 System.out              com.example.ma                       I  🏠 کاربر قبلاً وارد شده، انتقال به MainActivity
2025-07-12 16:26:50.487 28937-28937 Looper                  com.example.ma                       W  PerfMonitor looperActivity : package=com.example.ma/.ui.auth.LoginActivity time=0ms latency=320ms  procState=-1  historyMsgCount=1 (msgIndex=1 wall=369ms seq=2213 h=android.app.ActivityThread$H w=159)
2025-07-12 16:26:50.531 28937-28937 ActivityThread          com.example.ma                       I  HardwareRenderer preload  done
2025-07-12 16:26:50.550 28937-28937 System.out              com.example.ma                       I  🔍 isLoggedIn() چک شد: true, currentUserId: 930b5d13-0408-4c57-965b-235c5532b35a
2025-07-12 16:26:50.562 28937-28937 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:50.562 28937-28937 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:50.594 28937-28937 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:50.789 28937-28937 ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-12 16:26:50.814 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity.loadProfileImageToView: imagePath = ''
2025-07-12 16:26:50.815 28937-28968 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:50.815 28937-28968 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:26:50.815 28937-28968 System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Alikakai
2025-07-12 16:26:50.816 28937-28968 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:50.816 28937-28968 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:26:50.816 28937-28968 System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Miladnasiri
2025-07-12 16:26:50.819 28937-28937 SecurityManager         com.example.ma                       D  checkAccessControl flag0
2025-07-12 16:26:50.832 28937-28937 Looper                  com.example.ma                       W  PerfMonitor looperActivity : package=com.example.ma/.MainActivity time=0ms latency=307ms  procState=-1  historyMsgCount=2 (msgIndex=2 wall=301ms seq=2228 late=32ms h=android.app.ActivityThread$H w=159)
2025-07-12 16:26:50.834 28937-28968 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:50.834 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-12 16:26:50.834 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-12 16:26:50.834 28937-28968 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:26:50.834 28937-28968 System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Miladnasiri
2025-07-12 16:26:50.889 28937-28937 libc                    com.example.ma                       W  Access denied finding property "vendor.display.enable_optimal_refresh_rate"
2025-07-12 16:26:50.889 28937-28937 libc                    com.example.ma                       W  Access denied finding property "vendor.gpp.create_frc_extension"
2025-07-12 16:26:50.887 28937-28937 com.example.ma          com.example.ma                       W  type=1400 audit(0.0:500018): avc:  denied  { read } for  name="u:object_r:vendor_display_prop:s0" dev="tmpfs" ino=550 scontext=u:r:untrusted_app:s0:c222,c257,c743,c771 tcontext=u:object_r:vendor_display_prop:s0 tclass=file permissive=0
2025-07-12 16:26:51.033 28937-28937 VRI[MainActivity]       com.example.ma                       D  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4945 android.view.ViewRootImpl.doTraversal:3556 android.view.ViewRootImpl$TraversalRunnable.run:11567 android.view.Choreographer$CallbackRecord.run:1747 android.view.Choreographer$CallbackRecord.run:1756 
2025-07-12 16:26:51.034 28937-28937 VRI[MainActivity]       com.example.ma                       D  vri.Setup new sync=wmsSync-VRI[MainActivity]#14
2025-07-12 16:26:51.094 28937-28937 VRI[MainActivity]       com.example.ma                       D  vri.reportDrawFinished
2025-07-12 16:26:51.099 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity.loadProfileImageToView: imagePath = ''
2025-07-12 16:26:51.100 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity.loadProfileImageToView: imagePath = ''
2025-07-12 16:26:51.102 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: بررسی عکس پروفایل
2025-07-12 16:26:51.102 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: hasProfileImage = false
2025-07-12 16:26:51.103 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: عکس پروفایل موجود نیست، تلاش برای بازیابی از Supabase
2025-07-12 16:26:51.103 28937-28937 System.out              com.example.ma                       I  ❌ Username فعلی پیدا نشد
2025-07-12 16:26:51.103 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: loadProfileImageFromSupabase result = false
2025-07-12 16:26:51.103 28937-28937 System.out              com.example.ma                       I  ❌ MainActivity: عکس پروفایل بازیابی نشد
2025-07-12 16:26:51.103 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity.loadProfileImageToView: imagePath = ''
2025-07-12 16:26:51.105 28937-28937 UserSceneDetector       com.example.ma                       D  invoke error.
2025-07-12 16:26:51.264 28937-28937 HandWritingStubImpl     com.example.ma                       I  refreshLastKeyboardType: 1
2025-07-12 16:26:51.264 28937-28937 HandWritingStubImpl     com.example.ma                       I  getCurrentKeyboardType: 1
2025-07-12 16:26:51.338 28937-28964 RenderInspector         com.example.ma                       W  DequeueBuffer time out on com.example.ma/com.example.ma.MainActivity, count=1, avg=25 ms, max=25 ms.
2025-07-12 16:26:54.621 29176-29213 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:54.622 29176-29213 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:26:54.622 29176-29213 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Alikakai
2025-07-12 16:26:54.624 29176-29213 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:26:54.625 29176-29213 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:26:54.625 29176-29213 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Miladnasiri
2025-07-12 16:26:54.626 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:54.647 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:26:55.036 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474376135, downTime=474376135, phoneEventTime=۰۲:۵۶:۵۵.۰۱۰ } moveCount:0
2025-07-12 16:26:55.044 28937-28937 Editor                  com.example.ma                       D  Double tap detection is fail
2025-07-12 16:26:55.068 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474376188, downTime=474376135, phoneEventTime=۰۲:۵۶:۵۵.۰۶۴ } moveCount:0
2025-07-12 16:26:55.100 28937-28937 HandWritingStubImpl     com.example.ma                       I  refreshLastKeyboardType: 1
2025-07-12 16:26:55.102 28937-28937 HandWritingStubImpl     com.example.ma                       I  getCurrentKeyboardType: 1
2025-07-12 16:26:55.110 28937-28937 ImeTracker              com.example.ma                       I  com.example.ma:1c6629b: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser true
2025-07-12 16:26:55.111 28937-28937 InputMethodManager      com.example.ma                       D  showSoftInput() view=com.google.android.material.textfield.TextInputEditText{d4ccc60 VFED..CL. .F.P..ID 0,0-838,146 #7f0800e0 app:id/etBottleCount aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
2025-07-12 16:26:55.129 28937-28937 TextViewImpl            com.example.ma                       V  sendCursorInfo support:false
2025-07-12 16:26:55.143 28937-28937 AssistStructure         com.example.ma                       I  Flattened final assist data: 6040 bytes, containing 1 windows, 43 views
2025-07-12 16:26:55.394 28937-28937 InsetsController        com.example.ma                       D  show(ime(), fromIme=true)
2025-07-12 16:26:55.405 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  requestedTypes: 8
2025-07-12 16:26:55.436 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationStart
2025-07-12 16:26:55.438 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.0
2025-07-12 16:26:55.453 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.04765599
2025-07-12 16:26:55.469 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.16040015
2025-07-12 16:26:55.486 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.29240876
2025-07-12 16:26:55.502 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.42071685
2025-07-12 16:26:55.521 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.5350897
2025-07-12 16:26:55.536 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.632136
2025-07-12 16:26:55.553 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.71193755
2025-07-12 16:26:55.569 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.77618587
2025-07-12 16:26:55.587 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.8271404
2025-07-12 16:26:55.603 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.86710876
2025-07-12 16:26:55.620 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.8982029
2025-07-12 16:26:55.636 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.9222465
2025-07-12 16:26:55.653 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.94074345
2025-07-12 16:26:55.669 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.95492005
2025-07-12 16:26:55.686 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.9657534
2025-07-12 16:26:55.707 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.97401255
2025-07-12 16:26:55.720 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.9802976
2025-07-12 16:26:55.736 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.98507345
2025-07-12 16:26:55.753 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.9886981
2025-07-12 16:26:55.770 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.99144655
2025-07-12 16:26:55.786 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.9935291
2025-07-12 16:26:55.803 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.99510604
2025-07-12 16:26:55.820 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.99629956
2025-07-12 16:26:55.837 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.9972026
2025-07-12 16:26:55.854 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.9978856
2025-07-12 16:26:55.870 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.998402
2025-07-12 16:26:55.887 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 0.9987924
2025-07-12 16:26:55.903 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationUpdate, value: 1.0
2025-07-12 16:26:55.904 28937-30663 ViewRootImplStubImpl    com.example.ma                       D  onAnimationEnd,canceled: false
2025-07-12 16:26:55.905 28937-28937 ImeTracker              com.example.ma                       I  com.example.ma:1c6629b: onShown
2025-07-12 16:26:56.095 28937-28964 RenderInspector         com.example.ma                       W  DequeueBuffer time out on com.example.ma/com.example.ma.MainActivity, count=5, avg=13 ms, max=20 ms.
2025-07-12 16:26:57.008 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474378125, downTime=474378125, phoneEventTime=۰۲:۵۶:۵۷.۰۰۱ } moveCount:0
2025-07-12 16:26:57.011 28937-28937 Editor                  com.example.ma                       D  Double tap detection is fail
2025-07-12 16:26:57.048 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474378167, downTime=474378125, phoneEventTime=۰۲:۵۶:۵۷.۰۴۳ } moveCount:0
2025-07-12 16:26:57.077 28937-28937 HandWritingStubImpl     com.example.ma                       I  refreshLastKeyboardType: 1
2025-07-12 16:26:57.080 28937-28937 HandWritingStubImpl     com.example.ma                       I  getCurrentKeyboardType: 1
2025-07-12 16:26:57.084 28937-28937 ImeTracker              com.example.ma                       I  com.example.ma:18dbe72: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser true
2025-07-12 16:26:57.085 28937-28937 InputMethodManager      com.example.ma                       D  showSoftInput() view=com.google.android.material.textfield.TextInputEditText{f4727bf VFED..CL. .F.P..ID 0,0-838,146 #7f0800e8 app:id/etPrice aid=1073741825} flags=0 reason=SHOW_SOFT_INPUT
2025-07-12 16:26:57.101 28937-28937 TextViewImpl            com.example.ma                       V  sendCursorInfo support:false
2025-07-12 16:26:57.116 28937-28937 InsetsController        com.example.ma                       D  show(ime(), fromIme=true)
2025-07-12 16:26:57.116 28937-28937 ImeTracker              com.example.ma                       I  com.example.ma:552e2709: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-12 16:26:57.120 28937-28937 InsetsController        com.example.ma                       D  show(ime(), fromIme=true)
2025-07-12 16:26:57.120 28937-28937 ImeTracker              com.example.ma                       I  com.example.ma:18dbe72: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-12 16:27:00.818 28937-28968 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:27:00.818 28937-28968 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:27:00.819 28937-28968 System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Alikakai
2025-07-12 16:27:00.819 28937-28968 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:27:00.819 28937-28968 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:27:00.819 28937-28968 System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Miladnasiri
2025-07-12 16:27:00.820 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-12 16:27:00.820 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-12 16:27:01.070 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474382183, downTime=474382183, phoneEventTime=۰۲:۵۷:۰۱.۰۵۸ } moveCount:0
2025-07-12 16:27:01.508 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474382626, downTime=474382183, phoneEventTime=۰۲:۵۷:۰۱.۵۰۲ } moveCount:25
2025-07-12 16:27:02.157 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474383275, downTime=474383275, phoneEventTime=۰۲:۵۷:۰۲.۱۵۰ } moveCount:0
2025-07-12 16:27:02.160 28937-28937 Editor                  com.example.ma                       D  Double tap detection is fail
2025-07-12 16:27:02.277 28937-28937 TextViewImpl            com.example.ma                       V  sendCursorInfo support:false
2025-07-12 16:27:02.325 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474383441, downTime=474383275, phoneEventTime=۰۲:۵۷:۰۲.۳۱۶ } moveCount:2
2025-07-12 16:27:02.344 28937-28937 HandWritingStubImpl     com.example.ma                       I  refreshLastKeyboardType: 1
2025-07-12 16:27:02.349 28937-28937 HandWritingStubImpl     com.example.ma                       I  getCurrentKeyboardType: 1
2025-07-12 16:27:02.353 28937-28937 ImeTracker              com.example.ma                       I  com.example.ma:a02f62d9: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser true
2025-07-12 16:27:02.354 28937-28937 InputMethodManager      com.example.ma                       D  showSoftInput() view=com.google.android.material.textfield.TextInputEditText{86ade8d VFED..CL. .F.P..ID 0,0-838,146 #7f0800e3 app:id/etDescription aid=1073741830} flags=0 reason=SHOW_SOFT_INPUT
2025-07-12 16:27:02.436 28937-28937 SpannableStringBuilder  com.example.ma                       E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-07-12 16:27:02.436 28937-28937 SpannableStringBuilder  com.example.ma                       E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-07-12 16:27:02.447 28937-28937 SpannableStringBuilder  com.example.ma                       E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-07-12 16:27:02.447 28937-28937 SpannableStringBuilder  com.example.ma                       E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-07-12 16:27:02.463 28937-28937 SpannableStringBuilder  com.example.ma                       E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-07-12 16:27:02.463 28937-28937 SpannableStringBuilder  com.example.ma                       E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-07-12 16:27:02.479 28937-28937 InsetsController        com.example.ma                       D  show(ime(), fromIme=true)
2025-07-12 16:27:02.480 28937-28937 ImeTracker              com.example.ma                       I  com.example.ma:f17be566: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-12 16:27:02.498 28937-28937 InsetsController        com.example.ma                       D  show(ime(), fromIme=true)
2025-07-12 16:27:02.499 28937-28937 ImeTracker              com.example.ma                       I  com.example.ma:a02f62d9: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-12 16:27:02.683 28937-28964 RenderInspector         com.example.ma                       W  QueueBuffer time out on com.example.ma/com.example.ma.MainActivity, count=3, avg=16 ms, max=21 ms.
2025-07-12 16:27:02.740 28937-28937 SpannableStringBuilder  com.example.ma                       E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-07-12 16:27:02.740 28937-28937 SpannableStringBuilder  com.example.ma                       E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-07-12 16:27:02.768 28937-28964 RenderInspector         com.example.ma                       W  DequeueBuffer time out on com.example.ma/com.example.ma.MainActivity, count=1, avg=16 ms, max=16 ms.
2025-07-12 16:27:04.301 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474385419, downTime=474385419, phoneEventTime=۰۲:۵۷:۰۴.۲۹۴ } moveCount:0
2025-07-12 16:27:04.335 28937-28937 MiInputConsumer         com.example.ma                       I  optimized resample latency: 2281149 ns
2025-07-12 16:27:04.632 29176-29213 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:27:04.632 29176-29213 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:27:04.633 29176-29213 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Alikakai
2025-07-12 16:27:04.636 29176-29213 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:27:04.637 29176-29213 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:27:04.637 29176-29213 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Miladnasiri
2025-07-12 16:27:04.638 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:27:04.654 29176-29176 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-12 16:27:04.701 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474385820, downTime=474385419, phoneEventTime=۰۲:۵۷:۰۴.۶۹۶ } moveCount:24
2025-07-12 16:27:04.703 28937-28937 SplineOverScroller      com.example.ma                       D  mFlingDuration: 1236,mFlingDistance: 308.83721923828125,mVelocity: 714
2025-07-12 16:27:05.780 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474386896, downTime=474386896, phoneEventTime=۰۲:۵۷:۰۵.۷۷۱ } moveCount:0
2025-07-12 16:27:05.825 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474386945, downTime=474386896, phoneEventTime=۰۲:۵۷:۰۵.۸۲۰ } moveCount:0
2025-07-12 16:27:05.855 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Original price: '500,000'
2025-07-12 16:27:05.856 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Original price bytes: 53, 48, 48, 44, 48, 48, 48
2025-07-12 16:27:05.856 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Clean price: '500000'
2025-07-12 16:27:05.856 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Clean price bytes: 53, 48, 48, 48, 48, 48
2025-07-12 16:27:05.856 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Final price: 500000.0
2025-07-12 16:27:05.858 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-12 16:27:05.858 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: currentUsername = null
2025-07-12 16:27:05.858 28937-28937 System.out              com.example.ma                       I  ❌ MainActivity.registerTransaction: کاربر شناسایی نشد
2025-07-12 16:27:06.073 28937-28964 RenderInspector         com.example.ma                       W  DequeueBuffer time out on com.example.ma/com.example.ma.MainActivity, count=4, avg=15 ms, max=18 ms.
2025-07-12 16:27:08.972 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=474390088, downTime=474390088, phoneEventTime=۰۲:۵۷:۰۸.۹۶۳ } moveCount:0
2025-07-12 16:27:09.059 28937-28937 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=474390178, downTime=474390088, phoneEventTime=۰۲:۵۷:۰۹.۰۵۴ } moveCount:0
2025-07-12 16:27:09.082 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Original price: '500,000'
2025-07-12 16:27:09.083 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Original price bytes: 53, 48, 48, 44, 48, 48, 48
2025-07-12 16:27:09.084 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Clean price: '500000'
2025-07-12 16:27:09.084 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Clean price bytes: 53, 48, 48, 48, 48, 48
2025-07-12 16:27:09.084 28937-28937 System.out              com.example.ma                       I  🔍 Debug - Final price: 500000.0
2025-07-12 16:27:09.092 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-12 16:27:09.092 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: currentUsername = null
2025-07-12 16:27:09.092 28937-28937 System.out              com.example.ma                       I  ❌ MainActivity.registerTransaction: کاربر شناسایی نشد
2025-07-12 16:27:10.824 28937-28968 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:27:10.825 28937-28968 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-12 16:27:10.825 28937-28968 System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Alikakai
2025-07-12 16:27:10.828 28937-28968 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-12 16:27:10.828 28937-28968 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-12 16:27:10.828 28937-28968 System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Miladnasiri
2025-07-12 16:27:10.830 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-12 16:27:10.830 28937-28937 System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
