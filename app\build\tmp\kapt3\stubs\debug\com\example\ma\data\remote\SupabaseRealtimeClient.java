package com.example.ma.data.remote;

/**
 * کلاینت Realtime برای Supabase
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u0000 ;2\u00020\u0001:\u0003:;<B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010$\u001a\u00020%J\u0006\u0010&\u001a\u00020%J\b\u0010\'\u001a\u00020\tH\u0002J\b\u0010(\u001a\u00020%H\u0002J\u0010\u0010)\u001a\u00020%2\u0006\u0010*\u001a\u00020\tH\u0002J\b\u0010+\u001a\u00020%H\u0002J\u001c\u0010,\u001a\u00020%2\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\bH\u0002J\u0010\u0010.\u001a\u00020%2\u0006\u0010/\u001a\u00020\u001fH\u0002J\b\u00100\u001a\u00020%H\u0002J\b\u00101\u001a\u00020%H\u0002J\b\u00102\u001a\u00020%H\u0002J4\u00103\u001a\u00020%2\u0006\u00104\u001a\u00020\t2$\u00105\u001a \u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\b0\u0007\u0012\u0004\u0012\u00020%06J&\u00107\u001a\u00020%2\u001e\u00105\u001a\u001a\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\b\u0012\u0004\u0012\u00020%06J\u000e\u00108\u001a\u00020%2\u0006\u00109\u001a\u00020\tR\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u0006\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\b0\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\n\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00050\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R)\u0010\u0019\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\b0\u00070\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0010R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u001f0\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010 \u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\b0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0010R\u0010\u0010\"\u001a\u0004\u0018\u00010#X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006="}, d2 = {"Lcom/example/ma/data/remote/SupabaseRealtimeClient;", "", "()V", "_connectionState", "Lkotlinx/coroutines/flow/MutableSharedFlow;", "Lcom/example/ma/data/remote/SupabaseRealtimeClient$ConnectionState;", "_notificationUpdates", "", "", "", "_transactionUpdates", "client", "Lokhttp3/OkHttpClient;", "connectionState", "Lkotlinx/coroutines/flow/SharedFlow;", "getConnectionState", "()Lkotlinx/coroutines/flow/SharedFlow;", "gson", "Lcom/google/gson/Gson;", "heartbeatJob", "Lkotlinx/coroutines/Job;", "isConnected", "", "messageRef", "", "notificationUpdates", "getNotificationUpdates", "reconnectJob", "shouldReconnect", "subscriptions", "", "Lcom/example/ma/data/remote/SupabaseRealtimeClient$ChannelSubscription;", "transactionUpdates", "getTransactionUpdates", "webSocket", "Lokhttp3/WebSocket;", "connect", "", "disconnect", "generateRef", "handleDisconnection", "handleMessage", "text", "scheduleReconnect", "sendMessage", "message", "sendSubscription", "subscription", "startHeartbeat", "stopHeartbeat", "stopReconnect", "subscribeToNotifications", "userId", "callback", "Lkotlin/Function1;", "subscribeToTransactions", "unsubscribe", "ref", "ChannelSubscription", "Companion", "ConnectionState", "app_debug"})
public final class SupabaseRealtimeClient {
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String TAG = "SupabaseRealtime";
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String REALTIME_URL = null;
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String API_KEY = null;
    private static final long HEARTBEAT_INTERVAL = 30000L;
    private static final long RECONNECT_DELAY = 5000L;
    @org.jetbrains.annotations.NotNull
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.Nullable
    private okhttp3.WebSocket webSocket;
    private boolean isConnected = false;
    private boolean shouldReconnect = true;
    @org.jetbrains.annotations.Nullable
    private kotlinx.coroutines.Job heartbeatJob;
    @org.jetbrains.annotations.Nullable
    private kotlinx.coroutines.Job reconnectJob;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableSharedFlow<java.util.List<java.util.Map<java.lang.String, java.lang.Object>>> _notificationUpdates = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.SharedFlow<java.util.List<java.util.Map<java.lang.String, java.lang.Object>>> notificationUpdates = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableSharedFlow<java.util.Map<java.lang.String, java.lang.Object>> _transactionUpdates = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.SharedFlow<java.util.Map<java.lang.String, java.lang.Object>> transactionUpdates = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableSharedFlow<com.example.ma.data.remote.SupabaseRealtimeClient.ConnectionState> _connectionState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.SharedFlow<com.example.ma.data.remote.SupabaseRealtimeClient.ConnectionState> connectionState = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Map<java.lang.String, com.example.ma.data.remote.SupabaseRealtimeClient.ChannelSubscription> subscriptions = null;
    private int messageRef = 0;
    @org.jetbrains.annotations.NotNull
    private final okhttp3.OkHttpClient client = null;
    @org.jetbrains.annotations.NotNull
    public static final com.example.ma.data.remote.SupabaseRealtimeClient.Companion Companion = null;
    
    public SupabaseRealtimeClient() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.SharedFlow<java.util.List<java.util.Map<java.lang.String, java.lang.Object>>> getNotificationUpdates() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.SharedFlow<java.util.Map<java.lang.String, java.lang.Object>> getTransactionUpdates() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.SharedFlow<com.example.ma.data.remote.SupabaseRealtimeClient.ConnectionState> getConnectionState() {
        return null;
    }
    
    /**
     * اتصال به Supabase Realtime
     */
    public final void connect() {
    }
    
    /**
     * قطع اتصال
     */
    public final void disconnect() {
    }
    
    /**
     * Subscribe به اعلانات کاربر
     */
    public final void subscribeToNotifications(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>, kotlin.Unit> callback) {
    }
    
    /**
     * Subscribe به تراکنش‌ها
     */
    public final void subscribeToTransactions(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.util.Map<java.lang.String, ? extends java.lang.Object>, kotlin.Unit> callback) {
    }
    
    /**
     * Unsubscribe از channel
     */
    public final void unsubscribe(@org.jetbrains.annotations.NotNull
    java.lang.String ref) {
    }
    
    /**
     * ارسال subscription
     */
    private final void sendSubscription(com.example.ma.data.remote.SupabaseRealtimeClient.ChannelSubscription subscription) {
    }
    
    /**
     * ارسال پیام
     */
    private final void sendMessage(java.util.Map<java.lang.String, ? extends java.lang.Object> message) {
    }
    
    /**
     * پردازش پیام دریافتی
     */
    private final void handleMessage(java.lang.String text) {
    }
    
    /**
     * مدیریت قطع اتصال
     */
    private final void handleDisconnection() {
    }
    
    /**
     * شروع heartbeat
     */
    private final void startHeartbeat() {
    }
    
    /**
     * توقف heartbeat
     */
    private final void stopHeartbeat() {
    }
    
    /**
     * برنامه‌ریزی اتصال مجدد
     */
    private final void scheduleReconnect() {
    }
    
    /**
     * توقف اتصال مجدد
     */
    private final void stopReconnect() {
    }
    
    /**
     * تولید reference منحصر به فرد
     */
    private final java.lang.String generateRef() {
        return null;
    }
    
    /**
     * اطلاعات subscription
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u001e\u0010\u0006\u001a\u001a\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\b\u0012\u0004\u0012\u00020\t0\u0007\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J!\u0010\u0014\u001a\u001a\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\b\u0012\u0004\u0012\u00020\t0\u0007H\u00c6\u0003JI\u0010\u0015\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032 \b\u0002\u0010\u0006\u001a\u001a\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\b\u0012\u0004\u0012\u00020\t0\u0007H\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0003H\u00d6\u0001R)\u0010\u0006\u001a\u001a\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\b\u0012\u0004\u0012\u00020\t0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000e\u00a8\u0006\u001c"}, d2 = {"Lcom/example/ma/data/remote/SupabaseRealtimeClient$ChannelSubscription;", "", "topic", "", "event", "ref", "callback", "Lkotlin/Function1;", "", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/jvm/functions/Function1;)V", "getCallback", "()Lkotlin/jvm/functions/Function1;", "getEvent", "()Ljava/lang/String;", "getRef", "getTopic", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class ChannelSubscription {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String topic = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String event = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String ref = null;
        @org.jetbrains.annotations.NotNull
        private final kotlin.jvm.functions.Function1<java.util.Map<java.lang.String, ? extends java.lang.Object>, kotlin.Unit> callback = null;
        
        public ChannelSubscription(@org.jetbrains.annotations.NotNull
        java.lang.String topic, @org.jetbrains.annotations.NotNull
        java.lang.String event, @org.jetbrains.annotations.NotNull
        java.lang.String ref, @org.jetbrains.annotations.NotNull
        kotlin.jvm.functions.Function1<? super java.util.Map<java.lang.String, ? extends java.lang.Object>, kotlin.Unit> callback) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getTopic() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getEvent() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getRef() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final kotlin.jvm.functions.Function1<java.util.Map<java.lang.String, ? extends java.lang.Object>, kotlin.Unit> getCallback() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final kotlin.jvm.functions.Function1<java.util.Map<java.lang.String, ? extends java.lang.Object>, kotlin.Unit> component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.remote.SupabaseRealtimeClient.ChannelSubscription copy(@org.jetbrains.annotations.NotNull
        java.lang.String topic, @org.jetbrains.annotations.NotNull
        java.lang.String event, @org.jetbrains.annotations.NotNull
        java.lang.String ref, @org.jetbrains.annotations.NotNull
        kotlin.jvm.functions.Function1<? super java.util.Map<java.lang.String, ? extends java.lang.Object>, kotlin.Unit> callback) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/ma/data/remote/SupabaseRealtimeClient$Companion;", "", "()V", "API_KEY", "", "HEARTBEAT_INTERVAL", "", "REALTIME_URL", "RECONNECT_DELAY", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * حالت‌های اتصال
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/ma/data/remote/SupabaseRealtimeClient$ConnectionState;", "", "(Ljava/lang/String;I)V", "CONNECTING", "CONNECTED", "DISCONNECTED", "ERROR", "app_debug"})
    public static enum ConnectionState {
        /*public static final*/ CONNECTING /* = new CONNECTING() */,
        /*public static final*/ CONNECTED /* = new CONNECTED() */,
        /*public static final*/ DISCONNECTED /* = new DISCONNECTED() */,
        /*public static final*/ ERROR /* = new ERROR() */;
        
        ConnectionState() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.example.ma.data.remote.SupabaseRealtimeClient.ConnectionState> getEntries() {
            return null;
        }
    }
}