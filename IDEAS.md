# 📋 ایده‌ها و خواسته‌های پروژه MA

## 👥 کاربران سیستم
- **دو شریک:** میلاد نصیری (Miladnasiri) و علی کاکایی (Alikakai)
- **احراز هویت:** نام کاربری و رمز عبور ثابت (بدون ایمیل تایید)
- **دسترسی:** مستقیم به Supabase با کلیدهای ارائه شده

## 🔄 سیستم تراکنش‌ها
### الزامات اصلی:
1. **ثبت تراکنش:** هر شریک می‌تواند تراکنش ثبت کند
2. **تایید شریک:** هر تراکنش نیاز به تایید شریک مقابل دارد
3. **اعلان‌رسانی:** ارسال اعلان به شریک مقابل برای تایید/رد
4. **نمایش:** تراکنش‌ها در بخش مخصوص نمایش داده شوند

### فیلدهای ضروری تراکنش:
- **پرداخت به:** انتخاب شریک (پیش‌فرض: خود کاربر)
- **نوع پرداخت:** نقدی/کارت به کارت
- **مبلغ و تعداد محصول**
- **توضیحات**

## 🔔 سیستم اعلانات
### الزامات:
1. **ارسال خودکار:** هنگام ثبت تراکنش
2. **نمایش دوطرفه:** برای هر دو شریک
3. **بروزرسانی لحظه‌ای:** بدون نیاز به refresh
4. **تایید/رد:** امکان پاسخ به اعلانات

## 🚨 مشکلات فعلی (نیاز به حل فوری):
1. ✅ تراکنش‌ها در Supabase ثبت میشوند
2. ✅ مشکل تاریخ فارسی حل شد
3. ✅ نام‌های کاربری در تمام کدبیس یکسان شدند
4. ✅ میلاد نصیری می‌تونه لاگین کنه
5. ✅ اعلانات inconsistent پاک شدند
6. ❌ اعلانات باید در بخش‌های مختلف نمایش داده شه:
   - برای فرستنده: "در حال انتظار" (تا تایید/رد شه)
   - برای گیرنده: "در انتظار تایید" (برای تایید/رد)
   - بعد از تایید/رد: "تایید شده" یا "رد شده"

## 📝 نکات مهم:
- **هیچ تغییری بدون اطلاع انجام نشود**
- **تمام تغییرات باید تست شوند**
- **پروژه در حال نهایی شدن است**
- **از اطلاعات واقعی استفاده می‌شود**
- **همیشه از ابزار supabase برای بررسی دیتابیس استفاده کن**

## 🔍 وضعیت فعلی Supabase (آخرین بررسی):
### کاربران:
- ✅ Alikakai (ID: ad28ba8f-0fa0-4420-8119-70fcacfd237e)
- ✅ Miladnasiri (ID: 930b5d13-0408-4c57-965b-235c5532b35a)

### اعلانات:
- ✅ 10 اعلان موجود
- ✅ اعلانات از Alikakai به Miladnasiri ارسال میشه
- ✅ اعلانات از Miladnasiri به Alikakai ارسال میشه
- ❌ بعضی اعلانات نام lowercase دارن (miladnasiri)

### تراکنش‌ها:
- ❌ مشکل تاریخ فارسی: تاریخ‌های فارسی باعث خطای 400 میشن
- ❌ جدول transactions خالیه - تراکنش‌ها ثبت نمیشن

## 🔧 مشکلات حل شده در این مرحله:
1. ✅ تبدیل تاریخ فارسی به انگلیسی در insertTransaction
2. ✅ تصحیح تمام SimpleDateFormat ها به Locale.ENGLISH
3. ✅ بهبود getCurrentUsername برای میلاد نصیری
4. ✅ یکسان‌سازی نام‌های کاربری در تمام کدبیس:
   - User.kt: "Alikakai", "Miladnasiri"
   - CalculationManager.kt: تصحیح شد
   - SupabaseClient.kt: تصحیح شد
   - TransactionsAdapter.kt: تصحیح شد
   - NotificationActivity.kt: تصحیح شد
5. ✅ پاک کردن اعلانات inconsistent از Supabase
6. ✅ تست موفق درج تراکنش با تاریخ انگلیسی

## 🔧 اولویت‌های حل مشکل:
1. **بالا:** ثبت تراکنش در Supabase
2. **بالا:** ارسال اعلانات
3. **متوسط:** بازگردانی گزینه "پرداخت به"
4. **متوسط:** نمایش تراکنش‌ها
5. **پایین:** بهبود UI/UX
