# 📋 ایده‌ها و خواسته‌های پروژه MA

## 👥 کاربران سیستم
- **دو شریک:** میلاد نصیری (Miladnasiri) و علی کاکایی (Alikakai)
- **احراز هویت:** نام کاربری و رمز عبور ثابت (بدون ایمیل تایید)
- **دسترسی:** مستقیم به Supabase با کلیدهای ارائه شده

## 🔄 سیستم تراکنش‌ها
### الزامات اصلی:
1. **ثبت تراکنش:** هر شریک می‌تواند تراکنش ثبت کند
2. **تایید شریک:** هر تراکنش نیاز به تایید شریک مقابل دارد
3. **اعلان‌رسانی:** ارسال اعلان به شریک مقابل برای تایید/رد
4. **نمایش:** تراکنش‌ها در بخش مخصوص نمایش داده شوند

### فیلدهای ضروری تراکنش:
- **پرداخت به:** انتخاب شریک (پیش‌فرض: خود کاربر)
- **نوع پرداخت:** نقدی/کارت به کارت
- **مبلغ و تعداد محصول**
- **توضیحات**

## 🔔 سیستم اعلانات
### الزامات:
1. **ارسال خودکار:** هنگام ثبت تراکنش
2. **نمایش دوطرفه:** برای هر دو شریک
3. **بروزرسانی لحظه‌ای:** بدون نیاز به refresh
4. **تایید/رد:** امکان پاسخ به اعلانات

## 🚨 مشکلات فعلی (نیاز به حل فوری):
1. ✅ تراکنش‌ها در Supabase ثبت میشوند
2. ❌ اعلانات به خود کاربر نمایش داده میشه (باید فقط برای مقصد باشه)
3. ❌ میلاد نصیری نمی‌تونه لاگین کنه ("کاربر یافت نشد")
4. ❌ اعلانات باید در بخش‌های مختلف نمایش داده شه:
   - برای فرستنده: "در حال انتظار" (تا تایید/رد شه)
   - برای گیرنده: "در انتظار تایید" (برای تایید/رد)
   - بعد از تایید/رد: "تایید شده" یا "رد شده"
5. ❌ نام‌های کاربری در کدهای مختلف inconsistent هستند

## 📝 نکات مهم:
- **هیچ تغییری بدون اطلاع انجام نشود**
- **تمام تغییرات باید تست شوند**
- **پروژه در حال نهایی شدن است**
- **از اطلاعات واقعی استفاده می‌شود**

## 🔧 اولویت‌های حل مشکل:
1. **بالا:** ثبت تراکنش در Supabase
2. **بالا:** ارسال اعلانات
3. **متوسط:** بازگردانی گزینه "پرداخت به"
4. **متوسط:** نمایش تراکنش‌ها
5. **پایین:** بهبود UI/UX
