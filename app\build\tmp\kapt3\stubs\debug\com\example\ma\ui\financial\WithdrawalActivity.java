package com.example.ma.ui.financial;

/**
 * صفحه برداشت شخصی از سهم سود
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\u0002J\n\u0010\u0014\u001a\u0004\u0018\u00010\u0011H\u0002J\b\u0010\u0015\u001a\u00020\u0016H\u0002J\u0012\u0010\u0017\u001a\u00020\u00162\b\u0010\u0018\u001a\u0004\u0018\u00010\u0019H\u0014J\b\u0010\u001a\u001a\u00020\u0016H\u0002J\b\u0010\u001b\u001a\u00020\u0016H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/example/ma/ui/financial/WithdrawalActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "btnSubmit", "Lcom/google/android/material/button/MaterialButton;", "etAmount", "Lcom/google/android/material/textfield/TextInputEditText;", "etDescription", "rbCard", "Landroid/widget/RadioButton;", "rbCash", "rgWithdrawalType", "Landroid/widget/RadioGroup;", "tvAmountInWords", "Landroid/widget/TextView;", "tvAvailableBalance", "formatCurrency", "", "amount", "", "getCurrentUserId", "loadAvailableBalance", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "setupUI", "submitWithdrawal", "app_debug"})
public final class WithdrawalActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.google.android.material.textfield.TextInputEditText etAmount;
    private com.google.android.material.textfield.TextInputEditText etDescription;
    private android.widget.RadioGroup rgWithdrawalType;
    private android.widget.RadioButton rbCard;
    private android.widget.RadioButton rbCash;
    private com.google.android.material.button.MaterialButton btnSubmit;
    private android.widget.TextView tvAvailableBalance;
    private android.widget.TextView tvAmountInWords;
    
    public WithdrawalActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void loadAvailableBalance() {
    }
    
    private final void submitWithdrawal() {
    }
    
    private final java.lang.String getCurrentUserId() {
        return null;
    }
    
    private final java.lang.String formatCurrency(double amount) {
        return null;
    }
}