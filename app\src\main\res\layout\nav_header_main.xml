<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:background="@drawable/gradient_primary"
    android:gravity="bottom"
    android:orientation="vertical"
    android:padding="24dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <!-- کارت پروفایل مدرن -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <!-- عکس پروفایل -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="60dp"
                android:layout_height="60dp"
                app:cardCornerRadius="30dp"
                app:cardElevation="4dp"
                android:layout_marginEnd="16dp">

                <ImageView
                    android:id="@+id/ivNavProfileImage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_person_modern"
                    android:background="@color/surface_color"
                    android:padding="12dp"
                    android:scaleType="centerCrop"
                    app:tint="@color/primary_color" />

            </com.google.android.material.card.MaterialCardView>

            <!-- اطلاعات کاربر -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvNavUserName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="نام کاربر"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/tvNavUserRole"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="شریک کسب‌وکار"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <!-- آیکون تنظیمات -->
            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_settings"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                app:tint="@color/primary_color"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
