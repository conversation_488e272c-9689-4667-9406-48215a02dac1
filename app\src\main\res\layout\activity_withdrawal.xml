<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:background="@color/primary_color"
            android:elevation="4dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="برداشت سرمایه"
            app:titleTextColor="@color/text_white" />

        <!-- موجودی قابل برداشت -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/success_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💎"
                    android:textSize="32sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="موجودی قابل برداشت"
                    android:textSize="16sp"
                    android:textColor="@color/text_white"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/tvAvailableBalance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0 تومان"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_white"
                    android:fontFamily="sans-serif-medium"
                    tools:text="4,500,000 تومان" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- فرم برداشت -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- عنوان -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💰 جزئیات برداشت"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="20dp"
                    android:fontFamily="sans-serif-medium" />

                <!-- مبلغ برداشت -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="مبلغ برداشت (تومان)"
                    app:startIconDrawable="@drawable/ic_money"
                    app:startIconTint="@color/primary_color"
                    app:suffixText="تومان"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif"
                        tools:text="1000000" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- نمایش مبلغ به حروف -->
                <TextView
                    android:id="@+id/tvAmountInWords"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:padding="12dp"
                    android:background="@drawable/bg_amount_display"
                    android:text=""
                    android:textSize="14sp"
                    android:textColor="@color/primary_color"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:fontFamily="sans-serif-medium"
                    android:visibility="gone"
                    tools:text="یک میلیون تومان"
                    tools:visibility="visible" />

                <!-- نوع برداشت -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💳 نوع برداشت:"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp"
                    android:fontFamily="sans-serif-medium" />

                <RadioGroup
                    android:id="@+id/rgWithdrawalType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:orientation="vertical">

                    <RadioButton
                        android:id="@+id/rbCard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="💳 از کارت شخصی"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:padding="8dp"
                        android:fontFamily="sans-serif" />

                    <RadioButton
                        android:id="@+id/rbCash"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="💵 از پول نقد"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:padding="8dp"
                        android:fontFamily="sans-serif" />

                </RadioGroup>

                <!-- توضیحات -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:hint="توضیحات برداشت"
                    app:startIconDrawable="@drawable/ic_description"
                    app:startIconTint="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etDescription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="2"
                        android:maxLines="4"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif"
                        tools:text="برداشت برای هزینه‌های شخصی" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- دکمه ثبت -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSubmit"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="ثبت برداشت"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:backgroundTint="@color/info_color"
                    app:cornerRadius="12dp"
                    app:icon="@drawable/ic_account_balance"
                    app:iconGravity="textStart"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- هشدار -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/warning_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⚠️"
                    android:textSize="24sp"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="برداشت نیاز به تایید شریک دارد و پس از تایید از سهم شما کسر می‌شود"
                    android:textSize="14sp"
                    android:textColor="@color/text_white"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
