package com.example.ma.utils

import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * تست‌های واحد برای CoroutineManager
 */
class CoroutineManagerTest {

    private lateinit var coroutineManager: CoroutineManager

    @Before
    fun setup() {
        coroutineManager = CoroutineManager()
    }

    @After
    fun tearDown() {
        coroutineManager.cleanup()
    }

    @Test
    fun launchWithLifecycle_shouldExecuteSuccessfully() = runTest {
        // Given
        var executed = false

        // When
        val job = coroutineManager.launchWithLifecycle("test_job") {
            executed = true
        }

        // Wait for completion
        job.join()

        // Then
        assertTrue(executed)
        assertTrue(job.isCompleted)
    }

    @Test
    fun launchWithLifecycle_withSameKey_shouldCancelPreviousJob() = runTest {
        // Given
        var firstJobExecuted = false
        var secondJobExecuted = false

        // When
        val firstJob = coroutineManager.launchWithLifecycle("same_key") {
            delay(100)
            firstJobExecuted = true
        }

        val secondJob = coroutineManager.launchWithLifecycle("same_key") {
            secondJobExecuted = true
        }

        // Wait for completion
        secondJob.join()

        // Then
        assertTrue(firstJob.isCancelled)
        assertFalse(firstJobExecuted)
        assertTrue(secondJobExecuted)
    }

    @Test
    fun launchIO_shouldExecuteInIOContext() = runTest {
        // Given
        var threadName = ""

        // When
        val job = coroutineManager.launchIO("io_test") {
            threadName = Thread.currentThread().name
        }

        job.join()

        // Then
        assertTrue(threadName.contains("DefaultDispatcher") || threadName.contains("IO"))
    }

    @Test
    fun cancelJob_shouldCancelSpecificJob() = runTest {
        // Given
        var executed = false

        // When
        coroutineManager.launchWithLifecycle("test_job") {
            delay(100)
            executed = true
        }

        coroutineManager.cancelJob("test_job")
        delay(150) // Wait longer than the job would take

        // Then
        assertFalse(executed)
        assertFalse(coroutineManager.isJobActive("test_job"))
    }

    @Test
    fun cancelAllJobs_shouldCancelAllActiveJobs() = runTest {
        // Given
        var job1Executed = false
        var job2Executed = false

        // When
        coroutineManager.launchWithLifecycle("job1") {
            delay(100)
            job1Executed = true
        }

        coroutineManager.launchWithLifecycle("job2") {
            delay(100)
            job2Executed = true
        }

        coroutineManager.cancelAllJobs()
        delay(150)

        // Then
        assertFalse(job1Executed)
        assertFalse(job2Executed)
        assertEquals(0, coroutineManager.getActiveJobsCount())
    }

    @Test
    fun isJobActive_shouldReturnCorrectStatus() = runTest {
        // Given
        val jobKey = "test_job"

        // When - Before launching
        assertFalse(coroutineManager.isJobActive(jobKey))

        // When - After launching
        val job = coroutineManager.launchWithLifecycle(jobKey) {
            delay(50)
        }

        assertTrue(coroutineManager.isJobActive(jobKey))

        // When - After completion
        job.join()
        assertFalse(coroutineManager.isJobActive(jobKey))
    }

    @Test
    fun getActiveJobsCount_shouldReturnCorrectCount() = runTest {
        // Given
        assertEquals(0, coroutineManager.getActiveJobsCount())

        // When
        coroutineManager.launchWithLifecycle("job1") {
            delay(100)
        }

        coroutineManager.launchWithLifecycle("job2") {
            delay(100)
        }

        // Then
        assertEquals(2, coroutineManager.getActiveJobsCount())

        // When - Cancel one job
        coroutineManager.cancelJob("job1")

        // Then
        assertEquals(1, coroutineManager.getActiveJobsCount())
    }

    @Test
    fun cleanup_shouldCancelAllJobsAndCleanup() = runTest {
        // Given
        coroutineManager.launchWithLifecycle("job1") {
            delay(100)
        }

        coroutineManager.launchWithLifecycle("job2") {
            delay(100)
        }

        assertEquals(2, coroutineManager.getActiveJobsCount())

        // When
        coroutineManager.cleanup()

        // Then
        assertEquals(0, coroutineManager.getActiveJobsCount())
    }
}

/**
 * تست‌های واحد برای DebouncedCoroutineLauncher
 */
class DebouncedCoroutineLauncherTest {

    private lateinit var coroutineManager: CoroutineManager
    private lateinit var debouncedLauncher: DebouncedCoroutineLauncher

    @Before
    fun setup() {
        coroutineManager = CoroutineManager()
        debouncedLauncher = DebouncedCoroutineLauncher(coroutineManager, 100)
    }

    @After
    fun tearDown() {
        debouncedLauncher.cancel()
        coroutineManager.cleanup()
    }

    @Test
    fun launch_shouldDebounceMultipleCalls() = runTest {
        // Given
        var executionCount = 0

        // When - Multiple rapid calls
        debouncedLauncher.launch { executionCount++ }
        debouncedLauncher.launch { executionCount++ }
        debouncedLauncher.launch { executionCount++ }

        // Wait for debounce delay + execution
        delay(150)

        // Then - Should execute only once
        assertEquals(1, executionCount)
    }

    @Test
    fun launch_afterDelay_shouldExecuteAgain() = runTest {
        // Given
        var executionCount = 0

        // When - First call
        debouncedLauncher.launch { executionCount++ }
        delay(150) // Wait for execution

        // When - Second call after delay
        debouncedLauncher.launch { executionCount++ }
        delay(150) // Wait for execution

        // Then - Should execute twice
        assertEquals(2, executionCount)
    }
}

/**
 * تست‌های واحد برای ThrottledCoroutineLauncher
 */
class ThrottledCoroutineLauncherTest {

    private lateinit var coroutineManager: CoroutineManager
    private lateinit var throttledLauncher: ThrottledCoroutineLauncher

    @Before
    fun setup() {
        coroutineManager = CoroutineManager()
        throttledLauncher = ThrottledCoroutineLauncher(coroutineManager, 100)
    }

    @After
    fun tearDown() {
        throttledLauncher.cancel()
        coroutineManager.cleanup()
    }

    @Test
    fun launch_shouldThrottleMultipleCalls() = runTest {
        // Given
        var executionCount = 0

        // When - Multiple rapid calls
        throttledLauncher.launch { executionCount++ }
        throttledLauncher.launch { executionCount++ }
        throttledLauncher.launch { executionCount++ }

        // Wait for throttle interval + execution
        delay(150)

        // Then - Should execute only twice (first immediate, last after throttle)
        assertEquals(2, executionCount)
    }
}
