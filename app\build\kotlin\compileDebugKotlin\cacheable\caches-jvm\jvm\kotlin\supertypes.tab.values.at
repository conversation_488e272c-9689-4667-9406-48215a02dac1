/ Header Record For PersistentHashMapValueStorage android.app.Application) (androidx.appcompat.app.AppCompatActivity androidx.room.RoomDatabase$ #androidx.room.RoomDatabase.Callback kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum java.lang.Exception# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError# "com.example.ma.data.model.ApiError$ #com.example.ma.data.model.ApiResult$ #com.example.ma.data.model.ApiResult$ #com.example.ma.data.model.ApiResult kotlin.Enum kotlin.Enum) (androidx.appcompat.app.AppCompatActivity# "com.example.ma.ui.auth.LoginResult# "com.example.ma.ui.auth.LoginResult# "com.example.ma.ui.auth.LoginResult$ #androidx.lifecycle.AndroidViewModel% $androidx.fragment.app.DialogFragment) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel  com.example.ma.ui.model.UiState  com.example.ma.ui.model.UiState  com.example.ma.ui.model.UiState  com.example.ma.ui.model.UiState) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity" !kotlinx.coroutines.CoroutineScope android.text.TextWatcher android.text.TextWatcher) (androidx.appcompat.app.AppCompatActivity% $androidx.fragment.app.DialogFragment) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity android.text.TextWatcher android.text.TextWatcher) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity android.text.TextWatcher) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity android.text.TextWatcher) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity android.text.TextWatcher android.text.TextWatcher) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity android.text.TextWatcher) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity android.text.TextWatcher) (androidx.appcompat.app.AppCompatActivity android.text.TextWatcher) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity% $androidx.fragment.app.DialogFragment) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity!  androidx.viewbinding.ViewBinding) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity% $androidx.fragment.app.DialogFragment) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding$ #androidx.lifecycle.AndroidViewModel2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity!  androidx.viewbinding.ViewBinding