package com.example.ma.ui.financial;

/**
 * صفحه مالی - مرکز مدیریت تراکنش‌های مالی
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\n\u0010\u0007\u001a\u0004\u0018\u00010\u0004H\u0002J\b\u0010\b\u001a\u00020\tH\u0002J\u0012\u0010\n\u001a\u00020\t2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u0014J\u0010\u0010\r\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\u0004H\u0002J\b\u0010\u000f\u001a\u00020\tH\u0002J\b\u0010\u0010\u001a\u00020\tH\u0002J\b\u0010\u0011\u001a\u00020\tH\u0002J\u001c\u0010\u0012\u001a\u00020\t2\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00150\u0014H\u0002\u00a8\u0006\u0016"}, d2 = {"Lcom/example/ma/ui/financial/FinancialActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "formatCurrency", "", "amount", "", "getCurrentUserId", "loadAccountBalances", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "openExpenseActivity", "type", "openTransactionsActivity", "openWithdrawalActivity", "setupUI", "updateBalancesUI", "balances", "", "", "app_debug"})
public final class FinancialActivity extends androidx.appcompat.app.AppCompatActivity {
    
    public FinancialActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void loadAccountBalances() {
    }
    
    private final void updateBalancesUI(java.util.Map<java.lang.String, ? extends java.lang.Object> balances) {
    }
    
    private final void openExpenseActivity(java.lang.String type) {
    }
    
    private final void openWithdrawalActivity() {
    }
    
    private final void openTransactionsActivity() {
    }
    
    private final java.lang.String getCurrentUserId() {
        return null;
    }
    
    private final java.lang.String formatCurrency(double amount) {
        return null;
    }
}