package com.example.ma.ui.financial

import android.os.Bundle
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.data.remote.SupabaseClient
import com.example.ma.utils.SimpleCurrencyTextWatcher
import com.example.ma.utils.CurrencyFormatter
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch

/**
 * صفحه برداشت شخصی از سهم سود
 */
class WithdrawalActivity : AppCompatActivity() {

    private lateinit var etAmount: TextInputEditText
    private lateinit var etDescription: TextInputEditText
    private lateinit var rgWithdrawalType: RadioGroup
    private lateinit var rbCard: RadioButton
    private lateinit var rbCash: RadioButton
    private lateinit var btnSubmit: MaterialButton
    private lateinit var tvAvailableBalance: android.widget.TextView
    private lateinit var tvAmountInWords: android.widget.TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_withdrawal)

        setupUI()
        loadAvailableBalance()
    }

    private fun setupUI() {
        etAmount = findViewById(R.id.etAmount)
        etDescription = findViewById(R.id.etDescription)
        rgWithdrawalType = findViewById(R.id.rgWithdrawalType)
        rbCard = findViewById(R.id.rbCard)
        rbCash = findViewById(R.id.rbCash)
        btnSubmit = findViewById(R.id.btnSubmit)
        tvAvailableBalance = findViewById(R.id.tvAvailableBalance)
        tvAmountInWords = findViewById(R.id.tvAmountInWords)

        // دکمه بازگشت
        findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)?.setNavigationOnClickListener {
            onBackPressed()
        }

        // انتخاب پیش‌فرض
        rbCard.isChecked = true

        // دکمه ثبت
        btnSubmit.setOnClickListener {
            submitWithdrawal()
        }

        // تنظیم TextWatcher ساده
        etAmount.addTextChangedListener(
            SimpleCurrencyTextWatcher(
                editText = etAmount,
                displayTextView = tvAmountInWords
            )
        )
    }

    private fun loadAvailableBalance() {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val balances = SupabaseClient.getAccountBalances()
                    if (balances != null) {
                        val profitShare = balances["profit_share"] as? Double ?: 0.0
                        tvAvailableBalance.text = formatCurrency(profitShare)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@WithdrawalActivity, "خطا در بارگذاری موجودی", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun submitWithdrawal() {
        val amountText = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()

        // اعتبارسنجی
        if (amountText.isEmpty()) {
            etAmount.error = "مبلغ را وارد کنید"
            return
        }

        val amount = try {
            // حذف کاما قبل از تبدیل به عدد
            val cleanAmountText = amountText.replace(",", "").replace("٬", "")
            cleanAmountText.toDouble()
        } catch (e: NumberFormatException) {
            etAmount.error = "مبلغ نامعتبر"
            return
        }

        if (amount <= 0) {
            etAmount.error = "مبلغ باید بیشتر از صفر باشد"
            return
        }

        if (description.isEmpty()) {
            etDescription.error = "توضیحات را وارد کنید"
            return
        }

        // تشخیص نوع برداشت
        val withdrawalType = when (rgWithdrawalType.checkedRadioButtonId) {
            R.id.rbCard -> "card"
            R.id.rbCash -> "cash"
            else -> "card"
        }

        // ثبت برداشت
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val success = SupabaseClient.createWithdrawal(
                        userId = currentUserId,
                        amount = amount,
                        withdrawalType = withdrawalType,
                        description = description
                    )

                    if (success) {
                        Toast.makeText(this@WithdrawalActivity, "برداشت با موفقیت ثبت شد", Toast.LENGTH_SHORT).show()
                        finish()
                    } else {
                        Toast.makeText(this@WithdrawalActivity, "خطا در ثبت برداشت", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@WithdrawalActivity, "خطا در احراز هویت", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@WithdrawalActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun getCurrentUserId(): String? {
        return try {
            val sharedPreferences = getSharedPreferences("auth_prefs", MODE_PRIVATE)
            // ابتدا تلاش برای دریافت username
            val username = sharedPreferences.getString("current_username", null)
            if (username != null) {
                println("🔍 WithdrawalActivity: Current username = $username")
                return username
            }

            // اگر username نبود، از user_id استفاده کن
            val userId = sharedPreferences.getString("current_user_id", null)
            println("🔍 WithdrawalActivity: Current user_id = $userId")
            return userId
        } catch (e: Exception) {
            println("❌ WithdrawalActivity: خطا در دریافت user ID: ${e.message}")
            null
        }
    }

    private fun formatCurrency(amount: Double): String {
        return String.format("%,.0f تومان", amount)
    }
}
