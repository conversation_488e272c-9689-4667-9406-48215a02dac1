package com.example.ma.ui.statistics

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.data.remote.SupabaseClient
import com.example.ma.ui.financial.FinancialActivity
import com.example.ma.utils.CalculationManager
import com.example.ma.utils.CurrencyFormatter
import com.google.android.material.card.MaterialCardView
import kotlinx.coroutines.launch

/**
 * صفحه آمار کامل - نمایش گزارشات مالی و آمار شخصی/کلی
 */
class StatisticsActivity : AppCompatActivity() {

    // آمار شخصی
    private lateinit var tvPersonalSales: TextView
    private lateinit var tvPersonalExpenses: TextView
    private lateinit var tvPersonalWithdrawals: TextView
    private lateinit var tvPersonalProfit: TextView

    // آمار کلی شرکت
    private lateinit var tvTotalSales: TextView
    private lateinit var tvTotalExpenses: TextView
    private lateinit var tvNetProfit: TextView
    private lateinit var tvCurrentStock: TextView

    // کارت‌های عملیات
    private lateinit var cardAddExpense: MaterialCardView
    private lateinit var cardAddWithdrawal: MaterialCardView
    private lateinit var cardManageInventory: MaterialCardView
    private lateinit var cardViewTransactions: MaterialCardView

    private var currentUserId: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_statistics)

        currentUserId = getCurrentUserId()
        
        setupUI()
        setupClickListeners()
        loadStatistics()
    }

    private fun setupUI() {
        // Toolbar
        findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)?.setNavigationOnClickListener {
            onBackPressed()
        }

        // آمار شخصی
        tvPersonalSales = findViewById(R.id.tvPersonalSales)
        tvPersonalExpenses = findViewById(R.id.tvPersonalExpenses)
        tvPersonalWithdrawals = findViewById(R.id.tvPersonalWithdrawals)
        tvPersonalProfit = findViewById(R.id.tvPersonalProfit)

        // آمار کلی
        tvTotalSales = findViewById(R.id.tvTotalSales)
        tvTotalExpenses = findViewById(R.id.tvTotalExpenses)
        tvNetProfit = findViewById(R.id.tvNetProfit)
        tvCurrentStock = findViewById(R.id.tvCurrentStock)

        // کارت‌های عملیات
        cardAddExpense = findViewById(R.id.cardAddExpense)
        cardAddWithdrawal = findViewById(R.id.cardAddWithdrawal)
        cardManageInventory = findViewById(R.id.cardManageInventory)
        cardViewTransactions = findViewById(R.id.cardViewTransactions)
    }

    private fun setupClickListeners() {
        // ثبت هزینه
        cardAddExpense.setOnClickListener {
            val intent = Intent(this, FinancialActivity::class.java)
            startActivity(intent)
        }

        // برداشت شخصی
        cardAddWithdrawal.setOnClickListener {
            val intent = Intent(this, com.example.ma.ui.financial.WithdrawalActivity::class.java)
            startActivity(intent)
        }

        // مدیریت انبار
        cardManageInventory.setOnClickListener {
            val intent = Intent(this, com.example.ma.ui.inventory.InventoryActivity::class.java)
            startActivity(intent)
        }

        // مشاهده تراکنش‌ها
        cardViewTransactions.setOnClickListener {
            val intent = Intent(this, com.example.ma.ui.financial.TransactionsActivity::class.java)
            startActivity(intent)
        }
    }

    private fun loadStatistics() {
        lifecycleScope.launch {
            try {
                if (currentUserId != null) {
                    // بارگذاری آمار شخصی
                    loadPersonalStatistics()
                    
                    // بارگذاری آمار کلی
                    loadCompanyStatistics()
                    
                    // بارگذاری موجودی انبار
                    loadInventoryStatus()

                    // بروزرسانی محاسبات در دیتابیس
                    updateCalculations()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@StatisticsActivity, "خطا در بارگذاری آمار", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private suspend fun updateCalculations() {
        try {
            val success = CalculationManager.updateAccountBalances()
            if (success) {
                println("محاسبات با موفقیت بروزرسانی شد")
            } else {
                println("خطا در بروزرسانی محاسبات")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun loadPersonalStatistics() {
        try {
            // فروش‌های شخصی
            val personalSales = SupabaseClient.getPersonalSales(currentUserId!!)
            tvPersonalSales.text = formatCurrency(personalSales)

            // هزینه‌های پرداختی
            val personalExpenses = SupabaseClient.getPersonalExpenses(currentUserId!!)
            tvPersonalExpenses.text = formatCurrency(personalExpenses)

            // برداشت‌های انجام شده
            val personalWithdrawals = SupabaseClient.getPersonalWithdrawals(currentUserId!!)
            tvPersonalWithdrawals.text = formatCurrency(personalWithdrawals)

            // محاسبه سود شخصی (بر اساس فرمول شراکت)
            val personalProfit = calculatePersonalProfit()
            tvPersonalProfit.text = formatCurrency(personalProfit)

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun loadCompanyStatistics() {
        try {
            // کل فروش‌ها
            val totalSales = SupabaseClient.getTotalSales()
            tvTotalSales.text = formatCurrency(totalSales)

            // کل هزینه‌ها
            val totalExpenses = SupabaseClient.getTotalExpenses()
            tvTotalExpenses.text = formatCurrency(totalExpenses)

            // سود خالص
            val netProfit = totalSales - totalExpenses
            tvNetProfit.text = formatCurrency(netProfit)

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun loadInventoryStatus() {
        try {
            val currentStock = SupabaseClient.getCurrentStock()
            tvCurrentStock.text = "$currentStock عدد"
        } catch (e: Exception) {
            e.printStackTrace()
            tvCurrentStock.text = "0 عدد"
        }
    }

    private suspend fun calculatePersonalProfit(): Double {
        return try {
            // استفاده از CalculationManager برای محاسبات دقیق
            CalculationManager.calculatePersonalProfit(currentUserId!!)
        } catch (e: Exception) {
            0.0
        }
    }

    private fun getCurrentUserId(): String? {
        val sharedPreferences = getSharedPreferences("auth_prefs", MODE_PRIVATE)
        return sharedPreferences.getString("current_user_id", null)
    }

    private fun formatCurrency(amount: Double): String {
        return CurrencyFormatter.formatWithWords(amount)
    }

    override fun onResume() {
        super.onResume()
        // بروزرسانی آمار هنگام بازگشت به صفحه
        loadStatistics()
    }
}
