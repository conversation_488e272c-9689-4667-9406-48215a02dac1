-- حذف جداول قدیمی اگر وجود دارند
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;

-- جدول اعلانات
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    from_user_id VARCHAR(50) NOT NULL,
    to_user_id VARCHAR(50) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
    amount DECIMAL(15,2) NOT NULL,
    product_count INTEGER DEFAULT NULL,
    description TEXT DEFAULT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    transaction_data JSONB DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

-- جدول تراکنش‌ها (برای ذخیره تراکنش‌های تایید شده)
CREATE TABLE transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    notification_id UUID REFERENCES notifications(id),
    user_id VARCHAR(50) NOT NULL,
    partner_id VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
    amount DECIMAL(15,2) NOT NULL,
    product_count INTEGER DEFAULT NULL,
    description TEXT DEFAULT NULL,
    approved_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول کاربران (اگر وجود نداره)
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(50) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    profile_image TEXT DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- اضافه کردن کاربران اصلی
INSERT INTO users (id, username, full_name, phone) VALUES 
('miladnasiri', 'Miladnasiri', 'میلاد نصیری', '09184352395'),
('alikakai', 'Alikakai', 'علی کاکایی', '09172558813')
ON CONFLICT (id) DO NOTHING;

-- ایندکس‌ها برای بهبود عملکرد
CREATE INDEX idx_notifications_to_user ON notifications(to_user_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_type ON notifications(transaction_type);
CREATE INDEX idx_notifications_created ON notifications(created_at DESC);
CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_transactions_type ON transactions(type);

-- تریگر برای بروزرسانی updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS (Row Level Security) برای امنیت
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Policy برای notifications
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (to_user_id = current_setting('app.current_user_id', true));

CREATE POLICY "Users can insert notifications" ON notifications
    FOR INSERT WITH CHECK (from_user_id = current_setting('app.current_user_id', true));

CREATE POLICY "Users can update their received notifications" ON notifications
    FOR UPDATE USING (to_user_id = current_setting('app.current_user_id', true));

-- Policy برای transactions
CREATE POLICY "Users can view their own transactions" ON transactions
    FOR SELECT USING (user_id = current_setting('app.current_user_id', true) OR partner_id = current_setting('app.current_user_id', true));

CREATE POLICY "Users can insert transactions" ON transactions
    FOR INSERT WITH CHECK (approved_by = current_setting('app.current_user_id', true));
