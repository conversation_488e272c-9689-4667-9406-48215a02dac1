/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder!  androidx.viewbinding.ViewBinding2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder