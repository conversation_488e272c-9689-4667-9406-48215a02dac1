package com.example.ma.data.local.dao

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.ma.data.local.AppDatabase
import com.example.ma.data.local.entity.UserEntity
import com.example.ma.data.local.entity.SyncStatus
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * تست‌های واحد برای UserDao
 */
@RunWith(AndroidJUnit4::class)
class UserDaoTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var database: AppDatabase
    private lateinit var userDao: UserDao

    @Before
    fun setup() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            AppDatabase::class.java
        ).allowMainThreadQueries().build()
        
        userDao = database.userDao()
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun insertUser_shouldInsertSuccessfully() = runTest {
        // Given
        val user = UserEntity(
            id = "user1",
            username = "testuser",
            displayName = "Test User",
            fullName = "Test Full Name",
            email = "<EMAIL>",
            phone = "**********",
            profileImageUrl = null,
            isActive = true
        )

        // When
        val insertId = userDao.insertUser(user)

        // Then
        assertTrue(insertId > 0)
        
        val retrievedUser = userDao.getUserById("user1")
        assertNotNull(retrievedUser)
        assertEquals("testuser", retrievedUser?.username)
        assertEquals("Test User", retrievedUser?.displayName)
    }

    @Test
    fun getUserByUsername_shouldReturnCorrectUser() = runTest {
        // Given
        val user1 = UserEntity(
            id = "user1",
            username = "testuser1",
            displayName = "Test User 1",
            fullName = null,
            email = null,
            phone = null
        )
        val user2 = UserEntity(
            id = "user2",
            username = "testuser2",
            displayName = "Test User 2",
            fullName = null,
            email = null,
            phone = null
        )

        userDao.insertUser(user1)
        userDao.insertUser(user2)

        // When
        val retrievedUser = userDao.getUserByUsername("testuser1")

        // Then
        assertNotNull(retrievedUser)
        assertEquals("user1", retrievedUser?.id)
        assertEquals("testuser1", retrievedUser?.username)
    }

    @Test
    fun getAllUsers_shouldReturnAllUsers() = runTest {
        // Given
        val users = listOf(
            UserEntity(
                id = "user1",
                username = "auser",
                displayName = "A User",
                fullName = null,
                email = null,
                phone = null
            ),
            UserEntity(
                id = "user2",
                username = "buser",
                displayName = "B User",
                fullName = null,
                email = null,
                phone = null
            ),
            UserEntity(
                id = "user3",
                username = "cuser",
                displayName = "C User",
                fullName = null,
                email = null,
                phone = null
            )
        )

        userDao.insertUsers(users)

        // When
        val allUsers = userDao.getAllUsers().first()

        // Then
        assertEquals(3, allUsers.size)
        // Should be ordered by username
        assertEquals("auser", allUsers[0].username)
        assertEquals("buser", allUsers[1].username)
        assertEquals("cuser", allUsers[2].username)
    }

    @Test
    fun getActiveUsers_shouldReturnOnlyActiveUsers() = runTest {
        // Given
        val activeUser = UserEntity(
            id = "user1",
            username = "activeuser",
            displayName = "Active User",
            fullName = null,
            email = null,
            phone = null,
            isActive = true
        )
        val inactiveUser = UserEntity(
            id = "user2",
            username = "inactiveuser",
            displayName = "Inactive User",
            fullName = null,
            email = null,
            phone = null,
            isActive = false
        )

        userDao.insertUser(activeUser)
        userDao.insertUser(inactiveUser)

        // When
        val activeUsers = userDao.getActiveUsers().first()

        // Then
        assertEquals(1, activeUsers.size)
        assertEquals("activeuser", activeUsers[0].username)
        assertTrue(activeUsers[0].isActive)
    }

    @Test
    fun updateUser_shouldUpdateSuccessfully() = runTest {
        // Given
        val originalUser = UserEntity(
            id = "user1",
            username = "testuser",
            displayName = "Original Name",
            fullName = null,
            email = null,
            phone = null
        )
        userDao.insertUser(originalUser)

        // When
        val updatedUser = originalUser.copy(
            displayName = "Updated Name",
            email = "<EMAIL>"
        )
        val updateCount = userDao.updateUser(updatedUser)

        // Then
        assertEquals(1, updateCount)
        
        val retrievedUser = userDao.getUserById("user1")
        assertEquals("Updated Name", retrievedUser?.displayName)
        assertEquals("<EMAIL>", retrievedUser?.email)
    }

    @Test
    fun deleteUser_shouldDeleteSuccessfully() = runTest {
        // Given
        val user = UserEntity(
            id = "user1",
            username = "testuser",
            displayName = "Test User",
            fullName = null,
            email = null,
            phone = null
        )
        userDao.insertUser(user)

        // When
        val deleteCount = userDao.deleteUser(user)

        // Then
        assertEquals(1, deleteCount)
        
        val retrievedUser = userDao.getUserById("user1")
        assertNull(retrievedUser)
    }

    @Test
    fun updateSyncStatus_shouldUpdateCorrectly() = runTest {
        // Given
        val user = UserEntity(
            id = "user1",
            username = "testuser",
            displayName = "Test User",
            fullName = null,
            email = null,
            phone = null,
            syncStatus = SyncStatus.PENDING
        )
        userDao.insertUser(user)

        // When
        val timestamp = System.currentTimeMillis()
        val updateCount = userDao.updateSyncStatus("user1", SyncStatus.SYNCED, timestamp)

        // Then
        assertEquals(1, updateCount)
        
        val retrievedUser = userDao.getUserById("user1")
        assertEquals(SyncStatus.SYNCED, retrievedUser?.syncStatus)
        assertEquals(timestamp, retrievedUser?.lastSync)
    }

    @Test
    fun getUsersNeedingSync_shouldReturnCorrectUsers() = runTest {
        // Given
        val syncedUser = UserEntity(
            id = "user1",
            username = "synceduser",
            displayName = "Synced User",
            fullName = null,
            email = null,
            phone = null,
            syncStatus = SyncStatus.SYNCED
        )
        val pendingUser = UserEntity(
            id = "user2",
            username = "pendinguser",
            displayName = "Pending User",
            fullName = null,
            email = null,
            phone = null,
            syncStatus = SyncStatus.PENDING
        )
        val failedUser = UserEntity(
            id = "user3",
            username = "faileduser",
            displayName = "Failed User",
            fullName = null,
            email = null,
            phone = null,
            syncStatus = SyncStatus.FAILED
        )

        userDao.insertUsers(listOf(syncedUser, pendingUser, failedUser))

        // When
        val usersNeedingSync = userDao.getUsersNeedingSync()

        // Then
        assertEquals(2, usersNeedingSync.size)
        assertTrue(usersNeedingSync.any { it.id == "user2" })
        assertTrue(usersNeedingSync.any { it.id == "user3" })
        assertFalse(usersNeedingSync.any { it.id == "user1" })
    }

    @Test
    fun searchUsers_shouldReturnMatchingUsers() = runTest {
        // Given
        val users = listOf(
            UserEntity(
                id = "user1",
                username = "john_doe",
                displayName = "John Doe",
                fullName = "John Smith Doe",
                email = null,
                phone = null
            ),
            UserEntity(
                id = "user2",
                username = "jane_smith",
                displayName = "Jane Smith",
                fullName = "Jane Mary Smith",
                email = null,
                phone = null
            ),
            UserEntity(
                id = "user3",
                username = "bob_wilson",
                displayName = "Bob Wilson",
                fullName = "Robert Wilson",
                email = null,
                phone = null
            )
        )
        userDao.insertUsers(users)

        // When
        val searchResults = userDao.searchUsers("%john%").first()

        // Then
        assertEquals(1, searchResults.size)
        assertEquals("john_doe", searchResults[0].username)
    }
}
