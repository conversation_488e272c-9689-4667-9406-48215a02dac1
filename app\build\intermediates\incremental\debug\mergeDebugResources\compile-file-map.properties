#Sun Jul 13 15:02:10 PDT 2025
com.example.ma.app-mergeDebugResources-49\:/layout/activity_database_setup.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_database_setup.xml.flat
com.example.ma.app-main-56\:/drawable/ic_expenses.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_expenses.xml.flat
com.example.ma.app-main-52\:/drawable/gradient_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_background.xml.flat
com.example.ma.app-main-56\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.ma.app-main-52\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-56\:/drawable/ic_arrow_back.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_back.xml.flat
com.example.ma.app-main-52\:/drawable/gradient_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_primary.xml.flat
com.example.ma.app-pngs-49\:/drawable-xhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-51\:/drawable/ic_logout.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_logout.xml.flat
com.example.ma.app-main-56\:/drawable/ic_filter.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_filter.xml.flat
com.example.ma.app-mergeDebugResources-48\:/layout/activity_login.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.example.ma.app-main-51\:/drawable/ic_chart.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_chart.xml.flat
com.example.ma.app-main-56\:/drawable/ic_edit.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_edit.xml.flat
com.example.ma.app-main-56\:/drawable/ic_visibility.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_visibility.xml.flat
com.example.ma.app-main-52\:/drawable/ic_settings.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_settings.xml.flat
com.example.ma.app-main-52\:/drawable/circle_background_white.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background_white.xml.flat
com.example.ma.app-main-51\:/drawable/card_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_card_background.xml.flat
com.example.ma.app-mergeDebugResources-48\:/layout/activity_main.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.example.ma.app-main-56\:/drawable/ic_logout.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_logout.xml.flat
com.example.ma.app-main-51\:/drawable/ic_balance.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_balance.xml.flat
com.example.ma.app-main-56\:/drawable/ic_search.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_search.xml.flat
com.example.ma.app-main-52\:/drawable/rounded_background_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background_primary.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_financial.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_financial.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_financial.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_financial.xml.flat
com.example.ma.app-main-52\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-51\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-56\:/drawable/ic_description.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_description.xml.flat
com.example.ma.app-main-56\:/drawable/spinner_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_spinner_background.xml.flat
com.example.ma.app-main-52\:/drawable/ic_expenses.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_expenses.xml.flat
com.example.ma.app-main-51\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.ma.app-main-56\:/drawable/ic_person.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person.xml.flat
com.example.ma.app-main-56\:/drawable/selector_radio_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_selector_radio_background.xml.flat
com.example.ma.app-main-52\:/drawable/circle_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.example.ma.app-main-56\:/drawable/bg_amount_display.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_amount_display.xml.flat
com.example.ma.app-main-52\:/drawable/spinner_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_spinner_background.xml.flat
com.example.ma.app-main-52\:/drawable/ic_clear.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_clear.xml.flat
com.example.ma.app-main-51\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.webp.flat
com.example.ma.app-pngs-49\:/drawable-hdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-hdpi_ic_launcher_foreground.png.flat
com.example.ma.app-pngs-44\:/drawable-xxxhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xxxhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-51\:/drawable/ic_person.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_notification.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_notification.xml.flat
com.example.ma.app-main-52\:/xml/data_extraction_rules.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.example.ma.app-main-52\:/drawable/ic_edit.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_edit.xml.flat
com.example.ma.app-pngs-45\:/drawable-xhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-mergeDebugResources-49\:/layout/nav_header_main.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_nav_header_main.xml.flat
com.example.ma.app-main-51\:/drawable/ic_withdrawal.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_withdrawal.xml.flat
com.example.ma.app-main-52\:/drawable/nav_item_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_nav_item_background.xml.flat
com.example.ma.app-main-52\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.webp.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_profile.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_profile.xml.flat
com.example.ma.app-main-51\:/drawable/ic_dashboard.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_dashboard.xml.flat
com.example.ma.app-main-56\:/drawable/ic_trending_up.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_trending_up.xml.flat
com.example.ma.app-main-56\:/drawable/ic_add.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_add.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_inventory.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_inventory.xml.flat
com.example.ma.app-main-56\:/drawable/rounded_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background.xml.flat
com.example.ma.app-main-52\:/drawable/ic_lock.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_withdrawal.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_withdrawal.xml.flat
com.example.ma.app-main-56\:/drawable/bg_status_approved.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_status_approved.xml.flat
com.example.ma.app-main-52\:/drawable/rounded_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background.xml.flat
com.example.ma.app-main-56\:/drawable/ic_balance.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_balance.xml.flat
com.example.ma.app-main-51\:/drawable/ic_arrow_back.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_back.xml.flat
com.example.ma.app-main-52\:/drawable/ic_phone.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_phone.xml.flat
com.example.ma.app-main-52\:/drawable/bg_status_approved.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_status_approved.xml.flat
com.example.ma.app-main-56\:/drawable/ic_transactions.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_transactions.xml.flat
com.example.ma.app-main-51\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.ma.app-pngs-44\:/drawable-mdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-mdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-56\:/drawable/ic_launcher_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.example.ma.app-main-52\:/drawable/card_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_card_background.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_main.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.example.ma.app-main-52\:/drawable/ic_add.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_add.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_statistics.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_statistics.xml.flat
com.example.ma.app-mergeDebugResources-48\:/layout/nav_header_main.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_nav_header_main.xml.flat
com.example.ma.app-main-52\:/drawable/ic_check.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_check.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/item_transaction.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_item_transaction.xml.flat
com.example.ma.app-main-51\:/drawable/ic_visibility_off.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_visibility_off.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_main.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.example.ma.app-main-56\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-51\:/drawable/ic_inventory.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_inventory.xml.flat
com.example.ma.app-main-52\:/xml/backup_rules.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_notification.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_notification.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/item_notification.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_item_notification.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/dialog_theme_selection.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_theme_selection.xml.flat
com.example.ma.app-main-51\:/menu/activity_main_drawer.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\menu_activity_main_drawer.xml.flat
com.example.ma.app-main-56\:/drawable/ic_withdrawal.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_withdrawal.xml.flat
com.example.ma.app-main-52\:/drawable/ic_category.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_category.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_withdrawal.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_withdrawal.xml.flat
com.example.ma.app-main-56\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-51\:/navigation/nav_graph.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\navigation_nav_graph.xml.flat
com.example.ma.app-main-51\:/drawable/ic_lock_modern.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock_modern.xml.flat
com.example.ma.app-main-52\:/drawable/ic_reports.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_reports.xml.flat
com.example.ma.app-pngs-45\:/drawable-hdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-hdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-56\:/drawable/ic_menu.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_menu.xml.flat
com.example.ma.app-main-52\:/drawable/rounded_background_light.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background_light.xml.flat
com.example.ma.app-main-52\:/drawable/ic_visibility_off.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_visibility_off.xml.flat
com.example.ma.app-main-51\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.ma.app-main-56\:/drawable/ic_chart.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_chart.xml.flat
com.example.ma.app-main-52\:/menu/activity_main_drawer.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\menu_activity_main_drawer.xml.flat
com.example.ma.app-main-51\:/drawable/nav_item_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_nav_item_background.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/dialog_theme_selection.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_theme_selection.xml.flat
com.example.ma.app-main-56\:/xml/backup_rules.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.example.ma.app-main-56\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.ma.app-main-56\:/drawable/ic_trending_down.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_trending_down.xml.flat
com.example.ma.app-main-56\:/drawable/rounded_background_light.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background_light.xml.flat
com.example.ma.app-main-52\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-52\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-52\:/drawable/ic_launcher_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.example.ma.app-pngs-44\:/drawable-xhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-51\:/drawable/gradient_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_primary.xml.flat
com.example.ma.app-main-52\:/drawable/ic_transactions.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_transactions.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_transactions.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_transactions.xml.flat
com.example.ma.app-main-52\:/drawable/ic_filter.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_filter.xml.flat
com.example.ma.app-main-56\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.ma.app-main-56\:/drawable/ic_reports.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_reports.xml.flat
com.example.ma.app-main-56\:/drawable/ic_bar_chart.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_bar_chart.xml.flat
com.example.ma.app-main-52\:/drawable/ic_visibility.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_visibility.xml.flat
com.example.ma.app-main-51\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_login.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.example.ma.app-main-56\:/drawable/gradient_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_background.xml.flat
com.example.ma.app-main-51\:/drawable/ic_receipt.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_receipt.xml.flat
com.example.ma.app-main-56\:/drawable/ic_close.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_close.xml.flat
com.example.ma.app-main-51\:/drawable/ic_trending_down.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_trending_down.xml.flat
com.example.ma.app-main-56\:/drawable/ic_phone.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_phone.xml.flat
com.example.ma.app-main-51\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-52\:/drawable/ic_menu.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_menu.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_inventory.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_inventory.xml.flat
com.example.ma.app-main-56\:/drawable/circle_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.example.ma.app-main-52\:/drawable/ic_logout.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_logout.xml.flat
com.example.ma.app-main-51\:/drawable/ic_visibility.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_visibility.xml.flat
com.example.ma.app-main-52\:/drawable/ic_money.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_money.xml.flat
com.example.ma.app-main-52\:/drawable/ic_camera.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_camera.xml.flat
com.example.ma.app-main-52\:/drawable/ic_trending_down.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_trending_down.xml.flat
com.example.ma.app-main-56\:/drawable/ic_category.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_category.xml.flat
com.example.ma.app-main-52\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.webp.flat
com.example.ma.app-main-52\:/drawable/ic_account_balance.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_account_balance.xml.flat
com.example.ma.app-pngs-45\:/drawable-xxhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xxhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-52\:/drawable/ic_lock_modern.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock_modern.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_database_setup.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_database_setup.xml.flat
com.example.ma.app-main-52\:/drawable/ic_person.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person.xml.flat
com.example.ma.app-main-56\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.webp.flat
com.example.ma.app-main-56\:/drawable/ic_person_modern.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person_modern.xml.flat
com.example.ma.app-main-56\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.ma.app-pngs-45\:/drawable-mdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-mdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-51\:/drawable/ic_reports.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_reports.xml.flat
com.example.ma.app-main-56\:/menu/activity_main_drawer.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\menu_activity_main_drawer.xml.flat
com.example.ma.app-main-51\:/drawable/ic_notifications.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_notifications.xml.flat
com.example.ma.app-main-52\:/drawable/ic_inventory.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_inventory.xml.flat
com.example.ma.app-main-56\:/xml/data_extraction_rules.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.example.ma.app-main-51\:/drawable/ic_phone.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_phone.xml.flat
com.example.ma.app-main-51\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-56\:/drawable/ic_check.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_check.xml.flat
com.example.ma.app-main-51\:/drawable/ic_lock.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock.xml.flat
com.example.ma.app-main-51\:/drawable/ic_launcher_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_expense.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_expense.xml.flat
com.example.ma.app-main-56\:/drawable/rounded_background_secondary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background_secondary.xml.flat
com.example.ma.app-pngs-49\:/drawable-mdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-mdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-56\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-56\:/drawable/circle_background_white.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background_white.xml.flat
com.example.ma.app-main-56\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.ma.app-main-52\:/navigation/nav_graph.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\navigation_nav_graph.xml.flat
com.example.ma.app-main-52\:/drawable/ic_balance.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_balance.xml.flat
com.example.ma.app-main-51\:/drawable/rounded_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background.xml.flat
com.example.ma.app-main-51\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.webp.flat
com.example.ma.app-main-51\:/drawable/ic_transactions.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_transactions.xml.flat
com.example.ma.app-main-52\:/drawable/ic_dashboard.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_dashboard.xml.flat
com.example.ma.app-main-56\:/drawable/ic_lock_modern.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock_modern.xml.flat
com.example.ma.app-pngs-44\:/drawable-hdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-hdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-56\:/drawable/rounded_background_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background_primary.xml.flat
com.example.ma.app-main-56\:/drawable/ic_home.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_home.xml.flat
com.example.ma.app-main-51\:/drawable/ic_expenses.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_expenses.xml.flat
com.example.ma.app-main-52\:/drawable/ic_arrow_back.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_back.xml.flat
com.example.ma.app-main-52\:/drawable/button_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_primary.xml.flat
com.example.ma.app-main-56\:/drawable/gradient_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_primary.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/item_notification.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_item_notification.xml.flat
com.example.ma.app-main-52\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.ma.app-pngs-45\:/drawable-anydpi-v24/ic_launcher_foreground.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat
com.example.ma.app-main-52\:/drawable/ic_notifications.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_notifications.xml.flat
com.example.ma.app-main-52\:/drawable/ic_search.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_search.xml.flat
com.example.ma.app-main-56\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.ma.app-main-52\:/drawable/ic_home.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_home.xml.flat
com.example.ma.app-main-51\:/xml/data_extraction_rules.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.example.ma.app-main-52\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.ma.app-main-51\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-52\:/drawable/ic_close.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_close.xml.flat
com.example.ma.app-main-56\:/navigation/nav_graph.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\navigation_nav_graph.xml.flat
com.example.ma.app-main-52\:/drawable/ic_withdrawal.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_withdrawal.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_statistics.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_statistics.xml.flat
com.example.ma.app-main-52\:/drawable/ic_chart.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_chart.xml.flat
com.example.ma.app-main-51\:/drawable/ic_email.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_email.xml.flat
com.example.ma.app-main-51\:/drawable/ic_menu.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_menu.xml.flat
com.example.ma.app-main-56\:/drawable/ic_money.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_money.xml.flat
com.example.ma.app-main-56\:/drawable/ic_dashboard.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_dashboard.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_expense.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_expense.xml.flat
com.example.ma.app-main-52\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.ma.app-mergeDebugResources-48\:/layout/activity_profile.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_profile.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_login.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.example.ma.app-main-56\:/drawable/ic_receipt.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_receipt.xml.flat
com.example.ma.app-pngs-49\:/drawable-xxhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xxhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-mergeDebugResources-53\:/layout/activity_reports.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_reports.xml.flat
com.example.ma.app-main-52\:/drawable/ic_description.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_description.xml.flat
com.example.ma.app-pngs-49\:/drawable-anydpi-v24/ic_launcher_foreground.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat
com.example.ma.app-main-51\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_transactions.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_transactions.xml.flat
com.example.ma.app-main-52\:/drawable/ic_email.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_email.xml.flat
com.example.ma.app-main-52\:/drawable/ic_trending_up.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_trending_up.xml.flat
com.example.ma.app-main-56\:/drawable/ic_notifications.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_notifications.xml.flat
com.example.ma.app-main-52\:/drawable/bg_amount_display.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_amount_display.xml.flat
com.example.ma.app-main-56\:/drawable/button_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_primary.xml.flat
com.example.ma.app-main-52\:/drawable/ic_bar_chart.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_bar_chart.xml.flat
com.example.ma.app-main-56\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.webp.flat
com.example.ma.app-main-51\:/drawable/ic_camera.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_camera.xml.flat
com.example.ma.app-pngs-45\:/drawable-xxxhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xxxhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-56\:/drawable/ic_lock.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock.xml.flat
com.example.ma.app-main-51\:/xml/backup_rules.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.example.ma.app-main-51\:/drawable/circle_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.example.ma.app-main-52\:/drawable/ic_storage.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_storage.xml.flat
com.example.ma.app-main-51\:/drawable/ic_settings.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_settings.xml.flat
com.example.ma.app-mergeDebugResources-49\:/layout/activity_profile.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_profile.xml.flat
com.example.ma.app-main-56\:/drawable/ic_camera.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_camera.xml.flat
com.example.ma.app-main-51\:/drawable/ic_person_modern.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person_modern.xml.flat
com.example.ma.app-main-51\:/drawable/ic_account_balance.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_account_balance.xml.flat
com.example.ma.app-main-56\:/drawable/notification_badge_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_notification_badge_background.xml.flat
com.example.ma.app-main-52\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.ma.app-main-52\:/drawable/ic_person_modern.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person_modern.xml.flat
com.example.ma.app-main-56\:/drawable/ic_storage.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_storage.xml.flat
com.example.ma.app-main-52\:/drawable/ic_palette.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_palette.xml.flat
com.example.ma.app-main-52\:/drawable/rounded_background_secondary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background_secondary.xml.flat
com.example.ma.app-main-56\:/drawable/ic_clear.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_clear.xml.flat
com.example.ma.app-pngs-44\:/drawable-ldpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-ldpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-51\:/drawable/ic_money.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_money.xml.flat
com.example.ma.app-main-51\:/drawable/ic_edit.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_edit.xml.flat
com.example.ma.app-main-51\:/drawable/button_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_primary.xml.flat
com.example.ma.app-main-56\:/drawable/ic_visibility_off.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_visibility_off.xml.flat
com.example.ma.app-pngs-44\:/drawable-xxhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xxhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-56\:/drawable/ic_email.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_email.xml.flat
com.example.ma.app-pngs-44\:/drawable-anydpi-v24/ic_launcher_foreground.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/item_transaction.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_item_transaction.xml.flat
com.example.ma.app-main-56\:/drawable/ic_palette.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_palette.xml.flat
com.example.ma.app-main-56\:/drawable/ic_settings.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_settings.xml.flat
com.example.ma.app-main-52\:/drawable/ic_receipt.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_receipt.xml.flat
com.example.ma.app-main-52\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.ma.app-main-51\:/drawable/gradient_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_background.xml.flat
com.example.ma.app-pngs-49\:/drawable-ldpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-ldpi_ic_launcher_foreground.png.flat
com.example.ma.app-pngs-45\:/drawable-ldpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-ldpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-56\:/drawable/ic_inventory.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_inventory.xml.flat
com.example.ma.app-main-51\:/drawable/ic_trending_up.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_trending_up.xml.flat
com.example.ma.app-main-56\:/drawable/nav_item_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_nav_item_background.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/item_notification_new.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_item_notification_new.xml.flat
com.example.ma.app-main-56\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.ma.app-main-56\:/drawable/card_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_card_background.xml.flat
com.example.ma.app-mergeDebugResources-53\:/layout/nav_header_main.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\layout_nav_header_main.xml.flat
com.example.ma.app-main-51\:/drawable/ic_home.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_home.xml.flat
com.example.ma.app-main-56\:/drawable/ic_account_balance.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_account_balance.xml.flat
com.example.ma.app-main-51\:/drawable/circle_background_white.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background_white.xml.flat
com.example.ma.app-main-52\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.ma.app-pngs-49\:/drawable-xxxhdpi/ic_launcher_foreground.png=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\drawable-xxxhdpi_ic_launcher_foreground.png.flat
com.example.ma.app-main-51\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.webp.flat
