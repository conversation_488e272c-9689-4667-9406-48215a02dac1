<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/primary_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🗄️ راه‌اندازی دیتابیس"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_white"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ایجاد جداول Supabase برای سیستم اعلانات"
                    android:textSize="14sp"
                    android:textColor="@color/text_white"
                    android:layout_marginTop="8dp"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- دکمه‌های عملیات -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnTestConnection"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="🔗 تست اتصال و ایجاد جداول"
                android:textSize="16sp"
                android:backgroundTint="@color/info_color"
                app:cornerRadius="12dp"
                android:layout_marginBottom="12dp"
                android:fontFamily="sans-serif-medium" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCreateSampleData"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="📊 ایجاد داده‌های نمونه"
                android:textSize="16sp"
                android:backgroundTint="@color/success_color"
                app:cornerRadius="12dp"
                android:layout_marginBottom="12dp"
                android:fontFamily="sans-serif-medium" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCopyScript"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="📋 کپی اسکریپت SQL"
                android:textSize="16sp"
                android:backgroundTint="@color/warning_color"
                app:cornerRadius="12dp"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

        <!-- راهنمای استفاده -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📖 راهنمای استفاده"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="1️⃣ ابتدا 'تست اتصال' را کلیک کنید\n\n2️⃣ اگر خطا دریافت کردید، 'کپی اسکریپت SQL' را کلیک کنید\n\n3️⃣ به Supabase Dashboard بروید و در SQL Editor اسکریپت را اجرا کنید\n\n4️⃣ سپس 'ایجاد داده‌های نمونه' را کلیک کنید"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:lineSpacingExtra="4dp"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- نمایش اسکریپت SQL -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💾 اسکریپت SQL"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/tvSqlScript"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="در حال بارگذاری..."
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:background="@color/background_secondary"
                    android:padding="12dp"
                    android:fontFamily="monospace"
                    android:scrollbars="vertical" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
