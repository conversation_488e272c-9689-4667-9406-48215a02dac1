package com.example.ma.utils

import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale

/**
 * کلاس کمکی برای فرمت کردن قیمت‌ها به صورت حرفه‌ای با تبدیل به حروف
 */
object CurrencyFormatter {

    private val persianLocale = Locale("fa", "IR")
    private val decimalFormat = DecimalFormat("#,###", DecimalFormatSymbols(persianLocale))

    /**
     * فرمت کردن مبلغ به تومان
     */
    fun formatToToman(amount: Double): String {
        return "${decimalFormat.format(amount)} تومان"
    }

    /**
     * فرمت کردن مبلغ به ریال
     */
    fun formatToRial(amount: Double): String {
        return "${decimalFormat.format(amount * 10)} ریال"
    }

    /**
     * فرمت کردن مبلغ بدون واحد
     */
    fun formatNumber(amount: Double): String {
        return decimalFormat.format(amount)
    }

    /**
     * تبدیل عدد به حروف فارسی
     */
    fun numberToWords(number: Long): String {
        if (number == 0L) return "صفر"

        val ones = arrayOf("", "یک", "دو", "سه", "چهار", "پنج", "شش", "هفت", "هشت", "نه")
        val tens = arrayOf("", "", "بیست", "سی", "چهل", "پنجاه", "شصت", "هفتاد", "هشتاد", "نود")
        val teens = arrayOf("ده", "یازده", "دوازده", "سیزده", "چهارده", "پانزده", "شانزده", "هفده", "هجده", "نوزده")
        val hundreds = arrayOf("", "یکصد", "دویست", "سیصد", "چهارصد", "پانصد", "ششصد", "هفتصد", "هشتصد", "نهصد")
        val scales = arrayOf("", "هزار", "میلیون", "میلیارد", "بیلیون")

        fun convertGroup(num: Int): String {
            var result = ""
            val h = num / 100
            val t = (num % 100) / 10
            val o = num % 10

            if (h > 0) {
                result += hundreds[h]
            }

            if (t == 1) {
                if (result.isNotEmpty()) result += " و "
                result += teens[o]
            } else {
                if (t > 0) {
                    if (result.isNotEmpty()) result += " و "
                    result += tens[t]
                }
                if (o > 0) {
                    if (result.isNotEmpty()) result += " و "
                    result += ones[o]
                }
            }

            return result
        }

        var num = number
        var scaleIndex = 0
        val parts = mutableListOf<String>()

        while (num > 0) {
            val group = (num % 1000).toInt()
            if (group > 0) {
                var groupText = convertGroup(group)
                if (scaleIndex > 0) {
                    groupText += " " + scales[scaleIndex]
                }
                parts.add(0, groupText)
            }
            num /= 1000
            scaleIndex++
        }

        return parts.joinToString(" و ")
    }

    /**
     * فرمت کامل با عدد، جداکننده و حروف
     */
    fun formatWithWords(amount: Long): String {
        val formatted = formatNumber(amount.toDouble())
        val words = numberToWords(amount)
        return "$formatted ($words) تومان"
    }

    /**
     * فرمت کامل با عدد، جداکننده و حروف
     */
    fun formatWithWords(amount: Double): String {
        return formatWithWords(amount.toLong())
    }

    /**
     * فرمت کامل با عدد، جداکننده و حروف
     */
    fun formatWithWords(amount: Int): String {
        return formatWithWords(amount.toLong())
    }

    /**
     * فقط حروف بدون عدد
     */
    fun toWordsOnly(amount: Long): String {
        return "${numberToWords(amount)} تومان"
    }

    /**
     * فقط حروف بدون عدد
     */
    fun toWordsOnly(amount: Double): String {
        return toWordsOnly(amount.toLong())
    }

    /**
     * فقط حروف بدون عدد
     */
    fun toWordsOnly(amount: Int): String {
        return toWordsOnly(amount.toLong())
    }

    /**
     * فرمت کردن مبلغ با علامت مثبت/منفی
     */
    fun formatWithSign(amount: Double, showPositiveSign: Boolean = true): String {
        val formatted = formatToToman(amount)
        return when {
            amount > 0 && showPositiveSign -> "+$formatted"
            amount < 0 -> formatted // منفی خودش نشان داده می‌شود
            else -> formatted
        }
    }

    /**
     * تبدیل اعداد انگلیسی به فارسی
     */
    fun toPersianNumbers(input: String): String {
        val englishDigits = arrayOf("0", "1", "2", "3", "4", "5", "6", "7", "8", "9")
        val persianDigits = arrayOf("۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹")

        var result = input
        for (i in englishDigits.indices) {
            result = result.replace(englishDigits[i], persianDigits[i])
        }
        return result
    }

    /**
     * فرمت کامل با اعداد فارسی
     */
    fun formatToPersianToman(amount: Double): String {
        return toPersianNumbers(formatToToman(amount))
    }

    // متدهای سازگار با کدهای قبلی
    fun format(amount: Double): String = formatToToman(amount)
    fun format(amount: Long): String = formatToToman(amount.toDouble())
    fun format(amount: Int): String = formatToToman(amount.toDouble())
    fun formatWithoutUnit(amount: Double): String = formatNumber(amount)
    fun formatWithoutUnit(amount: Long): String = formatNumber(amount.toDouble())
    fun formatWithoutUnit(amount: Int): String = formatNumber(amount.toDouble())
}
