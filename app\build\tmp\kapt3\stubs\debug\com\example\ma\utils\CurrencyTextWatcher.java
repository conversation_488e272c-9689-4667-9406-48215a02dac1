package com.example.ma.utils;

/**
 * TextWatcher برای نمایش real-time قیمت به حروف و فرمت کردن با جداکننده
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\r\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\u0018\u00002\u00020\u0001B!\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J\u0012\u0010\b\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u0016J*\u0010\f\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u000fH\u0016J*\u0010\u0012\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/ma/utils/CurrencyTextWatcher;", "Landroid/text/TextWatcher;", "displayTextView", "Landroid/widget/TextView;", "showWithWords", "", "formatInput", "(Landroid/widget/TextView;ZZ)V", "afterTextChanged", "", "s", "Landroid/text/Editable;", "beforeTextChanged", "", "start", "", "count", "after", "onTextChanged", "before", "app_debug"})
public final class CurrencyTextWatcher implements android.text.TextWatcher {
    @org.jetbrains.annotations.NotNull
    private final android.widget.TextView displayTextView = null;
    private final boolean showWithWords = false;
    private final boolean formatInput = false;
    
    public CurrencyTextWatcher(@org.jetbrains.annotations.NotNull
    android.widget.TextView displayTextView, boolean showWithWords, boolean formatInput) {
        super();
    }
    
    @java.lang.Override
    public void beforeTextChanged(@org.jetbrains.annotations.Nullable
    java.lang.CharSequence s, int start, int count, int after) {
    }
    
    @java.lang.Override
    public void onTextChanged(@org.jetbrains.annotations.Nullable
    java.lang.CharSequence s, int start, int before, int count) {
    }
    
    @java.lang.Override
    public void afterTextChanged(@org.jetbrains.annotations.Nullable
    android.text.Editable s) {
    }
}