package com.example.ma.data.local.dao

import androidx.room.*
import com.example.ma.data.local.entity.UserEntity
import com.example.ma.data.local.entity.SyncStatus
import kotlinx.coroutines.flow.Flow

/**
 * DAO برای عملیات کاربران
 */
@Dao
interface UserDao {
    
    /**
     * دریافت همه کاربران
     */
    @Query("SELECT * FROM users ORDER BY username")
    fun getAllUsers(): Flow<List<UserEntity>>
    
    /**
     * دریافت کاربر بر اساس ID
     */
    @Query("SELECT * FROM users WHERE id = :userId")
    suspend fun getUserById(userId: String): UserEntity?
    
    /**
     * دریافت کاربر بر اساس username
     */
    @Query("SELECT * FROM users WHERE username = :username")
    suspend fun getUserByUsername(username: String): UserEntity?
    
    /**
     * دریافت کاربران فعال
     */
    @Query("SELECT * FROM users WHERE is_active = 1 ORDER BY username")
    fun getActiveUsers(): Flow<List<UserEntity>>
    
    /**
     * دریافت کاربران نیازمند همگام‌سازی
     */
    @Query("SELECT * FROM users WHERE sync_status != 'SYNCED'")
    suspend fun getUsersNeedingSync(): List<UserEntity>
    
    /**
     * درج کاربر جدید
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: UserEntity): Long
    
    /**
     * درج چندین کاربر
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUsers(users: List<UserEntity>): List<Long>
    
    /**
     * بروزرسانی کاربر
     */
    @Update
    suspend fun updateUser(user: UserEntity): Int
    
    /**
     * حذف کاربر
     */
    @Delete
    suspend fun deleteUser(user: UserEntity): Int
    
    /**
     * حذف کاربر بر اساس ID
     */
    @Query("DELETE FROM users WHERE id = :userId")
    suspend fun deleteUserById(userId: String): Int
    
    /**
     * بروزرسانی وضعیت همگام‌سازی
     */
    @Query("UPDATE users SET sync_status = :status, last_sync = :timestamp WHERE id = :userId")
    suspend fun updateSyncStatus(userId: String, status: SyncStatus, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * بروزرسانی URL عکس پروفایل
     */
    @Query("UPDATE users SET profile_image_url = :imageUrl, updated_at = :timestamp WHERE id = :userId")
    suspend fun updateProfileImageUrl(userId: String, imageUrl: String, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * بروزرسانی اطلاعات شخصی
     */
    @Query("UPDATE users SET display_name = :displayName, full_name = :fullName, email = :email, phone = :phone, updated_at = :timestamp WHERE id = :userId")
    suspend fun updatePersonalInfo(
        userId: String,
        displayName: String?,
        fullName: String?,
        email: String?,
        phone: String?,
        timestamp: Long = System.currentTimeMillis()
    ): Int
    
    /**
     * تنظیم وضعیت فعال/غیرفعال
     */
    @Query("UPDATE users SET is_active = :isActive, updated_at = :timestamp WHERE id = :userId")
    suspend fun setUserActiveStatus(userId: String, isActive: Boolean, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * پاک کردن همه کاربران
     */
    @Query("DELETE FROM users")
    suspend fun clearAllUsers(): Int
    
    /**
     * شمارش کاربران
     */
    @Query("SELECT COUNT(*) FROM users")
    suspend fun getUserCount(): Int
    
    /**
     * شمارش کاربران فعال
     */
    @Query("SELECT COUNT(*) FROM users WHERE is_active = 1")
    suspend fun getActiveUserCount(): Int
    
    /**
     * دریافت آخرین زمان همگام‌سازی
     */
    @Query("SELECT MAX(last_sync) FROM users")
    suspend fun getLastSyncTime(): Long?
    
    /**
     * جستجو در کاربران
     */
    @Query("SELECT * FROM users WHERE username LIKE :query OR display_name LIKE :query OR full_name LIKE :query ORDER BY username")
    fun searchUsers(query: String): Flow<List<UserEntity>>
}
