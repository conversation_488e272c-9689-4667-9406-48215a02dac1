package com.example.ma.utils

import kotlinx.coroutines.*
import kotlin.coroutines.CoroutineContext

/**
 * مدیریت Coroutines برای جلوگیری از Memory Leak
 */
class CoroutineManager : CoroutineScope {
    
    private val job = SupervisorJob()
    override val coroutineContext: CoroutineContext = job + Dispatchers.Main
    
    private val activeJobs = mutableMapOf<String, Job>()
    
    /**
     * اجرای coroutine با مدیریت lifecycle
     */
    fun launchWithLifecycle(
        key: String? = null,
        context: CoroutineContext = Dispatchers.Main,
        start: CoroutineStart = CoroutineStart.DEFAULT,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        val newJob = launch(context, start, block)
        
        key?.let { jobKey ->
            // لغو job قبلی با همین key
            activeJobs[jobKey]?.cancel()
            activeJobs[jobKey] = newJob
            
            // حذف از لیست پس از تکمیل
            newJob.invokeOnCompletion {
                activeJobs.remove(jobKey)
            }
        }
        
        return newJob
    }
    
    /**
     * اجرای coroutine در IO thread
     */
    fun launchIO(
        key: String? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return launchWithLifecycle(key, Dispatchers.IO, block = block)
    }
    
    /**
     * اجرای coroutine در Main thread
     */
    fun launchMain(
        key: String? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return launchWithLifecycle(key, Dispatchers.Main, block = block)
    }
    
    /**
     * لغو job خاص
     */
    fun cancelJob(key: String) {
        activeJobs[key]?.cancel()
        activeJobs.remove(key)
    }
    
    /**
     * لغو همه jobs
     */
    fun cancelAllJobs() {
        activeJobs.values.forEach { it.cancel() }
        activeJobs.clear()
    }
    
    /**
     * بررسی فعال بودن job
     */
    fun isJobActive(key: String): Boolean {
        return activeJobs[key]?.isActive == true
    }
    
    /**
     * دریافت تعداد jobs فعال
     */
    fun getActiveJobsCount(): Int {
        return activeJobs.size
    }
    
    /**
     * تمیز کردن منابع
     */
    fun cleanup() {
        cancelAllJobs()
        job.cancel()
    }
}

/**
 * Extension function برای Activity/Fragment
 */
fun androidx.lifecycle.LifecycleOwner.createCoroutineManager(): CoroutineManager {
    val manager = CoroutineManager()
    
    lifecycle.addObserver(object : androidx.lifecycle.DefaultLifecycleObserver {
        override fun onDestroy(owner: androidx.lifecycle.LifecycleOwner) {
            manager.cleanup()
            super.onDestroy(owner)
        }
    })
    
    return manager
}

/**
 * Safe coroutine launcher با timeout
 */
suspend fun <T> withTimeoutSafe(
    timeoutMillis: Long,
    block: suspend CoroutineScope.() -> T
): T? {
    return try {
        withTimeout(timeoutMillis, block)
    } catch (e: TimeoutCancellationException) {
        println("⏰ Coroutine timeout after ${timeoutMillis}ms")
        null
    } catch (e: CancellationException) {
        println("❌ Coroutine cancelled")
        null
    }
}

/**
 * Retry mechanism برای coroutines
 */
suspend fun <T> retryWithBackoff(
    maxRetries: Int = 3,
    initialDelayMs: Long = 1000,
    maxDelayMs: Long = 10000,
    factor: Double = 2.0,
    block: suspend () -> T
): T? {
    var currentDelay = initialDelayMs
    
    repeat(maxRetries) { attempt ->
        try {
            return block()
        } catch (e: Exception) {
            if (attempt == maxRetries - 1) {
                println("❌ Max retries reached: ${e.message}")
                return null
            }
            
            println("⚠️ Attempt ${attempt + 1} failed, retrying in ${currentDelay}ms: ${e.message}")
            delay(currentDelay)
            currentDelay = (currentDelay * factor).toLong().coerceAtMost(maxDelayMs)
        }
    }
    
    return null
}

/**
 * Debounced coroutine launcher
 */
class DebouncedCoroutineLauncher(
    private val scope: CoroutineScope,
    private val delayMs: Long = 300
) {
    private var debounceJob: Job? = null
    
    fun launch(block: suspend CoroutineScope.() -> Unit) {
        debounceJob?.cancel()
        debounceJob = scope.launch {
            delay(delayMs)
            block()
        }
    }
    
    fun cancel() {
        debounceJob?.cancel()
    }
}

/**
 * Throttled coroutine launcher
 */
class ThrottledCoroutineLauncher(
    private val scope: CoroutineScope,
    private val intervalMs: Long = 1000
) {
    private var lastExecutionTime = 0L
    private var throttleJob: Job? = null
    
    fun launch(block: suspend CoroutineScope.() -> Unit) {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastExecution = currentTime - lastExecutionTime
        
        if (timeSinceLastExecution >= intervalMs) {
            // اجرای فوری
            lastExecutionTime = currentTime
            throttleJob?.cancel()
            throttleJob = scope.launch { block() }
        } else {
            // اجرای با تأخیر
            val remainingDelay = intervalMs - timeSinceLastExecution
            throttleJob?.cancel()
            throttleJob = scope.launch {
                delay(remainingDelay)
                lastExecutionTime = System.currentTimeMillis()
                block()
            }
        }
    }
    
    fun cancel() {
        throttleJob?.cancel()
    }
}
