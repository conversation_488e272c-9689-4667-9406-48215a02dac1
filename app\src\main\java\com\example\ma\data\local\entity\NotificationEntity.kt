package com.example.ma.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ColumnInfo
import androidx.room.ForeignKey
import androidx.room.Index

/**
 * Entity برای جدول اعلانات
 */
@Entity(
    tableName = "notifications",
    foreignKeys = [
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["from_user_id"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["to_user_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["from_user_id"]),
        Index(value = ["to_user_id"]),
        Index(value = ["status"]),
        Index(value = ["created_at"])
    ]
)
data class NotificationEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "from_user_id")
    val fromUserId: String,
    
    @ColumnInfo(name = "to_user_id")
    val toUserId: String,
    
    @ColumnInfo(name = "transaction_type")
    val transactionType: TransactionType,
    
    @ColumnInfo(name = "amount")
    val amount: Double,
    
    @ColumnInfo(name = "description")
    val description: String,
    
    @ColumnInfo(name = "status")
    val status: NotificationStatus = NotificationStatus.PENDING,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "read_at")
    val readAt: Long? = null,
    
    @ColumnInfo(name = "last_sync")
    val lastSync: Long = 0L,
    
    @ColumnInfo(name = "sync_status")
    val syncStatus: SyncStatus = SyncStatus.PENDING,
    
    @ColumnInfo(name = "remote_id")
    val remoteId: String? = null
)

/**
 * وضعیت اعلان
 */
enum class NotificationStatus {
    PENDING,     // در انتظار
    APPROVED,    // تایید شده
    REJECTED,    // رد شده
    READ         // خوانده شده
}
