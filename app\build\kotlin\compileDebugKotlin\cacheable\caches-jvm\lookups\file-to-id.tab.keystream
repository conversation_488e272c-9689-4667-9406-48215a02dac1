?$PROJECT_DIR$\app\src\main\java\com\example\ma\MAApplication.kt>$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktB$PROJECT_DIR$\app\src\main\java\com\example\ma\config\AppConfig.ktH$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\AppDatabase.ktP$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\dao\NotificationDao.ktO$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\dao\TransactionDao.ktH$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\dao\UserDao.ktV$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\entity\NotificationEntity.ktU$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\entity\TransactionEntity.ktN$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\entity\UserEntity.ktE$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\ApiError.ktF$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\ApiResult.ktM$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\FinancialSummary.ktI$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\Notification.ktH$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\Transaction.ktL$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\TransactionType.ktA$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\User.ktL$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktT$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseRealtimeClient.ktP$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\AuthRepository.ktX$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\NotificationRepository.ktW$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\TransactionRepository.ktG$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\auth\LoginActivity.ktE$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\auth\LoginResult.ktH$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\auth\LoginViewModel.ktQ$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\dialogs\ThemeSelectionDialog.ktN$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktP$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\FinancialActivity.ktS$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsActivity.ktR$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsAdapter.ktQ$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktP$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\inventory\InventoryActivity.ktG$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\main\MainViewModel.ktB$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\model\UiState.ktW$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationActivity.ktV$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationAdapter.ktL$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\profile\ProfileActivity.ktP$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\setup\DatabaseSetupActivity.ktR$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\statistics\StatisticsActivity.ktJ$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CalculationManager.ktH$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CoroutineManager.ktI$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyFormatter.ktP$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyInputTextWatcher.ktK$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyTextWatcher.ktE$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\DatabaseSetup.ktF$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\ProfileManager.ktD$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\ThemeManager.ktR$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\UnifiedCurrencyTextWatcher.ktO$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\SafeCurrencyTextWatcher.ktQ$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\SimpleCurrencyTextWatcher.kt                                                                                                                                                                                                                                              