<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardTransaction"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/surface_color"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- ردیف اول: نوع و مبلغ -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tvType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="📈 فروش"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:fontFamily="sans-serif-medium"
                tools:text="📈 فروش" />

            <TextView
                android:id="@+id/tvAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="500,000 تومان"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/success_color"
                android:fontFamily="sans-serif-medium"
                tools:text="500,000 تومان" />

        </LinearLayout>

        <!-- توضیحات -->
        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="توضیحات تراکنش"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="8dp"
            android:fontFamily="sans-serif"
            tools:text="فروش محصولات ماه جاری" />

        <!-- ردیف دوم: کاربر و تاریخ -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tvUser"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="میلاد نصیری"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="sans-serif"
                tools:text="میلاد نصیری" />

            <TextView
                android:id="@+id/tvDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2024-01-15 14:30"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="sans-serif"
                tools:text="2024-01-15 14:30" />

        </LinearLayout>

        <!-- وضعیت -->
        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="تایید شده"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/success_color"
            android:background="@drawable/bg_status_approved"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:fontFamily="sans-serif-medium"
            tools:text="تایید شده" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
