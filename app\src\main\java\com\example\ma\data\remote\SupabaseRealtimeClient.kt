package com.example.ma.data.remote

import android.util.Log
import com.example.ma.config.AppConfig
import com.google.gson.Gson
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import okhttp3.*
import java.util.concurrent.TimeUnit

/**
 * کلاینت Realtime برای Supabase
 */
class SupabaseRealtimeClient {
    
    companion object {
        private const val TAG = "SupabaseRealtime"
        private val REALTIME_URL = AppConfig.Realtime.WEBSOCKET_URL
        private val API_KEY = AppConfig.SUPABASE_ANON_KEY
        private val HEARTBEAT_INTERVAL = AppConfig.Realtime.HEARTBEAT_INTERVAL_MS
        private val RECONNECT_DELAY = AppConfig.Realtime.RECONNECT_DELAY_MS
    }
    
    private val gson = Gson()
    private var webSocket: WebSocket? = null
    private var isConnected = false
    private var shouldReconnect = true
    private var heartbeatJob: Job? = null
    private var reconnectJob: Job? = null
    
    // Flows برای real-time updates
    private val _notificationUpdates = MutableSharedFlow<List<Map<String, Any>>>()
    val notificationUpdates: SharedFlow<List<Map<String, Any>>> = _notificationUpdates.asSharedFlow()
    
    private val _transactionUpdates = MutableSharedFlow<Map<String, Any>>()
    val transactionUpdates: SharedFlow<Map<String, Any>> = _transactionUpdates.asSharedFlow()
    
    private val _connectionState = MutableSharedFlow<ConnectionState>()
    val connectionState: SharedFlow<ConnectionState> = _connectionState.asSharedFlow()
    
    private val subscriptions = mutableMapOf<String, ChannelSubscription>()
    private var messageRef = 0
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(0, TimeUnit.SECONDS) // No timeout for WebSocket
        .writeTimeout(10, TimeUnit.SECONDS)
        .build()
    
    /**
     * حالت‌های اتصال
     */
    enum class ConnectionState {
        CONNECTING,
        CONNECTED,
        DISCONNECTED,
        ERROR
    }
    
    /**
     * اطلاعات subscription
     */
    data class ChannelSubscription(
        val topic: String,
        val event: String,
        val ref: String,
        val callback: (Map<String, Any>) -> Unit
    )
    
    /**
     * اتصال به Supabase Realtime
     */
    fun connect() {
        if (isConnected || webSocket != null) {
            Log.d(TAG, "Already connected or connecting")
            return
        }
        
        Log.d(TAG, "Connecting to Supabase Realtime...")
        _connectionState.tryEmit(ConnectionState.CONNECTING)
        
        val request = Request.Builder()
            .url("$REALTIME_URL?apikey=$API_KEY&vsn=1.0.0")
            .build()
        
        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket connected")
                isConnected = true
                _connectionState.tryEmit(ConnectionState.CONNECTED)
                startHeartbeat()
                
                // Re-subscribe to all channels
                subscriptions.values.forEach { subscription ->
                    sendSubscription(subscription)
                }
            }
            
            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "Received message: $text")
                handleMessage(text)
            }
            
            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket closing: $code - $reason")
            }
            
            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket closed: $code - $reason")
                handleDisconnection()
            }
            
            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket error", t)
                _connectionState.tryEmit(ConnectionState.ERROR)
                handleDisconnection()
            }
        })
    }
    
    /**
     * قطع اتصال
     */
    fun disconnect() {
        Log.d(TAG, "Disconnecting from Supabase Realtime...")
        shouldReconnect = false
        stopHeartbeat()
        stopReconnect()
        
        webSocket?.close(1000, "Client disconnect")
        webSocket = null
        isConnected = false
        _connectionState.tryEmit(ConnectionState.DISCONNECTED)
    }
    
    /**
     * Subscribe به اعلانات کاربر
     */
    fun subscribeToNotifications(userId: String, callback: (List<Map<String, Any>>) -> Unit) {
        val topic = "realtime:public:notifications:to_user_id=eq.$userId"
        val ref = generateRef()
        
        val subscription = ChannelSubscription(
            topic = topic,
            event = "*",
            ref = ref,
            callback = { data ->
                // Convert single notification to list for compatibility
                val notifications = listOf(data)
                callback(notifications)
                _notificationUpdates.tryEmit(notifications)
            }
        )
        
        subscriptions[ref] = subscription
        
        if (isConnected) {
            sendSubscription(subscription)
        }
        
        Log.d(TAG, "Subscribed to notifications for user: $userId")
    }
    
    /**
     * Subscribe به تراکنش‌ها
     */
    fun subscribeToTransactions(callback: (Map<String, Any>) -> Unit) {
        val topics = listOf("sales", "expenses", "withdrawals", "inventory")
        
        topics.forEach { table ->
            val topic = "realtime:public:$table"
            val ref = generateRef()
            
            val subscription = ChannelSubscription(
                topic = topic,
                event = "*",
                ref = ref,
                callback = { data ->
                    callback(data)
                    _transactionUpdates.tryEmit(data)
                }
            )
            
            subscriptions[ref] = subscription
            
            if (isConnected) {
                sendSubscription(subscription)
            }
        }
        
        Log.d(TAG, "Subscribed to transaction updates")
    }
    
    /**
     * Unsubscribe از channel
     */
    fun unsubscribe(ref: String) {
        subscriptions.remove(ref)?.let { subscription ->
            if (isConnected) {
                val message = mapOf(
                    "topic" to subscription.topic,
                    "event" to "phx_leave",
                    "payload" to emptyMap<String, Any>(),
                    "ref" to ref
                )
                sendMessage(message)
            }
            Log.d(TAG, "Unsubscribed from: ${subscription.topic}")
        }
    }
    
    /**
     * ارسال subscription
     */
    private fun sendSubscription(subscription: ChannelSubscription) {
        val message = mapOf(
            "topic" to subscription.topic,
            "event" to "phx_join",
            "payload" to emptyMap<String, Any>(),
            "ref" to subscription.ref
        )
        sendMessage(message)
    }
    
    /**
     * ارسال پیام
     */
    private fun sendMessage(message: Map<String, Any>) {
        val json = gson.toJson(message)
        Log.d(TAG, "Sending message: $json")
        webSocket?.send(json)
    }
    
    /**
     * پردازش پیام دریافتی
     */
    private fun handleMessage(text: String) {
        try {
            val message = gson.fromJson(text, Map::class.java) as Map<String, Any>
            val event = message["event"] as? String
            val payload = message["payload"] as? Map<String, Any>
            val ref = message["ref"] as? String
            
            when (event) {
                "INSERT", "UPDATE", "DELETE" -> {
                    // Handle database changes
                    payload?.let { data ->
                        subscriptions.values.find { it.ref == ref }?.callback?.invoke(data)
                    }
                }
                "phx_reply" -> {
                    // Handle subscription replies
                    Log.d(TAG, "Subscription reply: $payload")
                }
                "heartbeat" -> {
                    // Handle heartbeat
                    Log.d(TAG, "Heartbeat received")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing message: $text", e)
        }
    }
    
    /**
     * مدیریت قطع اتصال
     */
    private fun handleDisconnection() {
        isConnected = false
        stopHeartbeat()
        webSocket = null
        _connectionState.tryEmit(ConnectionState.DISCONNECTED)
        
        if (shouldReconnect) {
            scheduleReconnect()
        }
    }
    
    /**
     * شروع heartbeat
     */
    private fun startHeartbeat() {
        heartbeatJob = CoroutineScope(Dispatchers.IO).launch {
            while (isConnected && isActive) {
                delay(HEARTBEAT_INTERVAL)
                if (isConnected) {
                    val heartbeat = mapOf(
                        "topic" to "phoenix",
                        "event" to "heartbeat",
                        "payload" to emptyMap<String, Any>(),
                        "ref" to generateRef()
                    )
                    sendMessage(heartbeat)
                }
            }
        }
    }
    
    /**
     * توقف heartbeat
     */
    private fun stopHeartbeat() {
        heartbeatJob?.cancel()
        heartbeatJob = null
    }
    
    /**
     * برنامه‌ریزی اتصال مجدد
     */
    private fun scheduleReconnect() {
        if (reconnectJob?.isActive == true) return
        
        reconnectJob = CoroutineScope(Dispatchers.IO).launch {
            delay(RECONNECT_DELAY)
            if (shouldReconnect && !isConnected) {
                Log.d(TAG, "Attempting to reconnect...")
                connect()
            }
        }
    }
    
    /**
     * توقف اتصال مجدد
     */
    private fun stopReconnect() {
        reconnectJob?.cancel()
        reconnectJob = null
    }
    
    /**
     * تولید reference منحصر به فرد
     */
    private fun generateRef(): String {
        return (++messageRef).toString()
    }
}
