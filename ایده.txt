

🧠 تعریف دقیق پروژه حسابداری شراکتی (Real-Time)

این پروژه یک سیستم حسابداری آنلاین بین دو شریک است که تمام تراکنش‌ها و محاسبات آن باید به‌صورت لحظه‌ای (real-time) انجام شود. تمرکز کامل روی منطق داده، همگام‌سازی لحظه‌ای، و محاسبه خودکار سهم سود و مانده حساب هر شریک است.

---

👥 ساختار شراکت

دو شریک با دسترسی مستقل (احراز هویت جداگانه)
سود حاصل از فعالیت به‌صورت مساوی (۵۰/۵۰) بین دو شریک تقسیم می‌شود
اما هزینه‌هایی که هر شریک پرداخت می‌کند ممکن است متفاوت باشد
اختلاف در آورده‌ها باید از سهم سود طرف مقابل کسر یا به آن اضافه شود
برداشت‌های شخصی هر شریک نیز باید از سهم نهایی او کسر شود

---

🔄 رفتار Real-Time

هر تراکنش (فروش، هزینه، برداشت) بلافاصله در دیتابیس ثبت می‌شود
محاسبات سود، سهم هر شریک، و مانده حساب به‌صورت لحظه‌ای و خودکار به‌روزرسانی می‌شود
هر کاربر در لحظه می‌تواند وضعیت مالی خود و شریک را مشاهده کند
از Supabase (PostgreSQL + Realtime API) برای همگام‌سازی استفاده می‌شود
هیچ عملیات دستی یا دوره‌ای برای تسویه وجود ندارد؛ همه‌چیز باید به‌صورت خودکار و لحظه‌ای محاسبه شود

---

📂 ساختار داده‌ها و منطق محاسبات

جداول اصلی:
users: اطلاعات شرکا (user_id, name, phone, role)
sales: فروش‌ها (id, amount, date, created_by)
expenses: هزینه‌ها (id, amount, category, date, paid_by)
withdrawals: برداشت‌های شخصی (id, amount, type, date, user_id)

محاسبات لحظه‌ای:

`text
total_sales = SUM(sales.amount)
total_expenses = SUM(expenses.amount)
netprofit = totalsales - total_expenses

user1contribution = SUM(expenses WHERE paidby = user1)
user2contribution = SUM(expenses WHERE paidby = user2)
contributiondiff = user2contribution - user1_contribution

equalshare = netprofit / 2

user1finalshare = equalshare - contributiondiff
user2finalshare = equalshare + contributiondiff

user1balance = user1finalshare - SUM(withdrawals WHERE userid = user1)
user2balance = user2finalshare - SUM(withdrawals WHERE userid = user2)
`

📌 این محاسبات باید به‌صورت خودکار و لحظه‌ای پس از هر تغییر در داده‌ها اجرا شوند.

---

🔐 کنترل دسترسی

هر کاربر فقط می‌تواند تراکنش‌هایی که خودش ثبت کرده را ویرایش یا حذف کند
اطلاعات فروش، هزینه و برداشت برای هر دو شریک قابل مشاهده است
از Row-Level Security در Supabase برای اعمال این محدودیت‌ها استفاده می‌شود

مثال RLS:
`sql
CREATE POLICY "Only owner can update"
ON expenses
FOR UPDATE
USING (paid_by = auth.uid());
`

---

📌 نکات مهم
د
تمرکز کامل روی ساختار داده، منطق محاسبات، و رفتار real-time است
سیستم باید در هر لحظه وضعیت دقیق مالی هر شریک را محاسبه و نمایش دهد
هیچ مرحله‌ای از محاسبه نباید دستی یا دوره‌ای باشد

---

---

👤 اطلاعات کاربران و رفتار Real-Time

در این سیستم، اطلاعات هر کاربر (شریک) باید به‌صورت real-time در دسترس باشد. منظور از اطلاعات کاربر:

user_id: شناسه یکتا
name: نام کامل
phone: شماره تماس
avatar_url: آدرس عکس پروفایل
role: نقش (شریک ۱ یا ۲)

این اطلاعات باید در یک جدول جداگانه به نام users ذخیره شود و هر بار که تغییری در آن ایجاد شد (مثلاً تغییر عکس پروفایل)، سایر کاربران بلافاصله آن را ببینند—بدون نیاز به رفرش یا بارگذاری مجدد.

---

🔄 استفاده از اطلاعات کاربر در اعلان‌ها

در هر اعلان (notification) که به کاربر مقابل ارسال می‌شود، باید اطلاعات زیر نمایش داده شود:

نوع رویداد (مثلاً "فروش جدید ثبت شد")
نام کاربری ثبت‌کننده
عکس پروفایل ثبت‌کننده

📌 این یعنی سیستم اعلان باید به users متصل باشد و هنگام ساخت پیام اعلان، اطلاعات name و avatar_url را از آن بخواند.

---

✅ مثال کاربردی

فرض کن کاربر با user_id = A123 یک فروش ثبت می‌کند. سیستم باید اعلان زیر را برای شریک مقابل ارسال کند:

`
📢 علی یک فروش جدید ثبت کرد
[نمایش عکس پروفایل علی در کنار متن اعلان]
`

این اطلاعات باید از جدول users به‌صورت real-time واکشی شود تا اگر کاربر عکس یا نام خود را تغییر داد، اعلان‌های بعدی با اطلاعات جدید نمایش داده شوند.

---

📌 نکات فنی

جدول users باید real-time subscription داشته باشد (Supabase Realtime فعال باشد)
هنگام ساخت اعلان، باید JOIN یا lookup روی users انجام شود تا name و avatar_url به اعلان اضافه شود
اگر از Edge Functions یا Triggers استفاده می‌شود، باید اطلاعات کاربر را از users واکشی کرده و در payload اعلان قرار دهد

---

🖼️ پیاده‌سازی کامل سیستم عکس پروفایل

## ساختار دیتابیس:

### جدول users:
- `profile_image_url`: TEXT - ذخیره عکس به فرمت data:image/jpeg;base64,...
- Real-time subscription فعال برای همگام‌سازی لحظه‌ای

### جدول notifications:
- `from_user_profile_image`: TEXT - کپی عکس پروفایل در زمان ارسال اعلان
- `from_user_name`: VARCHAR(100) - نام کاربر فرستنده
- `notification_type`: VARCHAR(50) - نوع اعلان (transaction, profile_image_changed)
- `timestamp`: BIGINT - زمان ایجاد اعلان (milliseconds)

## مکان‌های نمایش عکس پروفایل:

### 1. صفحه اصلی (MainActivity):
- Header: `ivProfileImage` - عکس کاربر فعلی
- Navigation Drawer: `ivNavProfileImage` - عکس در منوی کشویی
- Real-time sync هر 10 ثانیه

### 2. صفحه پروفایل (ProfileActivity):
- عکس اصلی: `ivProfileImage` - قابل ویرایش با crop دایره‌ای
- انتخاب از گالری/دوربین با مجوزهای لازم
- ذخیره محلی + آپلود به Supabase
- ارسال اعلان خودکار به شریک

### 3. صفحه اعلانات (NotificationActivity):
- هر اعلان: `iv_notification_profile` - عکس فرستنده اعلان
- بارگذاری از فیلد `from_user_profile_image` در اعلان
- Fallback به آیکون پیش‌فرض در صورت عدم وجود

## ویژگی‌های پیاده‌سازی شده:

### ✅ انتخاب و ویرایش عکس:
- Image Cropper با شکل دایره‌ای
- فشرده‌سازی خودکار (JPEG, 70% quality)
- مدیریت مجوزهای Android 13+
- پیش‌نمایش فوری

### ✅ ذخیره‌سازی:
- محلی: فایل در internal storage
- آنلاین: base64 در Supabase
- تطبیق خودکار بین دو روش

### ✅ همگام‌سازی Real-Time:
- بررسی تغییرات هر 10 ثانیه
- بروزرسانی خودکار UI
- Trigger در Supabase برای اعلان تغییر

### ✅ مدیریت خطا:
- Fallback به آیکون پیش‌فرض
- مدیریت فایل‌های خراب/حذف شده
- Retry mechanism در صورت خطای شبکه

### ✅ بهینه‌سازی عملکرد:
- Cache محلی برای عکس‌ها
- فشرده‌سازی قبل از آپلود
- ایندکس‌های دیتابیس برای جستجوی سریع

## نحوه کارکرد:

1. **تغییر عکس پروفایل:**
   - کاربر عکس جدید انتخاب می‌کند
   - Crop و فشرده‌سازی
   - ذخیره محلی + آپلود به Supabase
   - Trigger خودکار اعلان به شریک
   - بروزرسانی همه مکان‌های نمایش

2. **نمایش در اعلانات:**
   - هر اعلان شامل عکس فرستنده
   - بارگذاری از فیلد ذخیره شده در اعلان
   - عدم وابستگی به تغییرات بعدی عکس

3. **همگام‌سازی:**
   - بررسی دوره‌ای تغییرات
   - بروزرسانی خودکار UI
   - حفظ consistency بین کاربران

