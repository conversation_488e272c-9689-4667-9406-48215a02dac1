package com.example.ma.ui.auth;

/**
 * صفحه ورود به سیستم
 * این صفحه احراز هویت کاربران را انجام می‌دهد
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\f\u001a\u00020\rH\u0002J\b\u0010\u000e\u001a\u00020\rH\u0002J\b\u0010\u000f\u001a\u00020\rH\u0002J\b\u0010\u0010\u001a\u00020\rH\u0002J\b\u0010\u0011\u001a\u00020\rH\u0002J\u0012\u0010\u0012\u001a\u00020\r2\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014H\u0014J\b\u0010\u0015\u001a\u00020\rH\u0002J\u0010\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\u0010\u0010\u0019\u001a\u00020\r2\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u0010\u0010\u001c\u001a\u00020\r2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\u0018\u0010\u001d\u001a\u00020\u001b2\u0006\u0010\u001e\u001a\u00020\u00182\u0006\u0010\u001f\u001a\u00020\u0018H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/example/ma/ui/auth/LoginActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "errorTextView", "Landroid/widget/TextView;", "loginButton", "Lcom/google/android/material/button/MaterialButton;", "passwordEditText", "Lcom/google/android/material/textfield/TextInputEditText;", "usernameEditText", "viewModel", "Lcom/example/ma/ui/auth/LoginViewModel;", "disableLoginButton", "", "enableLoginButton", "hideError", "navigateToMain", "observeViewModel", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "setupUI", "showError", "message", "", "showLoading", "isLoading", "", "showSuccess", "validateInput", "username", "password", "app_debug"})
public final class LoginActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.example.ma.ui.auth.LoginViewModel viewModel;
    private com.google.android.material.textfield.TextInputEditText usernameEditText;
    private com.google.android.material.textfield.TextInputEditText passwordEditText;
    private com.google.android.material.button.MaterialButton loginButton;
    private android.widget.TextView errorTextView;
    
    public LoginActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void observeViewModel() {
    }
    
    private final boolean validateInput(java.lang.String username, java.lang.String password) {
        return false;
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void hideError() {
    }
    
    private final void showSuccess(java.lang.String message) {
    }
    
    private final void disableLoginButton() {
    }
    
    private final void enableLoginButton() {
    }
    
    private final void showLoading(boolean isLoading) {
    }
    
    private final void navigateToMain() {
    }
}