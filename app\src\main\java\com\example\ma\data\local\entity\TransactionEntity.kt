package com.example.ma.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ColumnInfo
import androidx.room.ForeignKey
import androidx.room.Index

/**
 * Entity برای جدول تراکنش‌ها
 */
@Entity(
    tableName = "transactions",
    foreignKeys = [
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["user_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["user_id"]),
        Index(value = ["transaction_type"]),
        Index(value = ["status"]),
        Index(value = ["created_at"])
    ]
)
data class TransactionEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "user_id")
    val userId: String,
    
    @ColumnInfo(name = "transaction_type")
    val transactionType: TransactionType,
    
    @ColumnInfo(name = "amount")
    val amount: Double,
    
    @ColumnInfo(name = "quantity")
    val quantity: Int? = null,
    
    @ColumnInfo(name = "payment_type")
    val paymentType: String? = null,
    
    @ColumnInfo(name = "category")
    val category: String? = null,
    
    @ColumnInfo(name = "description")
    val description: String,
    
    @ColumnInfo(name = "status")
    val status: TransactionStatus = TransactionStatus.PENDING,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "approved_by")
    val approvedBy: String? = null,
    
    @ColumnInfo(name = "approved_at")
    val approvedAt: Long? = null,
    
    @ColumnInfo(name = "last_sync")
    val lastSync: Long = 0L,
    
    @ColumnInfo(name = "sync_status")
    val syncStatus: SyncStatus = SyncStatus.PENDING,
    
    @ColumnInfo(name = "remote_id")
    val remoteId: String? = null
)

/**
 * نوع تراکنش
 */
enum class TransactionType {
    SALE,        // فروش
    EXPENSE,     // هزینه
    WITHDRAWAL,  // برداشت
    INVENTORY    // انبار
}

/**
 * وضعیت تراکنش
 */
enum class TransactionStatus {
    PENDING,     // در انتظار تایید
    APPROVED,    // تایید شده
    REJECTED     // رد شده
}
