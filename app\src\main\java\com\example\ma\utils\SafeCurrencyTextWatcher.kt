package com.example.ma.utils

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import android.widget.TextView

/**
 * TextWatcher امن برای فرمت کردن ورودی ارز
 * این نسخه از مشکلات SpannableStringBuilder جلوگیری می‌کند
 */
class SafeCurrencyTextWatcher(
    private val editText: EditText,
    private val displayTextView: TextView? = null,
    private val showWithWords: Boolean = true,
    private val onAmountChanged: ((String) -> Unit)? = null
) : TextWatcher {

    private var isFormatting = false
    private var lastValidText = ""

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

    override fun afterTextChanged(s: Editable?) {
        if (isFormatting) return
        if (s == null) return

        isFormatting = true

        try {
            val currentText = s.toString()
            val cleanInput = currentText.replace(",", "").replace("٬", "").trim()

            // اگر ورودی خالی است
            if (cleanInput.isEmpty()) {
                lastValidText = ""
                updateDisplayText("")
                onAmountChanged?.invoke("")
                return
            }

            // فقط اعداد مجاز هستند
            val digitsOnly = cleanInput.filter { it.isDigit() }
            
            if (digitsOnly.isEmpty()) {
                // بازگشت به آخرین متن معتبر
                restoreText(s, lastValidText)
                return
            }

            // بررسی طول عدد (حداکثر 15 رقم)
            if (digitsOnly.length > 15) {
                restoreText(s, lastValidText)
                return
            }

            // فرمت کردن عدد
            try {
                val number = digitsOnly.toLong()
                val formatted = CurrencyFormatter.formatWithoutUnit(number)

                // اگر فرمت تغییر کرده، اعمال کن
                if (currentText != formatted) {
                    val cursorPosition = editText.selectionStart
                    
                    // جایگزینی امن محتوا
                    replaceTextSafely(s, formatted)
                    
                    // محاسبه و تنظیم cursor
                    val newPosition = calculateNewCursorPosition(
                        cursorPosition, 
                        currentText, 
                        formatted
                    )
                    
                    setCursorPosition(newPosition, formatted.length)
                }

                // ذخیره آخرین متن معتبر
                lastValidText = formatted
                
                // نمایش به حروف
                updateDisplayText(digitsOnly)
                
                // اطلاع‌رسانی تغییر مبلغ
                onAmountChanged?.invoke(digitsOnly)

            } catch (e: NumberFormatException) {
                // عدد خیلی بزرگ است، بازگشت به آخرین متن معتبر
                restoreText(s, lastValidText)
            }

        } catch (e: Exception) {
            // در صورت خطای غیرمنتظره، بازگشت به آخرین متن معتبر
            restoreText(s, lastValidText)
        } finally {
            isFormatting = false
        }
    }

    /**
     * جایگزینی امن متن
     */
    private fun replaceTextSafely(s: Editable, newText: String) {
        try {
            // حذف همه محتوا
            if (s.isNotEmpty()) {
                s.delete(0, s.length)
            }
            // اضافه کردن متن جدید
            if (newText.isNotEmpty()) {
                s.insert(0, newText)
            }
        } catch (e: Exception) {
            // اگر خطا داد، از روش دیگر استفاده کن
            try {
                s.clear()
                s.append(newText)
            } catch (e2: Exception) {
                // نادیده بگیر
            }
        }
    }

    /**
     * بازگشت به متن قبلی
     */
    private fun restoreText(s: Editable, previousText: String) {
        try {
            if (s.toString() != previousText) {
                replaceTextSafely(s, previousText)
                setCursorPosition(previousText.length, previousText.length)
            }
        } catch (e: Exception) {
            // نادیده بگیر
        }
    }

    /**
     * محاسبه موقعیت جدید cursor
     */
    private fun calculateNewCursorPosition(
        currentPosition: Int,
        oldText: String,
        newText: String
    ): Int {
        // اگر cursor در انتها بود، در انتها نگه دار
        if (currentPosition >= oldText.length) {
            return newText.length
        }

        // محاسبه تعداد کاماهای اضافه شده
        val oldCommaCount = oldText.take(currentPosition).count { it == ',' }
        val newCommaCount = newText.take(currentPosition + (newText.length - oldText.length)).count { it == ',' }
        val commaDiff = newCommaCount - oldCommaCount

        return (currentPosition + commaDiff).coerceIn(0, newText.length)
    }

    /**
     * تنظیم موقعیت cursor
     */
    private fun setCursorPosition(position: Int, maxLength: Int) {
        editText.post {
            try {
                val safePosition = position.coerceIn(0, maxLength)
                if (safePosition <= editText.text?.length ?: 0) {
                    editText.setSelection(safePosition)
                }
            } catch (e: Exception) {
                try {
                    editText.setSelection(editText.text?.length ?: 0)
                } catch (e2: Exception) {
                    // نادیده بگیر
                }
            }
        }
    }

    /**
     * نمایش مبلغ به حروف
     */
    private fun updateDisplayText(cleanInput: String) {
        displayTextView?.let { textView ->
            try {
                if (cleanInput.isEmpty()) {
                    textView.text = ""
                    textView.visibility = android.view.View.GONE
                    return
                }

                val amount = cleanInput.toLong()
                val displayText = if (showWithWords) {
                    CurrencyFormatter.toWordsOnly(amount)
                } else {
                    CurrencyFormatter.formatToToman(amount.toDouble())
                }

                textView.text = displayText
                textView.visibility = android.view.View.VISIBLE

            } catch (e: Exception) {
                textView.text = ""
                textView.visibility = android.view.View.GONE
            }
        }
    }
}
