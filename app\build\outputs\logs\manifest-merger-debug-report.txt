-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31ab07ac54b43553e4f4f9e84ac05e5a\transformed\viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\0b14a0d325b7be31f9e4f5852c438fd6\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\6ab6b17ee4a3cc4ae8b0412149201ce1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\9f3fd3b1bdb45a281debe2e980ea3267\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\c6d3f1863f317f3f98909ac7d4c8978f\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\d55724a72063ff81ddab699ca014f9fa\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\5bb620f14016afd42a4e49383e59e2c1\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\df9dcfa80f0d546aeeb8a271b97d7532\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\2a9dfe8bff647af2d2620cae67ed4acc\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\52062fbf14dbef23f470e0c2bc95fe8c\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2a6c59fd7d9aff79906befdbab457041\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d68c079d08acbaddc5f9d5c4c15b2840\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d22bca4e19330d765a34659410853ea\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c371c7dff948fbd029b17c584853a613\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\72d67a0948508bbced2e888a032273e0\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6b58322859abeb3508a72e787a70f2b1\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\40ccbe15f879819ef2ae283fda880d0d\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b9673b8047a97375d492b7ab92b2e573\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8710056a1e3baa76160b332c0851c2c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f5792ba32a95e92bf1fc8642bc792bf\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd64f46329af454af35c85998962772a\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f0f64dc7f1d2f847c0b2fe94d75b332\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\140366d98c9bd3411a83c86bc7c84643\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b1b09edfd28f1c36e8ceb003f6f0b88\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f29c4fb79c4e00f6f7631de2e68247b3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\269c9137c208afcf8e588d1c023e5446\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0ef77f86a378f2c2a432ed1a6324323\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5d386b42ac7d4f769d6fe16fce414a4\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\96b0cd463cf2e768384aae445ee30984\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc01592502836c69d45da28e5fb8c361\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\987442b29d12e86050796097d5c5ff92\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec9d7d3c79019397ea962fb6752a4985\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e1a3cb3bdd9f7aa15aa722c5f06fb2c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3032b06c50b7e6fdbbd0bef4e6b35b45\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e18c774b32355d1b41112d58b52a2e8e\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba2257034a33ed455277661d841d7e72\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ffdae34722fa070cc89cbffb6948063\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a361167f6c158fa851868e419f3cd97e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\41a6d239e43c2288711226d76606a6d1\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf86c076217225ec1b4ab5af6e6dc26c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c2c5a5f45f6783456b6f897975d28d7\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c5fb45c7ef7ff78d95954e5ced3d4cb\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0ab2d6385dece8c8b306d80d0c04013\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bd11d3e3c966ee8f8c26219a74c7a5af\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9b8207b85a0baed64663a7885d2daec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cae9907e054a6cbc3db9167eb5430dd\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e3e4226219a70447e9a9f9457a9116b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a063c58bbad9d53692a3751543d5d2f\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\81036ecdae2cb216451a1cb4e9aa47a8\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-3\1ab40d1edce5cedbd96665006cdc2b19\transformed\exifinterface-1.3.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\90ae6aa8db4d61c2cb4a1f3f5239a828\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\adc7ba3146ad5784bb76578f7b89f73e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\04022819e1742aa1779629e9a532e4d0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57dc5f008709b1503af17c20b28ccdcf\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b86e65bc19076a5b43ec772f88ed6d6d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a79ad7af33aeb60c82d7a08eb58f886\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\184acb0b4c1f60288c16a1b4401635d2\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\06a9cc97f6bb8c99a3ba73202a8476b9\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-95:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:10:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:10:22-62
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:12:5-93:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:22:5-36:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:22:5-36:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\52062fbf14dbef23f470e0c2bc95fe8c\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\52062fbf14dbef23f470e0c2bc95fe8c\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2a6c59fd7d9aff79906befdbab457041\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2a6c59fd7d9aff79906befdbab457041\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\adc7ba3146ad5784bb76578f7b89f73e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\adc7ba3146ad5784bb76578f7b89f73e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\04022819e1742aa1779629e9a532e4d0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\04022819e1742aa1779629e9a532e4d0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:21:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:19:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:20:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:23:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:18:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:14:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:22:9-40
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:15:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:13:9-38
activity#com.example.ma.ui.auth.LoginActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:24:9-32:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:26:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:25:13-50
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:27:13-31:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:28:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:30:27-74
activity#com.example.ma.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:36:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:35:13-41
activity#com.example.ma.ui.profile.ProfileActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:38:9-41:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:40:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:41:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:39:13-55
activity#com.example.ma.ui.notifications.NotificationActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:43:9-46:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:45:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:46:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:44:13-66
activity#com.example.ma.ui.reports.ReportsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:48:9-51:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:50:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:51:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:49:13-55
activity#com.example.ma.ui.financial.FinancialActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:54:9-57:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:56:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:57:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:55:13-59
activity#com.example.ma.ui.financial.ExpenseActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:59:9-62:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:61:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:62:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:60:13-57
activity#com.example.ma.ui.financial.WithdrawalActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:64:9-67:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:66:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:67:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:65:13-60
activity#com.example.ma.ui.financial.TransactionsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:69:9-72:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:71:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:72:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:70:13-62
activity#com.example.ma.ui.statistics.StatisticsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:74:9-77:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:76:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:77:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:75:13-61
activity#com.example.ma.ui.inventory.InventoryActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:79:9-82:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:81:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:82:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:80:13-59
activity#com.example.ma.ui.setup.DatabaseSetupActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:84:9-87:47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:86:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:87:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:85:13-59
activity#com.canhub.cropper.CropImageActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:90:9-92:59
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:33:9-35:39
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:92:13-56
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:91:13-64
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31ab07ac54b43553e4f4f9e84ac05e5a\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31ab07ac54b43553e4f4f9e84ac05e5a\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\0b14a0d325b7be31f9e4f5852c438fd6\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\0b14a0d325b7be31f9e4f5852c438fd6\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\6ab6b17ee4a3cc4ae8b0412149201ce1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\6ab6b17ee4a3cc4ae8b0412149201ce1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\9f3fd3b1bdb45a281debe2e980ea3267\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\9f3fd3b1bdb45a281debe2e980ea3267\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\c6d3f1863f317f3f98909ac7d4c8978f\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\c6d3f1863f317f3f98909ac7d4c8978f\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\d55724a72063ff81ddab699ca014f9fa\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\d55724a72063ff81ddab699ca014f9fa\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\5bb620f14016afd42a4e49383e59e2c1\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\5bb620f14016afd42a4e49383e59e2c1\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\df9dcfa80f0d546aeeb8a271b97d7532\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\df9dcfa80f0d546aeeb8a271b97d7532\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\2a9dfe8bff647af2d2620cae67ed4acc\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\2a9dfe8bff647af2d2620cae67ed4acc\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\52062fbf14dbef23f470e0c2bc95fe8c\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\52062fbf14dbef23f470e0c2bc95fe8c\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2a6c59fd7d9aff79906befdbab457041\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2a6c59fd7d9aff79906befdbab457041\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d68c079d08acbaddc5f9d5c4c15b2840\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d68c079d08acbaddc5f9d5c4c15b2840\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d22bca4e19330d765a34659410853ea\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d22bca4e19330d765a34659410853ea\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c371c7dff948fbd029b17c584853a613\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c371c7dff948fbd029b17c584853a613\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\72d67a0948508bbced2e888a032273e0\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\72d67a0948508bbced2e888a032273e0\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6b58322859abeb3508a72e787a70f2b1\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6b58322859abeb3508a72e787a70f2b1\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\40ccbe15f879819ef2ae283fda880d0d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\40ccbe15f879819ef2ae283fda880d0d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b9673b8047a97375d492b7ab92b2e573\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b9673b8047a97375d492b7ab92b2e573\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8710056a1e3baa76160b332c0851c2c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8710056a1e3baa76160b332c0851c2c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f5792ba32a95e92bf1fc8642bc792bf\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f5792ba32a95e92bf1fc8642bc792bf\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd64f46329af454af35c85998962772a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd64f46329af454af35c85998962772a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f0f64dc7f1d2f847c0b2fe94d75b332\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f0f64dc7f1d2f847c0b2fe94d75b332\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\140366d98c9bd3411a83c86bc7c84643\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\140366d98c9bd3411a83c86bc7c84643\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b1b09edfd28f1c36e8ceb003f6f0b88\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b1b09edfd28f1c36e8ceb003f6f0b88\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f29c4fb79c4e00f6f7631de2e68247b3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f29c4fb79c4e00f6f7631de2e68247b3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\269c9137c208afcf8e588d1c023e5446\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\269c9137c208afcf8e588d1c023e5446\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0ef77f86a378f2c2a432ed1a6324323\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0ef77f86a378f2c2a432ed1a6324323\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5d386b42ac7d4f769d6fe16fce414a4\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5d386b42ac7d4f769d6fe16fce414a4\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\96b0cd463cf2e768384aae445ee30984\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\96b0cd463cf2e768384aae445ee30984\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc01592502836c69d45da28e5fb8c361\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc01592502836c69d45da28e5fb8c361\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\987442b29d12e86050796097d5c5ff92\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\987442b29d12e86050796097d5c5ff92\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec9d7d3c79019397ea962fb6752a4985\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec9d7d3c79019397ea962fb6752a4985\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e1a3cb3bdd9f7aa15aa722c5f06fb2c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e1a3cb3bdd9f7aa15aa722c5f06fb2c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3032b06c50b7e6fdbbd0bef4e6b35b45\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3032b06c50b7e6fdbbd0bef4e6b35b45\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e18c774b32355d1b41112d58b52a2e8e\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e18c774b32355d1b41112d58b52a2e8e\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba2257034a33ed455277661d841d7e72\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba2257034a33ed455277661d841d7e72\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ffdae34722fa070cc89cbffb6948063\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ffdae34722fa070cc89cbffb6948063\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a361167f6c158fa851868e419f3cd97e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a361167f6c158fa851868e419f3cd97e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\41a6d239e43c2288711226d76606a6d1\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\41a6d239e43c2288711226d76606a6d1\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf86c076217225ec1b4ab5af6e6dc26c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf86c076217225ec1b4ab5af6e6dc26c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c2c5a5f45f6783456b6f897975d28d7\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c2c5a5f45f6783456b6f897975d28d7\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c5fb45c7ef7ff78d95954e5ced3d4cb\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c5fb45c7ef7ff78d95954e5ced3d4cb\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0ab2d6385dece8c8b306d80d0c04013\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0ab2d6385dece8c8b306d80d0c04013\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bd11d3e3c966ee8f8c26219a74c7a5af\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bd11d3e3c966ee8f8c26219a74c7a5af\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9b8207b85a0baed64663a7885d2daec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9b8207b85a0baed64663a7885d2daec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cae9907e054a6cbc3db9167eb5430dd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cae9907e054a6cbc3db9167eb5430dd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e3e4226219a70447e9a9f9457a9116b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e3e4226219a70447e9a9f9457a9116b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a063c58bbad9d53692a3751543d5d2f\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a063c58bbad9d53692a3751543d5d2f\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\81036ecdae2cb216451a1cb4e9aa47a8\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\81036ecdae2cb216451a1cb4e9aa47a8\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-3\1ab40d1edce5cedbd96665006cdc2b19\transformed\exifinterface-1.3.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-3\1ab40d1edce5cedbd96665006cdc2b19\transformed\exifinterface-1.3.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\90ae6aa8db4d61c2cb4a1f3f5239a828\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\90ae6aa8db4d61c2cb4a1f3f5239a828\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\adc7ba3146ad5784bb76578f7b89f73e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\adc7ba3146ad5784bb76578f7b89f73e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\04022819e1742aa1779629e9a532e4d0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\04022819e1742aa1779629e9a532e4d0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57dc5f008709b1503af17c20b28ccdcf\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57dc5f008709b1503af17c20b28ccdcf\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b86e65bc19076a5b43ec772f88ed6d6d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b86e65bc19076a5b43ec772f88ed6d6d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a79ad7af33aeb60c82d7a08eb58f886\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a79ad7af33aeb60c82d7a08eb58f886\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\184acb0b4c1f60288c16a1b4401635d2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\184acb0b4c1f60288c16a1b4401635d2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\06a9cc97f6bb8c99a3ba73202a8476b9\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\06a9cc97f6bb8c99a3ba73202a8476b9\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
queries
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:9:5-20:15
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:10:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:11:21-69
category#android.intent.category.OPENABLE
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:13:13-73
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:13:23-70
data
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:15:13-44
	android:mimeType
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:15:19-41
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:17:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:18:21-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:27:13-47
	android:authorities
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:26:13-37
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:24:13-63
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:28:13-30:62
	android:resource
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:30:17-59
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:29:17-67
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\04022819e1742aa1779629e9a532e4d0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\04022819e1742aa1779629e9a532e4d0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
