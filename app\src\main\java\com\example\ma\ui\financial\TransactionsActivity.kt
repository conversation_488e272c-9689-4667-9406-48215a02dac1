package com.example.ma.ui.financial

import android.app.DatePickerDialog
import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import com.example.ma.data.remote.SupabaseClient
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * صفحه تراکنش‌ها با فیلتر و جستجو
 */
class TransactionsActivity : AppCompatActivity() {

    private lateinit var etSearch: TextInputEditText
    private lateinit var etDateFrom: TextInputEditText
    private lateinit var etDateTo: TextInputEditText
    private lateinit var spinnerType: AutoCompleteTextView
    private lateinit var spinnerUser: AutoCompleteTextView
    private lateinit var spinnerStatus: AutoCompleteTextView
    private lateinit var btnFilter: MaterialButton
    private lateinit var btnClearFilter: MaterialButton
    private lateinit var recyclerView: RecyclerView
    
    private lateinit var adapter: TransactionsAdapter
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_transactions)

        setupUI()
        setupFilters()
        setupRecyclerView()
        loadTransactions()
    }

    private fun setupUI() {
        // Toolbar
        findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)?.setNavigationOnClickListener {
            onBackPressed()
        }

        etSearch = findViewById(R.id.etSearch)
        etDateFrom = findViewById(R.id.etDateFrom)
        etDateTo = findViewById(R.id.etDateTo)
        spinnerType = findViewById(R.id.spinnerType)
        spinnerUser = findViewById(R.id.spinnerUser)
        spinnerStatus = findViewById(R.id.spinnerStatus)
        btnFilter = findViewById(R.id.btnFilter)
        btnClearFilter = findViewById(R.id.btnClearFilter)
        recyclerView = findViewById(R.id.recyclerView)

        // تنظیم انتخاب تاریخ
        etDateFrom.setOnClickListener { showDatePicker(etDateFrom) }
        etDateTo.setOnClickListener { showDatePicker(etDateTo) }

        // دکمه‌های فیلتر
        btnFilter.setOnClickListener { applyFilters() }
        btnClearFilter.setOnClickListener { clearFilters() }
    }

    private fun setupFilters() {
        // فیلتر نوع تراکنش
        val types = arrayOf("همه", "فروش", "هزینه عملیاتی", "هزینه مشارکتی", "برداشت")
        val typeAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, types)
        spinnerType.setAdapter(typeAdapter)
        spinnerType.setText(types[0], false)

        // فیلتر کاربر
        val users = arrayOf("همه", "میلاد نصیری", "علی کاکایی")
        val userAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, users)
        spinnerUser.setAdapter(userAdapter)
        spinnerUser.setText(users[0], false)

        // فیلتر وضعیت
        val statuses = arrayOf("همه", "در انتظار", "تایید شده", "رد شده")
        val statusAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, statuses)
        spinnerStatus.setAdapter(statusAdapter)
        spinnerStatus.setText(statuses[0], false)
    }

    private fun setupRecyclerView() {
        adapter = TransactionsAdapter { transaction ->
            // کلیک روی تراکنش
            showTransactionDetails(transaction)
        }
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter
    }

    private fun loadTransactions() {
        lifecycleScope.launch {
            try {
                val transactions = SupabaseClient.getAllTransactions()
                adapter.updateTransactions(transactions)
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@TransactionsActivity, "خطا در بارگذاری تراکنش‌ها", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun applyFilters() {
        val searchText = etSearch.text.toString().trim()
        val dateFrom = etDateFrom.text.toString().trim()
        val dateTo = etDateTo.text.toString().trim()
        val type = getFilterValue(spinnerType.text.toString())
        val user = getFilterValue(spinnerUser.text.toString())
        val status = getFilterValue(spinnerStatus.text.toString())

        lifecycleScope.launch {
            try {
                val transactions = SupabaseClient.getFilteredTransactions(
                    userId = user,
                    type = type,
                    status = status
                )
                adapter.updateTransactions(transactions)
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@TransactionsActivity, "خطا در اعمال فیلتر", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun clearFilters() {
        etSearch.text?.clear()
        etDateFrom.text?.clear()
        etDateTo.text?.clear()
        spinnerType.setText("همه", false)
        spinnerUser.setText("همه", false)
        spinnerStatus.setText("همه", false)
        loadTransactions()
    }

    private fun showDatePicker(editText: TextInputEditText) {
        val calendar = Calendar.getInstance()
        val datePicker = DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                calendar.set(year, month, dayOfMonth)
                editText.setText(dateFormat.format(calendar.time))
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        )
        datePicker.show()
    }

    private fun getFilterValue(displayValue: String): String? {
        return when (displayValue) {
            "همه" -> null
            "فروش" -> "sale"
            "هزینه عملیاتی" -> "business_expense"
            "هزینه مشارکتی" -> "shared_expense"
            "برداشت" -> "withdrawal"
            "میلاد نصیری" -> "miladnasiri"
            "علی کاکایی" -> "alikakai"
            "در انتظار" -> "pending"
            "تایید شده" -> "approved"
            "رد شده" -> "rejected"
            else -> null
        }
    }

    private fun showTransactionDetails(transaction: Map<String, Any>) {
        // نمایش جزئیات تراکنش
        Toast.makeText(this, "جزئیات تراکنش: ${transaction["description"]}", Toast.LENGTH_SHORT).show()
    }
}
