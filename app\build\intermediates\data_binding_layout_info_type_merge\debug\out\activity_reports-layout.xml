<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_reports" modulePackage="com.example.ma" filePath="app\src\main\res\layout\activity_reports.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_reports_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="519" endOffset="12"/></Target><Target id="@+id/tvTotalSales" view="TextView"><Expressions/><location startLine="64" startOffset="20" endLine="71" endOffset="66"/></Target><Target id="@+id/tvTotalExpenses" view="TextView"><Expressions/><location startLine="90" startOffset="20" endLine="97" endOffset="64"/></Target><Target id="@+id/tvNetProfit" view="TextView"><Expressions/><location startLine="115" startOffset="20" endLine="122" endOffset="66"/></Target><Target id="@+id/tvMiladShare" view="TextView"><Expressions/><location startLine="169" startOffset="20" endLine="176" endOffset="65"/></Target><Target id="@+id/tvMiladWithdrawals" view="TextView"><Expressions/><location startLine="195" startOffset="20" endLine="202" endOffset="66"/></Target><Target id="@+id/tvMiladFinalBalance" view="TextView"><Expressions/><location startLine="222" startOffset="20" endLine="229" endOffset="66"/></Target><Target id="@+id/tvMiladCashBalance" view="TextView"><Expressions/><location startLine="257" startOffset="20" endLine="263" endOffset="65"/></Target><Target id="@+id/tvMiladCardBalance" view="TextView"><Expressions/><location startLine="281" startOffset="20" endLine="287" endOffset="65"/></Target><Target id="@+id/tvMiladPersonalBalance" view="TextView"><Expressions/><location startLine="304" startOffset="20" endLine="310" endOffset="65"/></Target><Target id="@+id/tvAliShare" view="TextView"><Expressions/><location startLine="357" startOffset="20" endLine="364" endOffset="65"/></Target><Target id="@+id/tvAliWithdrawals" view="TextView"><Expressions/><location startLine="383" startOffset="20" endLine="390" endOffset="66"/></Target><Target id="@+id/tvAliFinalBalance" view="TextView"><Expressions/><location startLine="410" startOffset="20" endLine="417" endOffset="66"/></Target><Target id="@+id/tvAliCashBalance" view="TextView"><Expressions/><location startLine="445" startOffset="20" endLine="451" endOffset="65"/></Target><Target id="@+id/tvAliCardBalance" view="TextView"><Expressions/><location startLine="469" startOffset="20" endLine="475" endOffset="65"/></Target><Target id="@+id/tvAliPersonalBalance" view="TextView"><Expressions/><location startLine="492" startOffset="20" endLine="498" endOffset="65"/></Target><Target id="@+id/tvLastCalculated" view="TextView"><Expressions/><location startLine="507" startOffset="8" endLine="515" endOffset="45"/></Target></Targets></Layout>