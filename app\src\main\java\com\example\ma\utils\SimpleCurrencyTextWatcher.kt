package com.example.ma.utils

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import android.widget.TextView

/**
 * TextWatcher ساده برای فرمت کردن ورودی ارز
 * بدون پیچیدگی اضافی
 */
class SimpleCurrencyTextWatcher(
    private val editText: EditText,
    private val displayTextView: TextView? = null,
    private val onAmountChanged: (() -> Unit)? = null
) : TextWatcher {

    private var isUpdating = false

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

    override fun afterTextChanged(s: Editable?) {
        if (isUpdating) return
        
        isUpdating = true
        
        try {
            val input = s.toString()
            
            // حذف همه انواع کاما و فاصله
            val cleanInput = input
                .replace(",", "")      // کاما انگلیسی
                .replace("٬", "")      // کاما فارسی
                .replace("،", "")      // ویرگول فارسی
                .replace(" ", "")      // فاصله
                .replace("\u00A0", "") // non-breaking space
                .trim()
            
            // اگر خالی است
            if (cleanInput.isEmpty()) {
                displayTextView?.text = ""
                displayTextView?.visibility = android.view.View.GONE
                onAmountChanged?.invoke()
                return
            }
            
            // فقط اعداد
            val digitsOnly = cleanInput.filter { it.isDigit() }
            
            if (digitsOnly.isEmpty()) {
                // اگر هیچ عددی نیست، پاک کن
                s?.clear()
                displayTextView?.text = ""
                displayTextView?.visibility = android.view.View.GONE
                onAmountChanged?.invoke()
                return
            }

            // بررسی طول عدد (حداکثر 15 رقم)
            if (digitsOnly.length > 15) {
                // عدد خیلی بزرگ - نگه دار همون متن قبلی
                return
            }

            // فرمت کردن
            try {
                val number = digitsOnly.toLong()
                // استفاده از locale انگلیسی برای کاما
                val formatted = String.format(java.util.Locale.US, "%,d", number)
                
                // اگر متن تغییر کرده
                if (input != formatted) {
                    val cursorPos = editText.selectionStart
                    
                    // تنظیم متن جدید
                    editText.setText(formatted)
                    
                    // تنظیم cursor
                    val newPos = (cursorPos + (formatted.length - input.length)).coerceAtMost(formatted.length)
                    editText.setSelection(newPos.coerceAtLeast(0))
                }
                
                // نمایش به حروف
                updateDisplayText(number)
                
                // اطلاع رسانی
                onAmountChanged?.invoke()
                
            } catch (e: NumberFormatException) {
                // عدد خیلی بزرگ - نگه دار همون متن قبلی
                println("❌ NumberFormatException in TextWatcher: ${e.message}")
                println("❌ Digits: '$digitsOnly'")
            }
            
        } catch (e: Exception) {
            // در صورت خطا چیزی نکن
        } finally {
            isUpdating = false
        }
    }
    
    private fun updateDisplayText(amount: Long) {
        displayTextView?.let { textView ->
            try {
                val displayText = CurrencyFormatter.toWordsOnly(amount)
                textView.text = displayText
                textView.visibility = android.view.View.VISIBLE
            } catch (e: Exception) {
                textView.text = ""
                textView.visibility = android.view.View.GONE
            }
        }
    }
}
