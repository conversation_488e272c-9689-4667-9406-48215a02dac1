<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/surface_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header با عکس پروفایل و نام -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <!-- عکس پروفایل -->
            <ImageView
                android:id="@+id/ivProfilePicture"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_person"
                android:background="@drawable/circle_background"
                android:padding="2dp"
                android:scaleType="centerCrop" />

            <!-- نام فرستنده -->
            <TextView
                android:id="@+id/tvSenderName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:text="نام فرستنده"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:fontFamily="sans-serif-medium" />

            <!-- وضعیت -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="@color/warning_color">

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="در انتظار"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_white"
                    android:padding="8dp"
                    android:fontFamily="sans-serif-medium" />

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- جزئیات تراکنش -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/rounded_background"
            android:backgroundTint="@color/background_color"
            android:padding="12dp"
            android:layout_marginBottom="12dp">

            <!-- نوع تراکنش -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="نوع تراکنش:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tvTransactionType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="فروش"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/success_color"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- مبلغ -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مبلغ:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tvAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="۱,۵۰۰,۰۰۰ تومان"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- تعداد -->
            <LinearLayout
                android:id="@+id/layoutQuantity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tvQuantityLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="تعداد فروش:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tvQuantity"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="۱۰ بطری"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- نوع حساب -->
            <LinearLayout
                android:id="@+id/layoutAccount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tvAccountLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="واریز به حساب:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tvAccount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="نقدی میلاد"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/info_color"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- توضیحات -->
            <LinearLayout
                android:id="@+id/layoutDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="توضیحات:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tvDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="فروش محصولات ماه جاری"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <!-- تاریخ -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="تاریخ ثبت:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tvDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="۱۴۰۳/۰۷/۱۳ - ۱۴:۳۰"
                    android:textSize="14sp"
                    android:textColor="@color/text_hint"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </LinearLayout>

        <!-- دکمه‌های عملیات -->
        <LinearLayout
            android:id="@+id/layoutActions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:visibility="gone">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnReject"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="رد"
                android:textSize="14sp"
                android:backgroundTint="@color/error_color"
                app:cornerRadius="8dp"
                android:layout_marginEnd="8dp"
                style="@style/Widget.MaterialComponents.Button" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnApprove"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تایید"
                android:textSize="14sp"
                android:backgroundTint="@color/success_color"
                app:cornerRadius="8dp"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
