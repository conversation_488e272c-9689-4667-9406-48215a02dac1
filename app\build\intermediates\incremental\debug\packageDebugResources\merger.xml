<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res"><file name="button_primary" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="card_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_background_white" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\circle_background_white.xml" qualifiers="" type="drawable"/><file name="gradient_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="gradient_primary" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\gradient_primary.xml" qualifiers="" type="drawable"/><file name="ic_account_balance" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_account_balance.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_balance" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_balance.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_chart" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_chart.xml" qualifiers="" type="drawable"/><file name="ic_dashboard" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_dashboard.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_email" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_email.xml" qualifiers="" type="drawable"/><file name="ic_expenses" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_expenses.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_inventory" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_inventory.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_lock" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_lock.xml" qualifiers="" type="drawable"/><file name="ic_lock_modern" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_lock_modern.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_money" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_money.xml" qualifiers="" type="drawable"/><file name="ic_notifications" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_notifications.xml" qualifiers="" type="drawable"/><file name="ic_person" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_person_modern" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_person_modern.xml" qualifiers="" type="drawable"/><file name="ic_phone" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_phone.xml" qualifiers="" type="drawable"/><file name="ic_receipt" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_receipt.xml" qualifiers="" type="drawable"/><file name="ic_reports" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_reports.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_transactions" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_transactions.xml" qualifiers="" type="drawable"/><file name="ic_trending_down" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_trending_down.xml" qualifiers="" type="drawable"/><file name="ic_trending_up" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_trending_up.xml" qualifiers="" type="drawable"/><file name="ic_visibility" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_visibility.xml" qualifiers="" type="drawable"/><file name="ic_visibility_off" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_visibility_off.xml" qualifiers="" type="drawable"/><file name="ic_withdrawal" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_withdrawal.xml" qualifiers="" type="drawable"/><file name="nav_item_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\nav_item_background.xml" qualifiers="" type="drawable"/><file name="rounded_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="activity_login" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_profile" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_profile.xml" qualifiers="" type="layout"/><file name="nav_header_main" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\nav_header_main.xml" qualifiers="" type="layout"/><file name="activity_main_drawer" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\menu\activity_main_drawer.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#6C5CE7</color><color name="primary_dark_color">#5A4FCF</color><color name="primary_light">#A29BFE</color><color name="accent_color">#00CEC9</color><color name="secondary_color">#FD79A8</color><color name="background_color">#F8F9FA</color><color name="background_secondary">#E9ECEF</color><color name="surface_color">#FFFFFF</color><color name="card_background">#FFFFFF</color><color name="text_primary">#2D3436</color><color name="text_secondary">#636E72</color><color name="text_hint">#B2BEC3</color><color name="text_white">#FFFFFF</color><color name="success_color">#00B894</color><color name="success_light">#E8F8F5</color><color name="error_color">#E17055</color><color name="warning_color">#FDCB6E</color><color name="info_color">#74B9FF</color><color name="gradient_start">#6C5CE7</color><color name="gradient_end">#A29BFE</color><color name="overlay_dark">#80000000</color><color name="overlay_light">#40FFFFFF</color><color name="income_color">#00B894</color><color name="expense_color">#E17055</color><color name="balance_color">#6C5CE7</color><color name="outline">#79747E</color><color name="on_surface_variant">#49454F</color><color name="on_surface">#1C1B1F</color><color name="outline_variant">#CAC4D0</color><color name="surface_variant">#E7E0EC</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="nav_header_vertical_spacing">8dp</dimen><dimen name="nav_header_height">176dp</dimen><dimen name="fab_margin">16dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">حسابداری شراکت</string><string name="login_title">ورود به سیستم</string><string name="username_hint">نام کاربری</string><string name="password_hint">رمز عبور</string><string name="login_button">ورود</string><string name="show_password">نمایش رمز</string><string name="hide_password">مخفی کردن رمز</string><string name="invalid_credentials">نام کاربری یا رمز عبور اشتباه است</string><string name="welcome_message">خوش آمدید</string><string name="dashboard">داشبورد</string><string name="transactions">تراکنش‌ها</string><string name="notifications">اعلانات</string><string name="reports">گزارشات</string><string name="logout">خروج</string><string name="main_menu">منوی اصلی</string><string name="sale">فروش بطری</string><string name="purchase">خرید مواد اولیه</string><string name="expense">پرداختی</string><string name="income">دریافتی</string><string name="new_transaction">تراکنش جدید</string><string name="transaction_list">لیست تراکنش‌ها</string><string name="total_sales">کل فروش</string><string name="total_purchases">کل خرید</string><string name="total_expenses">کل پرداختی</string><string name="total_income">کل دریافتی</string><string name="net_profit">سود خالص</string><string name="my_share">سهم من</string><string name="partner_share">سهم شریک</string><string name="financial_summary">خلاصه مالی</string><string name="amount">مبلغ</string><string name="description">توضیحات</string><string name="date">تاریخ</string><string name="save">ذخیره</string><string name="cancel">لغو</string><string name="approve">تایید</string><string name="reject">رد</string><string name="pending">در انتظار تایید</string><string name="approved">تایید شده</string><string name="rejected">رد شده</string><string name="loading">در حال بارگذاری...</string><string name="error">خطا</string><string name="success">موفق</string><string name="confirm">تایید</string><string name="delete">حذف</string><string name="edit">ویرایش</string><string name="add">افزودن</string><string name="search">جستجو</string><string name="filter">فیلتر</string><string name="today">امروز</string><string name="yesterday">دیروز</string><string name="this_week">این هفته</string><string name="this_month">این ماه</string><string name="currency_unit">تومان</string><string name="transaction_saved">تراکنش با موفقیت ذخیره شد</string><string name="transaction_approved">تراکنش تایید شد</string><string name="transaction_rejected">تراکنش رد شد</string><string name="notification_sent">اعلان ارسال شد</string><string name="no_data">اطلاعاتی موجود نیست</string><string name="connection_error">خطا در اتصال به سرور</string><string name="new_notification">اعلان جدید</string><string name="mark_as_read">علامت‌گذاری به عنوان خوانده شده</string><string name="unread_notifications">اعلانات خوانده نشده</string><string name="no_notifications">اعلانی موجود نیست</string><string name="bottle_count_hint">تعداد بطری</string><string name="price_hint">قیمت</string><string name="transaction_type">نوع تراکنش</string><string name="cash_payment">نقدی</string><string name="card_payment">کارت به کارت</string><string name="cash_receiver">دریافت کننده</string><string name="card_destination">کارت مقصد</string><string name="ali_kakai_card">کارت علی کاکایی</string><string name="milad_nasiri_card">کارت میلاد نصیری</string><string name="register_transaction">ثبت تراکنش</string><string name="inventory_stock">موجودی انبار</string><string name="total_balance">موجودی کل</string><string name="account_balance">بدهکاری/طلبکاری</string><string name="bottles_unit">بطری</string><string name="menu_expenses">هزینه‌ها</string><string name="menu_inventory">انبار گردانی</string><string name="menu_personal_withdrawal">برداشت شخصی</string><string name="menu_notifications">اعلانات</string><string name="transaction_sent_for_approval">تراکنش برای تایید ارسال شد</string><string name="transaction_approved_message">تراکنش شما تایید شد</string><string name="transaction_rejected_message">تراکنش شما رد شد</string><string name="please_enter_bottle_count">لطفاً تعداد بطری را وارد کنید</string><string name="please_enter_price">لطفاً قیمت را وارد کنید</string><string name="please_select_payment_type">لطفاً نوع پرداخت را انتخاب کنید</string><string name="please_select_receiver">لطفاً دریافت کننده را انتخاب کنید</string><string name="invalid_number">عدد وارد شده معتبر نیست</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\styles.xml" qualifiers=""><style name="NavigationMenuTextStyle" parent="@style/TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/text_primary</item>
    </style><style name="NavigationSubheaderStyle" parent="@style/TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textAllCaps">false</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.MA" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/text_white</item>

        
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/secondary_color</item>
        <item name="colorOnSecondary">@color/text_white</item>

        
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="colorError">@color/error_color</item>
        <item name="colorOnError">@color/text_white</item>

        
        <item name="android:statusBarColor">@color/primary_color</item>
        <item name="android:windowLightStatusBar">false</item>
    </style><style name="Theme.MA" parent="Base.Theme.MA"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.MA" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/background_color</item>

        
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/secondary_color</item>
        <item name="colorOnSecondary">@color/background_color</item>

        
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="colorError">@color/error_color</item>
        <item name="colorOnError">@color/text_white</item>

        
        <item name="android:statusBarColor">@color/background_color</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background_color</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="colorControlNormal">@color/text_secondary</item>
        <item name="colorControlActivated">@color/primary_color</item>
        <item name="colorControlHighlight">@color/primary_light</item>
    </style><style name="Theme.MA" parent="Base.Theme.MA"/></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="activity_notification" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_notification.xml" qualifiers="" type="layout"/><file name="item_notification" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\item_notification.xml" qualifiers="" type="layout"/><file name="ic_add" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_check" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="ic_bar_chart" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_bar_chart.xml" qualifiers="" type="drawable"/><file name="rounded_background_light" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\rounded_background_light.xml" qualifiers="" type="drawable"/><file name="bg_amount_display" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\bg_amount_display.xml" qualifiers="" type="drawable"/><file name="bg_status_approved" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\bg_status_approved.xml" qualifiers="" type="drawable"/><file name="ic_category" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_category.xml" qualifiers="" type="drawable"/><file name="ic_clear" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_clear.xml" qualifiers="" type="drawable"/><file name="ic_description" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_description.xml" qualifiers="" type="drawable"/><file name="ic_filter" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_filter.xml" qualifiers="" type="drawable"/><file name="ic_palette" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_palette.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_storage" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_storage.xml" qualifiers="" type="drawable"/><file name="activity_database_setup" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_database_setup.xml" qualifiers="" type="layout"/><file name="activity_expense" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_expense.xml" qualifiers="" type="layout"/><file name="activity_financial" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_financial.xml" qualifiers="" type="layout"/><file name="activity_inventory" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_inventory.xml" qualifiers="" type="layout"/><file name="activity_statistics" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_statistics.xml" qualifiers="" type="layout"/><file name="activity_transactions" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_transactions.xml" qualifiers="" type="layout"/><file name="activity_withdrawal" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_withdrawal.xml" qualifiers="" type="layout"/><file name="dialog_theme_selection" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\dialog_theme_selection.xml" qualifiers="" type="layout"/><file name="item_transaction" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\item_transaction.xml" qualifiers="" type="layout"/><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="background_color">#0B0B0F</color><color name="surface_color">#1A1A1D</color><color name="card_background">#1A1A1D</color><color name="text_primary">#F5F5F5</color><color name="text_secondary">#A0A0A0</color><color name="text_white">#F5F5F5</color><color name="primary_color">#D4AF37</color><color name="primary_light">#E6C866</color><color name="primary_dark_color">#B8941F</color><color name="accent_color">#D4AF37</color><color name="success_color">#4CAF50</color><color name="error_color">#FF5252</color><color name="warning_color">#FF9800</color><color name="info_color">#00BFFF</color><color name="secondary_color">#00BFFF</color><color name="outline">#333333</color><color name="outline_variant">#2A2A2A</color><color name="black">#000000</color><color name="white">#FFFFFF</color><color name="primary_variant">#B8941F</color><color name="secondary_variant">#0099CC</color></file><file name="rounded_background_primary" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\rounded_background_primary.xml" qualifiers="" type="drawable"/><file name="rounded_background_secondary" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\rounded_background_secondary.xml" qualifiers="" type="drawable"/><file name="selector_radio_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\selector_radio_background.xml" qualifiers="" type="drawable"/><file name="item_notification_new" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\item_notification_new.xml" qualifiers="" type="layout"/></source><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>