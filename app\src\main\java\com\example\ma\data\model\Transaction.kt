package com.example.ma.data.model

/**
 * مدل تراکنش مالی
 */
data class Transaction(
    val id: String,
    val userId: String,
    val type: String,           // sale, expense, withdrawal
    val amount: Double,
    val description: String,
    val category: String? = null,
    val status: String = "pending",  // pending, approved, rejected
    val createdAt: String,
    val approvedBy: String? = null,
    val approvedAt: String? = null
)
