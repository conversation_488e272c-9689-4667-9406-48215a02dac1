// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityReportsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextView tvAliCardBalance;

  @NonNull
  public final TextView tvAliCashBalance;

  @NonNull
  public final TextView tvAliFinalBalance;

  @NonNull
  public final TextView tvAliPersonalBalance;

  @NonNull
  public final TextView tvAliShare;

  @NonNull
  public final TextView tvAliWithdrawals;

  @NonNull
  public final TextView tvLastCalculated;

  @NonNull
  public final TextView tvMiladCardBalance;

  @NonNull
  public final TextView tvMiladCashBalance;

  @NonNull
  public final TextView tvMiladFinalBalance;

  @NonNull
  public final TextView tvMiladPersonalBalance;

  @NonNull
  public final TextView tvMiladShare;

  @NonNull
  public final TextView tvMiladWithdrawals;

  @NonNull
  public final TextView tvNetProfit;

  @NonNull
  public final TextView tvTotalExpenses;

  @NonNull
  public final TextView tvTotalSales;

  private ActivityReportsBinding(@NonNull ScrollView rootView, @NonNull TextView tvAliCardBalance,
      @NonNull TextView tvAliCashBalance, @NonNull TextView tvAliFinalBalance,
      @NonNull TextView tvAliPersonalBalance, @NonNull TextView tvAliShare,
      @NonNull TextView tvAliWithdrawals, @NonNull TextView tvLastCalculated,
      @NonNull TextView tvMiladCardBalance, @NonNull TextView tvMiladCashBalance,
      @NonNull TextView tvMiladFinalBalance, @NonNull TextView tvMiladPersonalBalance,
      @NonNull TextView tvMiladShare, @NonNull TextView tvMiladWithdrawals,
      @NonNull TextView tvNetProfit, @NonNull TextView tvTotalExpenses,
      @NonNull TextView tvTotalSales) {
    this.rootView = rootView;
    this.tvAliCardBalance = tvAliCardBalance;
    this.tvAliCashBalance = tvAliCashBalance;
    this.tvAliFinalBalance = tvAliFinalBalance;
    this.tvAliPersonalBalance = tvAliPersonalBalance;
    this.tvAliShare = tvAliShare;
    this.tvAliWithdrawals = tvAliWithdrawals;
    this.tvLastCalculated = tvLastCalculated;
    this.tvMiladCardBalance = tvMiladCardBalance;
    this.tvMiladCashBalance = tvMiladCashBalance;
    this.tvMiladFinalBalance = tvMiladFinalBalance;
    this.tvMiladPersonalBalance = tvMiladPersonalBalance;
    this.tvMiladShare = tvMiladShare;
    this.tvMiladWithdrawals = tvMiladWithdrawals;
    this.tvNetProfit = tvNetProfit;
    this.tvTotalExpenses = tvTotalExpenses;
    this.tvTotalSales = tvTotalSales;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityReportsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityReportsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_reports, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityReportsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tvAliCardBalance;
      TextView tvAliCardBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvAliCardBalance == null) {
        break missingId;
      }

      id = R.id.tvAliCashBalance;
      TextView tvAliCashBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvAliCashBalance == null) {
        break missingId;
      }

      id = R.id.tvAliFinalBalance;
      TextView tvAliFinalBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvAliFinalBalance == null) {
        break missingId;
      }

      id = R.id.tvAliPersonalBalance;
      TextView tvAliPersonalBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvAliPersonalBalance == null) {
        break missingId;
      }

      id = R.id.tvAliShare;
      TextView tvAliShare = ViewBindings.findChildViewById(rootView, id);
      if (tvAliShare == null) {
        break missingId;
      }

      id = R.id.tvAliWithdrawals;
      TextView tvAliWithdrawals = ViewBindings.findChildViewById(rootView, id);
      if (tvAliWithdrawals == null) {
        break missingId;
      }

      id = R.id.tvLastCalculated;
      TextView tvLastCalculated = ViewBindings.findChildViewById(rootView, id);
      if (tvLastCalculated == null) {
        break missingId;
      }

      id = R.id.tvMiladCardBalance;
      TextView tvMiladCardBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvMiladCardBalance == null) {
        break missingId;
      }

      id = R.id.tvMiladCashBalance;
      TextView tvMiladCashBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvMiladCashBalance == null) {
        break missingId;
      }

      id = R.id.tvMiladFinalBalance;
      TextView tvMiladFinalBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvMiladFinalBalance == null) {
        break missingId;
      }

      id = R.id.tvMiladPersonalBalance;
      TextView tvMiladPersonalBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvMiladPersonalBalance == null) {
        break missingId;
      }

      id = R.id.tvMiladShare;
      TextView tvMiladShare = ViewBindings.findChildViewById(rootView, id);
      if (tvMiladShare == null) {
        break missingId;
      }

      id = R.id.tvMiladWithdrawals;
      TextView tvMiladWithdrawals = ViewBindings.findChildViewById(rootView, id);
      if (tvMiladWithdrawals == null) {
        break missingId;
      }

      id = R.id.tvNetProfit;
      TextView tvNetProfit = ViewBindings.findChildViewById(rootView, id);
      if (tvNetProfit == null) {
        break missingId;
      }

      id = R.id.tvTotalExpenses;
      TextView tvTotalExpenses = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalExpenses == null) {
        break missingId;
      }

      id = R.id.tvTotalSales;
      TextView tvTotalSales = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSales == null) {
        break missingId;
      }

      return new ActivityReportsBinding((ScrollView) rootView, tvAliCardBalance, tvAliCashBalance,
          tvAliFinalBalance, tvAliPersonalBalance, tvAliShare, tvAliWithdrawals, tvLastCalculated,
          tvMiladCardBalance, tvMiladCashBalance, tvMiladFinalBalance, tvMiladPersonalBalance,
          tvMiladShare, tvMiladWithdrawals, tvNetProfit, tvTotalExpenses, tvTotalSales);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
