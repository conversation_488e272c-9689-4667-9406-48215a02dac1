[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_bg_status_approved.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\bg_status_approved.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_chart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_chart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-pngs-49:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_item_notification_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\item_notification_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_rounded_background_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\rounded_background_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_lock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_lock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_withdrawal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_withdrawal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_button_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\button_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_rounded_background_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\rounded_background_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_nav_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\nav_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\menu_activity_main_drawer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\menu\\activity_main_drawer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-pngs-49:\\drawable-xxxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_receipt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_receipt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_gradient_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\gradient_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_reports.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_reports.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\navigation_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\navigation\\nav_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_dialog_theme_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\dialog_theme_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_money.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_money.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_lock_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_lock_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_circle_background_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\circle_background_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_expense.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_expense.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-pngs-49:\\drawable-xxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_account_balance.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_account_balance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_description.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_description.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_trending_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_trending_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_nav_header_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\nav_header_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_visibility_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_visibility_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_camera.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_camera.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_balance.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_balance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_check.xml"}, {"merged": "com.example.ma.app-merged_res-54:/layout_item_notification_new.xml.flat", "source": "com.example.ma.app-main-56:/layout/item_notification_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_notification_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\notification_badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_notifications.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_item_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\item_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_email.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_email.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_bar_chart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_bar_chart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_reports.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_reports.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\spinner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_rounded_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\rounded_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_item_transaction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\item_transaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_rounded_background_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\rounded_background_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-pngs-49:\\drawable-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_trending_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_trending_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_storage.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_storage.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_person_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_person_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_transactions.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_transactions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable-mdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-pngs-49:\\drawable-mdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_logout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_logout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_clear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_clear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_inventory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_inventory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_database_setup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_database_setup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_statistics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_statistics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_withdrawal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_withdrawal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\layout_activity_financial.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_financial.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_inventory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_inventory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_selector_radio_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\selector_radio_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_expenses.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_expenses.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable-ldpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-pngs-49:\\drawable-ldpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_palette.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_palette.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_visibility.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_visibility.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_bg_amount_display.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\bg_amount_display.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_transactions.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_transactions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-pngs-49:\\drawable-xhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_phone.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_phone.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-merged_res-54:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\mipmap-xhdpi\\ic_launcher.webp"}]