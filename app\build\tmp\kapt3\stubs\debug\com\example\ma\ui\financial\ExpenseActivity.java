package com.example.ma.ui.financial;

/**
 * صفحه ثبت هزینه (مشارکتی یا عملیاتی)
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\n\u0010\u000e\u001a\u0004\u0018\u00010\tH\u0002J\u0012\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u0014J\b\u0010\u0013\u001a\u00020\u0010H\u0002J\b\u0010\u0014\u001a\u00020\u0010H\u0002J\b\u0010\u0015\u001a\u00020\u0010H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/example/ma/ui/financial/ExpenseActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "btnSubmit", "Lcom/google/android/material/button/MaterialButton;", "etAmount", "Lcom/google/android/material/textfield/TextInputEditText;", "etDescription", "expenseType", "", "spinnerCategory", "Landroid/widget/AutoCompleteTextView;", "tvAmountInWords", "Landroid/widget/TextView;", "getCurrentUserId", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "setupCategorySpinner", "setupUI", "submitExpense", "app_debug"})
public final class ExpenseActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.google.android.material.textfield.TextInputEditText etAmount;
    private com.google.android.material.textfield.TextInputEditText etDescription;
    private android.widget.AutoCompleteTextView spinnerCategory;
    private com.google.android.material.button.MaterialButton btnSubmit;
    private android.widget.TextView tvAmountInWords;
    @org.jetbrains.annotations.NotNull
    private java.lang.String expenseType = "business";
    
    public ExpenseActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void setupCategorySpinner() {
    }
    
    private final void submitExpense() {
    }
    
    private final java.lang.String getCurrentUserId() {
        return null;
    }
}