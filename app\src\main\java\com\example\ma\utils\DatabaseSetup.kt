package com.example.ma.utils

import com.example.ma.data.remote.SupabaseClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * کلاس کمکی برای راه‌اندازی دیتابیس
 */
object DatabaseSetup {

    /**
     * ایجاد جداول مورد نیاز در Supabase
     */
    suspend fun createTables(): Boolean = withContext(Dispatchers.IO) {
        try {
            // ایجاد جدول notifications
            val createNotificationsTable = """
                CREATE TABLE IF NOT EXISTS notifications (
                    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                    from_user_id VARCHAR(50) NOT NULL,
                    to_user_id VARCHAR(50) NOT NULL,
                    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
                    amount DECIMAL(15,2) NOT NULL,
                    product_count INTEGER DEFAULT NULL,
                    description TEXT DEFAULT NULL,
                    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    read_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
                );
            """.trimIndent()

            // ایجاد جدول transactions
            val createTransactionsTable = """
                CREATE TABLE IF NOT EXISTS transactions (
                    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                    notification_id UUID REFERENCES notifications(id),
                    user_id VARCHAR(50) NOT NULL,
                    partner_id VARCHAR(50) NOT NULL,
                    type VARCHAR(20) NOT NULL CHECK (type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
                    amount DECIMAL(15,2) NOT NULL,
                    product_count INTEGER DEFAULT NULL,
                    description TEXT DEFAULT NULL,
                    approved_by VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """.trimIndent()

            // اجرای SQL ها
            val result1 = SupabaseClient.callFunction("execute_sql", mapOf("sql" to createNotificationsTable))
            val result2 = SupabaseClient.callFunction("execute_sql", mapOf("sql" to createTransactionsTable))

            // اگر function موجود نباشد، از طریق REST API امتحان کن
            if (result1 == null && result2 == null) {
                return@withContext createTablesViaREST()
            }

            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * ایجاد جداول از طریق REST API
     */
    private suspend fun createTablesViaREST(): Boolean {
        return try {
            // تست اتصال ساده
            try {
                SupabaseClient.get("notifications?limit=1") { result ->
                    // callback handled
                }
                true
            } catch (e: Exception) {
                println("جداول احتمالاً وجود ندارند. لطفاً مستقیماً در Supabase SQL Editor اجرا کنید:")
                println(getSQLScript())
                false
            }
        } catch (e: Exception) {
            println("خطا در تست جداول: ${e.message}")
            false
        }
    }

    /**
     * اضافه کردن داده‌های نمونه
     */
    suspend fun insertSampleData(): Boolean = withContext(Dispatchers.IO) {
        try {
            val sampleNotifications = listOf(
                mapOf(
                    "from_user_id" to "miladnasiri",
                    "to_user_id" to "alikakai",
                    "transaction_type" to "sale",
                    "amount" to 2500000.0,
                    "description" to "فروش محصولات ماه جاری"
                ),
                mapOf(
                    "from_user_id" to "alikakai",
                    "to_user_id" to "miladnasiri",
                    "transaction_type" to "withdrawal",
                    "amount" to 1000000.0,
                    "description" to "برداشت شخصی"
                ),
                mapOf(
                    "from_user_id" to "miladnasiri",
                    "to_user_id" to "alikakai",
                    "transaction_type" to "expense",
                    "amount" to 500000.0,
                    "description" to "هزینه اجاره مغازه"
                ),
                mapOf(
                    "from_user_id" to "alikakai",
                    "to_user_id" to "miladnasiri",
                    "transaction_type" to "purchase",
                    "amount" to 1500000.0,
                    "description" to "خرید کالا از تامین کننده"
                )
            )

            var allSuccess = true
            sampleNotifications.forEach { notification ->
                SupabaseClient.post("notifications", notification) { success ->
                    if (!success) allSuccess = false
                }
            }

            allSuccess
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * دریافت اسکریپت SQL کامل
     */
    fun getSQLScript(): String {
        return """
            -- ایجاد جداول برای سیستم اعلانات حسابداری شراکتی
            
            -- جدول اعلانات
            CREATE TABLE IF NOT EXISTS notifications (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                from_user_id VARCHAR(50) NOT NULL,
                to_user_id VARCHAR(50) NOT NULL,
                transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
                amount DECIMAL(15,2) NOT NULL,
                product_count INTEGER DEFAULT NULL,
                description TEXT DEFAULT NULL,
                status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                read_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
            );
            
            -- جدول تراکنش‌های تایید شده
            CREATE TABLE IF NOT EXISTS transactions (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                notification_id UUID REFERENCES notifications(id),
                user_id VARCHAR(50) NOT NULL,
                partner_id VARCHAR(50) NOT NULL,
                type VARCHAR(20) NOT NULL CHECK (type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
                amount DECIMAL(15,2) NOT NULL,
                product_count INTEGER DEFAULT NULL,
                description TEXT DEFAULT NULL,
                approved_by VARCHAR(50) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            -- ایندکس‌ها برای بهبود عملکرد
            CREATE INDEX IF NOT EXISTS idx_notifications_to_user ON notifications(to_user_id);
            CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
            CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(transaction_type);
            CREATE INDEX IF NOT EXISTS idx_notifications_created ON notifications(created_at DESC);
            CREATE INDEX IF NOT EXISTS idx_transactions_user ON transactions(user_id);
            CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
            
            -- تریگر برای بروزرسانی updated_at
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS ${'$'}${'$'}
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            ${'$'}${'$'} language 'plpgsql';
            
            CREATE TRIGGER update_notifications_updated_at 
                BEFORE UPDATE ON notifications 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """.trimIndent()
    }
}
