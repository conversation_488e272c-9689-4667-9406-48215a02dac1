import kotlinx.coroutines.runBlocking
import com.example.ma.data.remote.SupabaseClient

fun main() {
    runBlocking {
        // تست اتصال به Supabase
        val result = SupabaseClient.callFunction("test_connection", emptyMap())
        println("Connection test result: $result")
        
        // تست ایجاد جدول notifications
        val createTableSQL = """
            CREATE TABLE IF NOT EXISTS notifications (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                from_user_id VARCHAR(50) NOT NULL,
                to_user_id VARCHAR(50) NOT NULL,
                transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
                amount DECIMAL(15,2) NOT NULL,
                product_count INTEGER DEFAULT NULL,
                description TEXT DEFAULT NULL,
                status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """.trimIndent()
        
        val createResult = SupabaseClient.callFunction("execute_sql", mapOf("sql" to createTableSQL))
        println("Create table result: $createResult")
    }
}
