package com.example.ma.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0003\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u0012\u0010\u0000\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0001\"\u0004\b\u0000\u0010\u0002\u001a;\u0010\u0003\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0001\"\u0004\b\u0000\u0010\u00022\u001c\u0010\u0004\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0005H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\b\u001a\u0016\u0010\t\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\u00020\n\u001a\u0016\u0010\u000b\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\u00020\f\u001a\u001b\u0010\r\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\u0002H\u0002\u00a2\u0006\u0002\u0010\u000e\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u000f"}, d2 = {"loading", "Lcom/example/ma/data/model/ApiResult;", "T", "safeApiCall", "apiCall", "Lkotlin/Function1;", "Lkotlin/coroutines/Continuation;", "", "(Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "toApiResult", "", "toError", "Lcom/example/ma/data/model/ApiError;", "toSuccess", "(Ljava/lang/Object;)Lcom/example/ma/data/model/ApiResult;", "app_debug"})
public final class ApiResultKt {
    
    /**
     * ایجاد نتیجه موفق
     */
    @org.jetbrains.annotations.NotNull
    public static final <T extends java.lang.Object>com.example.ma.data.model.ApiResult<T> toSuccess(T $this$toSuccess) {
        return null;
    }
    
    /**
     * ایجاد نتیجه خطا
     */
    @org.jetbrains.annotations.NotNull
    public static final <T extends java.lang.Object>com.example.ma.data.model.ApiResult<T> toError(@org.jetbrains.annotations.NotNull
    com.example.ma.data.model.ApiError $this$toError) {
        return null;
    }
    
    /**
     * ایجاد نتیجه بارگذاری
     */
    @org.jetbrains.annotations.NotNull
    public static final <T extends java.lang.Object>com.example.ma.data.model.ApiResult<T> loading() {
        return null;
    }
    
    /**
     * تبدیل Exception به ApiResult
     */
    @org.jetbrains.annotations.NotNull
    public static final <T extends java.lang.Object>com.example.ma.data.model.ApiResult<T> toApiResult(@org.jetbrains.annotations.NotNull
    java.lang.Throwable $this$toApiResult) {
        return null;
    }
    
    /**
     * اجرای safe API call
     */
    @org.jetbrains.annotations.Nullable
    public static final <T extends java.lang.Object>java.lang.Object safeApiCall(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super T>, ? extends java.lang.Object> apiCall, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.ma.data.model.ApiResult<? extends T>> $completion) {
        return null;
    }
}