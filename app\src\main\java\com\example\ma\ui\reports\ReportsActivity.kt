package com.example.ma.ui.reports

import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.utils.AccountingEngine
import com.example.ma.utils.CurrencyFormatter
import kotlinx.coroutines.launch

/**
 * صفحه گزارشات مالی کامل
 */
class ReportsActivity : AppCompatActivity() {

    // Views
    private lateinit var tvTotalSales: TextView
    private lateinit var tvTotalExpenses: TextView
    private lateinit var tvNetProfit: TextView
    private lateinit var tvMiladShare: TextView
    private lateinit var tvAliShare: TextView
    private lateinit var tvMiladWithdrawals: TextView
    private lateinit var tvAliWithdrawals: TextView
    private lateinit var tvMiladFinalBalance: TextView
    private lateinit var tvAliFinalBalance: TextView
    private lateinit var tvMiladCashBalance: TextView
    private lateinit var tvMiladCardBalance: TextView
    private lateinit var tvMiladPersonalBalance: TextView
    private lateinit var tvAliCashBalance: TextView
    private lateinit var tvAliCardBalance: TextView
    private lateinit var tvAliPersonalBalance: TextView
    private lateinit var tvLastCalculated: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_reports)
        
        initViews()
        setupToolbar()
        loadFinancialReports()
    }

    private fun initViews() {
        tvTotalSales = findViewById(R.id.tvTotalSales)
        tvTotalExpenses = findViewById(R.id.tvTotalExpenses)
        tvNetProfit = findViewById(R.id.tvNetProfit)
        tvMiladShare = findViewById(R.id.tvMiladShare)
        tvAliShare = findViewById(R.id.tvAliShare)
        tvMiladWithdrawals = findViewById(R.id.tvMiladWithdrawals)
        tvAliWithdrawals = findViewById(R.id.tvAliWithdrawals)
        tvMiladFinalBalance = findViewById(R.id.tvMiladFinalBalance)
        tvAliFinalBalance = findViewById(R.id.tvAliFinalBalance)
        tvMiladCashBalance = findViewById(R.id.tvMiladCashBalance)
        tvMiladCardBalance = findViewById(R.id.tvMiladCardBalance)
        tvMiladPersonalBalance = findViewById(R.id.tvMiladPersonalBalance)
        tvAliCashBalance = findViewById(R.id.tvAliCashBalance)
        tvAliCardBalance = findViewById(R.id.tvAliCardBalance)
        tvAliPersonalBalance = findViewById(R.id.tvAliPersonalBalance)
        tvLastCalculated = findViewById(R.id.tvLastCalculated)
    }

    private fun setupToolbar() {
        supportActionBar?.apply {
            title = "گزارشات مالی"
            setDisplayHomeAsUpEnabled(true)
        }
    }

    private fun loadFinancialReports() {
        lifecycleScope.launch {
            try {
                println("📊 ReportsActivity: بارگذاری گزارشات مالی")
                
                // محاسبه وضعیت مالی کامل
                val financialStatus = AccountingEngine.calculateCompleteFinancialStatus()
                
                // نمایش اطلاعات کلی
                tvTotalSales.text = CurrencyFormatter.formatToToman(financialStatus.totalSales)
                tvTotalExpenses.text = CurrencyFormatter.formatToToman(financialStatus.totalExpenses)
                tvNetProfit.text = CurrencyFormatter.formatToToman(financialStatus.netProfit)
                
                // نمایش سهم شرکا
                tvMiladShare.text = CurrencyFormatter.formatToToman(financialStatus.miladShare)
                tvAliShare.text = CurrencyFormatter.formatToToman(financialStatus.aliShare)
                
                // نمایش برداشت‌ها
                tvMiladWithdrawals.text = CurrencyFormatter.formatToToman(financialStatus.miladWithdrawals)
                tvAliWithdrawals.text = CurrencyFormatter.formatToToman(financialStatus.aliWithdrawals)
                
                // نمایش موجودی نهایی
                tvMiladFinalBalance.text = CurrencyFormatter.formatToToman(financialStatus.miladFinalBalance)
                tvAliFinalBalance.text = CurrencyFormatter.formatToToman(financialStatus.aliFinalBalance)
                
                // نمایش موجودی حساب‌های میلاد
                tvMiladCashBalance.text = CurrencyFormatter.formatToToman(financialStatus.miladAccounts.cash)
                tvMiladCardBalance.text = CurrencyFormatter.formatToToman(financialStatus.miladAccounts.card)
                tvMiladPersonalBalance.text = CurrencyFormatter.formatToToman(financialStatus.miladAccounts.personal)
                
                // نمایش موجودی حساب‌های علی
                tvAliCashBalance.text = CurrencyFormatter.formatToToman(financialStatus.aliAccounts.cash)
                tvAliCardBalance.text = CurrencyFormatter.formatToToman(financialStatus.aliAccounts.card)
                tvAliPersonalBalance.text = CurrencyFormatter.formatToToman(financialStatus.aliAccounts.personal)
                
                // نمایش زمان آخرین محاسبه
                val lastCalculatedDate = java.text.SimpleDateFormat("yyyy/MM/dd - HH:mm", java.util.Locale.getDefault())
                    .format(java.util.Date(financialStatus.lastCalculated))
                tvLastCalculated.text = "آخرین بروزرسانی: $lastCalculatedDate"
                
                println("✅ ReportsActivity: گزارشات بارگذاری شد")
                
            } catch (e: Exception) {
                println("❌ ReportsActivity: خطا در بارگذاری گزارشات = ${e.message}")
                e.printStackTrace()
                
                // نمایش پیام خطا
                showErrorMessage("خطا در بارگذاری گزارشات مالی")
            }
        }
    }

    private fun showErrorMessage(message: String) {
        // نمایش پیام خطا در UI
        tvTotalSales.text = "خطا"
        tvTotalExpenses.text = "خطا"
        tvNetProfit.text = "خطا"
        tvLastCalculated.text = message
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
