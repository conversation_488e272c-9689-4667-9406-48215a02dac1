package com.example.ma

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.view.View
import android.widget.*
import com.google.android.material.textfield.TextInputEditText

import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GravityCompat

import androidx.drawerlayout.widget.DrawerLayout
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import com.example.ma.utils.CoroutineManager
import com.example.ma.utils.createCoroutineManager
import com.example.ma.data.model.TransactionType
import com.example.ma.data.model.User
import com.example.ma.data.repository.AuthRepository
import com.example.ma.data.remote.SupabaseClient
import com.example.ma.ui.auth.LoginActivity
import com.example.ma.ui.main.MainViewModel
import com.example.ma.ui.profile.ProfileActivity
import com.example.ma.ui.dialogs.ThemeSelectionDialog
import com.example.ma.utils.CurrencyFormatter
import com.example.ma.utils.ProfileManager
import com.example.ma.utils.ThemeManager
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.button.MaterialButton
import com.google.android.material.navigation.NavigationView
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import java.io.File
import kotlinx.coroutines.launch
import com.example.ma.utils.SimpleCurrencyTextWatcher
import android.text.Editable
import android.text.TextWatcher

class MainActivity : AppCompatActivity() {

    private lateinit var authRepository: AuthRepository
    private lateinit var viewModel: MainViewModel
    private lateinit var drawerLayout: DrawerLayout
    private lateinit var profileManager: ProfileManager
    private lateinit var sharedPreferences: SharedPreferences
    private var currentUser: User? = null

    // Coroutine Manager برای مدیریت بهتر coroutines
    private lateinit var coroutineManager: CoroutineManager

    // UI Elements
    private lateinit var tvHeaderUserName: TextView
    private lateinit var etBottleCount: TextInputEditText
    private lateinit var etPrice: TextInputEditText
    private lateinit var etDescription: TextInputEditText
    private lateinit var tvPriceInWords: TextView
    private lateinit var tvTotalAmount: TextView
    private lateinit var rbCash: RadioButton
    private lateinit var rbCard: RadioButton
    private lateinit var tvReceiverLabel: TextView
    private lateinit var spinnerReceiver: Spinner
    private lateinit var btnRegisterTransaction: MaterialButton

    // Quick Action Cards
    private lateinit var cardStats: View
    private lateinit var cardTransactions: View
    private lateinit var cardFinancial: View
    private lateinit var cardInventory: View

    override fun onCreate(savedInstanceState: Bundle?) {
        // اعمال تم ذخیره شده قبل از setContentView
        ThemeManager.applySavedTheme(this)

        super.onCreate(savedInstanceState)

        // Initialize components first
        authRepository = AuthRepository(this)
        profileManager = ProfileManager(this)
        sharedPreferences = getSharedPreferences("user_profile", Context.MODE_PRIVATE)
        coroutineManager = createCoroutineManager()

        // بررسی وضعیت ورود کاربر
        if (!authRepository.isLoggedIn()) {
            navigateToLogin()
            return
        }

        // دریافت کاربر فعلی
        currentUser = authRepository.getCurrentUserSync()
        if (currentUser == null) {
            navigateToLogin()
            return
        }

        setContentView(R.layout.activity_main)

        // Initialize UI components
        initializeViews()

        // راه‌اندازی ViewModel
        viewModel = ViewModelProvider(this).get(MainViewModel::class.java)

        setupUI()
        observeViewModel()
        loadData()

        // شروع real-time sync عکس‌های پروفایل
        startProfileImageSync()
    }

    private fun initializeViews() {
        // Initialize DrawerLayout
        drawerLayout = findViewById(R.id.drawer_layout)

        // Initialize header components
        tvHeaderUserName = findViewById(R.id.tvHeaderUserName)
    }

    private fun setupUI() {
        // پیدا کردن View ها
        etBottleCount = findViewById(R.id.etBottleCount)
        etPrice = findViewById(R.id.etPrice)
        etDescription = findViewById(R.id.etDescription)
        tvPriceInWords = findViewById(R.id.tvPriceInWords)
        tvTotalAmount = findViewById(R.id.tvTotalAmount)
        rbCash = findViewById(R.id.rbCash)
        rbCard = findViewById(R.id.rbCard)
        tvReceiverLabel = findViewById(R.id.tvReceiverLabel)
        spinnerReceiver = findViewById(R.id.spinnerReceiver)
        btnRegisterTransaction = findViewById(R.id.btnRegisterTransaction)

        // Quick Action Cards
        cardStats = findViewById(R.id.cardStats)
        cardTransactions = findViewById(R.id.cardTransactions)
        cardFinancial = findViewById(R.id.cardFinancial)
        cardInventory = findViewById(R.id.cardInventory)

        // نمایش کاربر فعلی
        updateUserDisplay()

        // بروزرسانی اطلاعات کاربر از دیتابیس
        lifecycleScope.launch {
            syncUserDataFromDatabase()

            // بازیابی عکس پروفایل از Supabase اگر وجود نداره
            println("🔍 MainActivity: بررسی عکس پروفایل")
            val hasImage = profileManager.hasProfileImage()
            println("🔍 MainActivity: hasProfileImage = $hasImage")

            if (!hasImage) {
                println("🔍 MainActivity: عکس پروفایل موجود نیست، تلاش برای بازیابی از Supabase")
                val imageLoaded = profileManager.loadProfileImageFromSupabase()
                println("🔍 MainActivity: loadProfileImageFromSupabase result = $imageLoaded")

                if (imageLoaded) {
                    println("✅ MainActivity: عکس پروفایل بازیابی شد، بروزرسانی UI")
                    runOnUiThread {
                        loadProfileImageToView(findViewById(R.id.ivProfileImage))
                        updateNavigationHeader()
                    }
                } else {
                    println("❌ MainActivity: عکس پروفایل بازیابی نشد")
                }
            } else {
                println("✅ MainActivity: عکس پروفایل از قبل موجود است")
            }
        }

        // بروزرسانی header منو بعد از تنظیم UI
        findViewById<View>(android.R.id.content).post {
            updateNavigationHeader()
        }

        // تنظیم Spinner برای انتخاب پرداخت‌کننده
        setupReceiverSpinner()

        // تنظیم کلیک‌های Quick Actions
        setupQuickActions()

        // تنظیم کارت‌های پرداخت
        setupPaymentCards()

        // تنظیم منوی همبرگری
        findViewById<View>(R.id.cardMenu)?.setOnClickListener {
            drawerLayout.openDrawer(GravityCompat.START)
        }

        // تنظیم دکمه اعلانات
        findViewById<View>(R.id.cardNotifications)?.setOnClickListener {
            openNotificationActivity()
        }

        // تنظیم کلیک عکس پروفایل
        findViewById<View>(R.id.cardProfileImage)?.setOnClickListener {
            openProfileActivity()
        }

        // تنظیم navigation menu
        setupNavigationMenu()

        // تنظیم کارت‌های انتخاب پرداخت
        setupPaymentCards()

        // تنظیم TextWatcher ها برای قیمت‌گذاری
        setupPriceWatchers()

        // تنظیم پیش‌فرض نقدی
        rbCash.isChecked = true
        tvReceiverLabel.text = "پرداخت به"
        tvReceiverLabel.visibility = View.VISIBLE
        spinnerReceiver.visibility = View.VISIBLE

        // دکمه ثبت تراکنش
        btnRegisterTransaction.setOnClickListener {
            registerTransaction()
        }
    }

    /**
     * تنظیم TextWatcher ها برای قیمت‌گذاری هوشمند
     */
    private fun setupPriceWatchers() {
        // TextWatcher ساده برای قیمت
        etPrice.addTextChangedListener(
            SimpleCurrencyTextWatcher(
                editText = etPrice,
                displayTextView = tvPriceInWords,
                onAmountChanged = {
                    calculateTotalAmount()
                }
            )
        )

        // TextWatcher برای تعداد محصول
        etBottleCount.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                calculateTotalAmount()
            }
        })
    }

    /**
     * محاسبه و نمایش مبلغ کل
     */
    private fun calculateTotalAmount() {
        try {
            val bottleCountStr = etBottleCount.text.toString().trim()
            val priceStr = etPrice.text.toString().replace(",", "").replace("٬", "").trim()

            if (bottleCountStr.isNotEmpty() && priceStr.isNotEmpty() &&
                bottleCountStr.all { it.isDigit() } && priceStr.all { it.isDigit() }) {

                val bottleCount = bottleCountStr.toIntOrNull() ?: 0
                val price = priceStr.toDoubleOrNull() ?: 0.0

                if (bottleCount > 0 && price > 0) {
                    val totalAmount = bottleCount * price
                    tvTotalAmount.text = "مبلغ کل: ${CurrencyFormatter.formatToToman(totalAmount)}"
                    tvTotalAmount.visibility = View.VISIBLE
                } else {
                    tvTotalAmount.visibility = View.GONE
                }
            } else {
                tvTotalAmount.visibility = View.GONE
            }
        } catch (e: Exception) {
            tvTotalAmount.visibility = View.GONE
        }
    }



    private fun observeViewModel() {
        // TODO: مشاهده تغییرات ViewModel
    }

    private fun loadData() {
        // TODO: بارگذاری داده‌ها از سرور
        // TODO: محاسبه آمارها در آینده
    }

    private fun registerTransaction() {
        val bottleCountStr = etBottleCount.text.toString().trim()
        val priceStr = etPrice.text.toString().trim()
        val description = etDescription.text.toString().trim()

        // اعتبارسنجی
        if (!validateInput(bottleCountStr, priceStr)) {
            return
        }

        val bottleCount = bottleCountStr.toInt()
        // حذف همه کاراکترهای غیرعددی
        val cleanPriceStr = priceStr
            .replace(",", "")      // کاما انگلیسی
            .replace("٬", "")      // کاما فارسی
            .replace("،", "")      // ویرگول فارسی
            .replace(" ", "")      // فاصله
            .replace("\u00A0", "") // non-breaking space
            .trim()

        val price = try {
            cleanPriceStr.toDouble()
        } catch (e: NumberFormatException) {
            Toast.makeText(this, "خطا در تبدیل قیمت: ${e.message}", Toast.LENGTH_LONG).show()
            return
        }

        val totalAmount = bottleCount * price

        val paymentType = when {
            rbCash.isChecked -> "نقدی"
            rbCard.isChecked -> "کارت به کارت"
            else -> {
                Toast.makeText(this, "لطفاً نوع پرداخت را انتخاب کنید", Toast.LENGTH_SHORT).show()
                return
            }
        }

        val receiver = spinnerReceiver.selectedItem?.toString()
        if (receiver == null || receiver.isEmpty()) {
            Toast.makeText(this, getString(R.string.please_select_receiver), Toast.LENGTH_SHORT).show()
            return
        }

        // ثبت تراکنش با مدیریت بهتر coroutine
        coroutineManager.launchMain("register_transaction") {
            val transactionDescription = if (description.isNotEmpty()) {
                "فروش $bottleCount بطری - $paymentType - $receiver - توضیحات: $description"
            } else {
                "فروش $bottleCount بطری - $paymentType - $receiver"
            }

            // استفاده از username بجای UUID
            val currentUsername = getCurrentUsername()
            println("🔍 MainActivity.registerTransaction: currentUsername = $currentUsername")

            if (currentUsername == null) {
                println("❌ MainActivity.registerTransaction: کاربر شناسایی نشد")

                // راه‌حل موقت: استفاده از username پیش‌فرض
                val defaultUsername = "Alikakai" // یا از کاربر بپرسید
                println("🔧 MainActivity.registerTransaction: استفاده از username پیش‌فرض: $defaultUsername")

                // ادامه با username پیش‌فرض
                val success = viewModel.registerSaleTransaction(
                    amount = totalAmount,
                    description = transactionDescription,
                    userId = defaultUsername,
                    bottleCount = bottleCount,
                    paymentType = paymentType,
                    receiver = receiver
                )

                if (success) {
                    runOnUiThread {
                        Toast.makeText(this@MainActivity, "تراکنش با موفقیت ثبت شد", Toast.LENGTH_SHORT).show()
                        clearInputs()
                    }
                } else {
                    runOnUiThread {
                        Toast.makeText(this@MainActivity, "خطا در ثبت تراکنش", Toast.LENGTH_SHORT).show()
                    }
                }
                return@launchMain
            }

            println("🔍 MainActivity.registerTransaction: شروع ثبت تراکنش")
            println("🔍 MainActivity.registerTransaction: amount = $totalAmount")
            println("🔍 MainActivity.registerTransaction: description = $transactionDescription")
            println("🔍 MainActivity.registerTransaction: userId = $currentUsername")

            val success = viewModel.registerSaleTransaction(
                amount = totalAmount,
                description = transactionDescription,
                userId = currentUsername, // استفاده از username
                bottleCount = bottleCount,
                paymentType = paymentType,
                receiver = receiver
            )

            println("🔍 MainActivity.registerTransaction: success = $success")

            if (success) {
                runOnUiThread {
                    Toast.makeText(this@MainActivity, getString(R.string.transaction_sent_for_approval), Toast.LENGTH_LONG).show()
                    clearForm()
                    loadData() // بروزرسانی آمارها
                }
            } else {
                runOnUiThread {
                    Toast.makeText(this@MainActivity, getString(R.string.connection_error), Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun validateInput(bottleCountStr: String, priceStr: String): Boolean {
        if (bottleCountStr.isEmpty()) {
            etBottleCount.error = getString(R.string.please_enter_bottle_count)
            etBottleCount.requestFocus()
            return false
        }

        if (priceStr.isEmpty()) {
            etPrice.error = getString(R.string.please_enter_price)
            etPrice.requestFocus()
            return false
        }

        try {
            // بررسی تعداد بطری
            val bottleCount = try {
                bottleCountStr.toInt()
            } catch (e: NumberFormatException) {
                etBottleCount.error = "تعداد نامعتبر"
                etBottleCount.requestFocus()
                return false
            }

            // حذف همه کاراکترهای غیرعددی به جز نقطه
            val cleanPriceStr = priceStr
                .replace(",", "")      // کاما انگلیسی
                .replace("٬", "")      // کاما فارسی
                .replace("،", "")      // ویرگول فارسی
                .replace(" ", "")      // فاصله
                .replace("\u00A0", "") // non-breaking space
                .trim()

            // Debug log
            println("🔍 Debug - Original price: '$priceStr'")
            println("🔍 Debug - Original price bytes: ${priceStr.toByteArray().joinToString { it.toString() }}")
            println("🔍 Debug - Clean price: '$cleanPriceStr'")
            println("🔍 Debug - Clean price bytes: ${cleanPriceStr.toByteArray().joinToString { it.toString() }}")

            // بررسی که فقط عدد باشد
            if (cleanPriceStr.isEmpty() || !cleanPriceStr.matches(Regex("^\\d+(\\.\\d+)?$"))) {
                etPrice.error = "فقط عدد وارد کنید"
                etPrice.requestFocus()
                return false
            }

            // بررسی طول عدد (حداکثر 10 رقم)
            if (cleanPriceStr.length > 10) {
                etPrice.error = "عدد خیلی بزرگ است"
                etPrice.requestFocus()
                return false
            }

            val price = try {
                cleanPriceStr.toDouble()
            } catch (e: NumberFormatException) {
                etPrice.error = "قیمت نامعتبر: ${e.message}"
                etPrice.requestFocus()
                return false
            }

            println("🔍 Debug - Final price: $price")

            if (bottleCount <= 0) {
                etBottleCount.error = "تعداد باید بیشتر از صفر باشد"
                etBottleCount.requestFocus()
                return false
            }

            if (price <= 0) {
                etPrice.error = "قیمت باید بیشتر از صفر باشد"
                etPrice.requestFocus()
                return false
            }

        } catch (e: NumberFormatException) {
            etPrice.error = "عدد نامعتبر: ${e.message}"
            etPrice.requestFocus()
            return false
        }

        return true
    }

    private fun setupPaymentCards() {
        // تنظیم RadioButton ها
        rbCash.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                rbCard.isChecked = false
                tvReceiverLabel.text = "پرداخت به"
                tvReceiverLabel.visibility = View.VISIBLE
                spinnerReceiver.visibility = View.VISIBLE
                setupReceiverSpinner()
            }
        }

        rbCard.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                rbCash.isChecked = false
                tvReceiverLabel.text = "پرداخت به"
                tvReceiverLabel.visibility = View.VISIBLE
                spinnerReceiver.visibility = View.VISIBLE
                setupReceiverSpinner()
            }
        }
    }

    private fun setupReceiverSpinner() {
        // تنظیم گزینه‌های پرداخت‌کننده
        val receivers = arrayOf("علی کاکایی", "میلاد نصیری")
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, receivers)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerReceiver.adapter = adapter

        // تنظیم پیش‌فرض بر اساس کاربر فعلی
        currentUser?.let { user ->
            val defaultSelection = when (user.username.lowercase()) {
                "miladnasiri" -> 1 // میلاد نصیری
                "alikakai" -> 0   // علی کاکایی
                else -> 0
            }
            spinnerReceiver.setSelection(defaultSelection)
        }
    }

    private fun setupQuickActions() {
        // آمار
        cardStats.setOnClickListener {
            // باز کردن صفحه مالی
            val intent = Intent(this, com.example.ma.ui.financial.FinancialActivity::class.java)
            startActivity(intent)
        }

        // تراکنش‌ها
        cardTransactions.setOnClickListener {
            // باز کردن صفحه تراکنش‌ها
            Toast.makeText(this, "صفحه تراکنش‌ها", Toast.LENGTH_SHORT).show()
        }

        // مالی
        cardFinancial.setOnClickListener {
            // باز کردن صفحه مالی
            Toast.makeText(this, "صفحه مالی", Toast.LENGTH_SHORT).show()
        }

        // انبار
        cardInventory.setOnClickListener {
            // باز کردن صفحه انبار
            Toast.makeText(this, "صفحه انبار", Toast.LENGTH_SHORT).show()
        }
    }

    private fun updateUserDisplay() {
        val fullName = profileManager.getFullName()
        val displayName = if (fullName.isNotEmpty()) fullName else "کاربر گرامی"
        tvHeaderUserName.text = displayName

        // بارگذاری عکس پروفایل
        val profileImageView = findViewById<ImageView>(R.id.ivHeaderProfileImage)
        loadProfileImageToView(profileImageView)
    }

    private fun updateNavigationHeader() {
        try {
            val navigationView = findViewById<NavigationView>(R.id.nav_view)
            val headerView = navigationView.getHeaderView(0)

            val navProfileImage = headerView.findViewById<ImageView>(R.id.ivNavProfileImage)
            val navUserName = headerView.findViewById<TextView>(R.id.tvNavUserName)
            val navUserRole = headerView.findViewById<TextView>(R.id.tvNavUserRole)

            // تنظیم نام کاربر
            currentUser?.let { user ->
                navUserName.text = user.displayName
                navUserRole.text = "شریک کسب‌وکار"

                // تنظیم عکس پروفایل
                val profilePrefs = getSharedPreferences("user_profile", MODE_PRIVATE)
                val imagePath = profilePrefs.getString("profile_image", null)

                loadProfileImageToView(navProfileImage)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun syncUserDataFromDatabase() {
        try {
            currentUser?.let { user ->
                // دریافت اطلاعات کامل کاربر از دیتابیس
                val userInfo = withContext(Dispatchers.IO) {
                    authRepository.getUserInfo(user.username)
                }
                userInfo?.let { info ->
                    // ذخیره در ProfileManager
                    val profilePrefs = getSharedPreferences("user_profile", MODE_PRIVATE)
                    profilePrefs.edit().apply {
                        putString("full_name", info.displayName)
                        putString("email", info.email ?: "")
                        putString("phone", info.phone ?: "")
                        putBoolean("profile_completed", true)
                        apply()
                    }

                    // بازیابی عکس پروفایل از Supabase
                    val profileImageFromDB = withContext(Dispatchers.IO) {
                        SupabaseClient.getUserProfileImage(user.username)
                    }
                    if (!profileImageFromDB.isNullOrEmpty()) {
                        profilePrefs.edit().putString("profile_image", profileImageFromDB).apply()
                        println("عکس پروفایل از Supabase بازیابی شد: ${profileImageFromDB.length} کاراکتر")
                    }

                    // بروزرسانی UI
                    runOnUiThread {
                        updateUserDisplay()
                        updateNavigationHeader()
                        // بروزرسانی عکس پروفایل
                        loadProfileImageToView(findViewById(R.id.ivProfileImage))
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setupNavigationMenu() {
        val navigationView = findViewById<NavigationView>(R.id.nav_view)
        navigationView.setNavigationItemSelectedListener { menuItem ->
            when (menuItem.itemId) {
                R.id.nav_home -> {
                    // در حال حاضر در صفحه اصلی هستیم
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_profile -> {
                    openProfileActivity()
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_reports -> {
                    // TODO: باز کردن صفحه گزارشات
                    Toast.makeText(this, "گزارشات - به زودی", Toast.LENGTH_SHORT).show()
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_notifications -> {
                    openNotificationActivity()
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_theme_settings -> {
                    openThemeSelectionDialog()
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_logout -> {
                    logout()
                    true
                }
                else -> false
            }
        }
    }

    private fun logout() {
        authRepository.logout()
        navigateToLogin()
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun openProfileActivity() {
        val intent = Intent(this, ProfileActivity::class.java)
        startActivityForResult(intent, PROFILE_REQUEST_CODE)
    }

    private fun openNotificationActivity() {
        val intent = Intent(this, com.example.ma.ui.notifications.NotificationActivity::class.java)
        startActivity(intent)
    }

    private fun openThemeSelectionDialog() {
        val dialog = ThemeSelectionDialog.newInstance()
        dialog.show(supportFragmentManager, "ThemeSelectionDialog")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PROFILE_REQUEST_CODE && resultCode == RESULT_OK) {
            // بروزرسانی نمایش کاربر بعد از بازگشت از پروفایل
            updateUserDisplay()
            updateNavigationHeader()
        }
    }

    private fun clearForm() {
        etBottleCount.text?.clear()
        etPrice.text?.clear()
        etDescription.text?.clear()
        rbCash.isChecked = false
        rbCard.isChecked = false
        tvReceiverLabel.visibility = View.GONE
        spinnerReceiver.visibility = View.GONE
    }

    /**
     * دریافت نام کاربری فعلی
     */
    private fun getCurrentUsername(): String? {
        return try {
            val authPrefs = getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
            var username = authPrefs.getString("current_username", null)
            println("🔍 MainActivity: Current username from prefs = $username")

            // اگر username موجود نیست، از user_id استخراج کن
            if (username.isNullOrEmpty()) {
                val userId = authPrefs.getString("current_user_id", null)
                println("🔍 MainActivity: Current user_id = $userId")

                username = when (userId) {
                    "ad28ba8f-0fa0-4420-8119-70fcacfd237e" -> {
                        // ذخیره username برای دفعات بعد
                        authPrefs.edit().putString("current_username", "Alikakai").apply()
                        println("✅ MainActivity: Username ذخیره شد: Alikakai")
                        "Alikakai"
                    }
                    "930b5d13-0408-4c57-965b-235c5532b35a" -> {
                        // ذخیره username برای دفعات بعد
                        authPrefs.edit().putString("current_username", "Miladnasiri").apply()
                        println("✅ MainActivity: Username ذخیره شد: Miladnasiri")
                        "Miladnasiri"
                    }
                    else -> {
                        println("❌ MainActivity: User ID ناشناخته: $userId")
                        null
                    }
                }
                println("🔍 MainActivity: Username extracted from user_id = $username")
            }

            return username
        } catch (e: Exception) {
            println("❌ MainActivity: خطا در دریافت username: ${e.message}")
            null
        }
    }

    /**
     * پاک کردن تمام فیلدهای ورودی
     */
    private fun clearInputs() {
        try {
            // پاک کردن فیلدهای متنی
            findViewById<TextInputEditText>(R.id.etBottleCount)?.setText("")
            findViewById<TextInputEditText>(R.id.etPrice)?.setText("")
            findViewById<TextInputEditText>(R.id.etDescription)?.setText("")

            // ریست کردن RadioButton ها به حالت پیش‌فرض (نقدی)
            findViewById<RadioButton>(R.id.rbCash)?.isChecked = true
            findViewById<RadioButton>(R.id.rbCard)?.isChecked = false

            // ریست کردن Spinner به اولین گزینه
            findViewById<Spinner>(R.id.spinnerReceiver)?.setSelection(0)

            // پنهان کردن متن قیمت با حروف
            findViewById<TextView>(R.id.tvPriceInWords)?.visibility = View.GONE

            // پنهان کردن مبلغ کل
            findViewById<TextView>(R.id.tvTotalAmount)?.visibility = View.GONE

            println("✅ فیلدهای ورودی پاک شدند")
        } catch (e: Exception) {
            println("❌ خطا در پاک کردن فیلدها: ${e.message}")
        }
    }

    private fun loadProfileImageToView(imageView: ImageView?) {
        imageView?.let { view ->
            val imagePath = sharedPreferences.getString("profile_image", "")
            println("🔍 MainActivity.loadProfileImageToView: imagePath = '$imagePath'")
            if (!imagePath.isNullOrEmpty()) {
                if (imagePath.startsWith("data:image")) {
                    // عکس base64 از Supabase
                    try {
                        val cleanBase64 = imagePath.replace("data:image/jpeg;base64,", "")
                            .replace("data:image/png;base64,", "")
                        val decodedBytes = android.util.Base64.decode(cleanBase64.toByteArray(), android.util.Base64.DEFAULT)
                        val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)

                        if (bitmap != null) {
                            setProfileImageWithCorrectSettings(view, bitmap)
                        } else {
                            setDefaultProfileImage(view)
                        }
                    } catch (e: Exception) {
                        setDefaultProfileImage(view)
                    }
                } else if (java.io.File(imagePath).exists()) {
                    // عکس محلی
                    val bitmap = BitmapFactory.decodeFile(imagePath)
                    if (bitmap != null) {
                        setProfileImageWithCorrectSettings(view, bitmap)
                    } else {
                        setDefaultProfileImage(view)
                    }
                } else {
                    setDefaultProfileImage(view)
                }
            } else {
                setDefaultProfileImage(view)
            }
        }
    }

    /**
     * تنظیم صحیح عکس پروفایل در ImageView
     * این تابع اطمینان می‌دهد که عکس با تنظیمات مناسب نمایش داده شود
     */
    private fun setProfileImageWithCorrectSettings(imageView: ImageView, bitmap: Bitmap) {
        try {
            // تنظیمات اساسی برای نمایش عکس
            imageView.setImageBitmap(bitmap)
            imageView.setPadding(0, 0, 0, 0)
            imageView.clearColorFilter()
            imageView.scaleType = ImageView.ScaleType.CENTER_CROP

            // حذف tint اگر وجود داشته باشد
            imageView.imageTintList = null

            println("عکس پروفایل با تنظیمات صحیح اعمال شد در MainActivity")
        } catch (e: Exception) {
            e.printStackTrace()
            println("خطا در تنظیم عکس پروفایل در MainActivity: ${e.message}")
        }
    }

    private fun setDefaultProfileImage(imageView: ImageView) {
        imageView.setImageResource(R.drawable.ic_person_modern)
        imageView.setPadding(12, 12, 12, 12)
        imageView.setColorFilter(getColor(R.color.primary_color))
        imageView.scaleType = ImageView.ScaleType.CENTER_INSIDE
    }

    private fun startProfileImageSync() {
        // استفاده از CoroutineManager برای مدیریت بهتر lifecycle
        coroutineManager.launchIO("profile_image_sync") {
            while (isActive) { // بررسی فعال بودن coroutine
                try {
                    syncProfileImages()
                    kotlinx.coroutines.delay(10000) // 10 ثانیه
                } catch (e: Exception) {
                    e.printStackTrace()
                    kotlinx.coroutines.delay(30000) // در صورت خطا، 30 ثانیه صبر کن
                }
            }
        }
    }

    private suspend fun syncProfileImages() {
        try {
            // دریافت عکس‌های جدید از Supabase در background thread
            val aliProfileImage = withContext(Dispatchers.IO) {
                SupabaseClient.getUserProfileImage("Alikakai")
            }
            val miladProfileImage = withContext(Dispatchers.IO) {
                SupabaseClient.getUserProfileImage("Miladnasiri")
            }

            // بروزرسانی عکس‌ها در UI
            runOnUiThread {
                updateProfileImageIfChanged("Alikakai", aliProfileImage)
                updateProfileImageIfChanged("Miladnasiri", miladProfileImage)
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateProfileImageIfChanged(username: String, newProfileImage: String?) {
        try {
            val currentUsername = getCurrentUsername()

            // اگر عکس کاربر فعلی تغییر کرده
            if (username == currentUsername && !newProfileImage.isNullOrEmpty()) {
                val currentStoredImage = sharedPreferences.getString("profile_image", "")

                // اگر عکس جدید متفاوت از عکس ذخیره شده محلی است
                if (newProfileImage != currentStoredImage) {
                    // ذخیره عکس جدید
                    sharedPreferences.edit().putString("profile_image", newProfileImage).apply()

                    // بروزرسانی UI
                    val profileImageView = findViewById<ImageView>(R.id.ivProfileImage)
                    loadProfileImageToView(profileImageView)

                    // بروزرسانی منوی کشویی
                    val navView = findViewById<NavigationView>(R.id.nav_view)
                    val headerView = navView.getHeaderView(0)
                    val navProfileImage = headerView.findViewById<ImageView>(R.id.ivNavProfileImage)
                    loadProfileImageToView(navProfileImage)
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }



    companion object {
        private const val PROFILE_REQUEST_CODE = 1001
    }

    private fun formatBalance(amount: Int): String {
        return if (amount >= 0) {
            "+${String.format("%,d", amount)} تومان"
        } else {
            "${String.format("%,d", amount)} تومان"
        }
    }



    override fun onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START)
        } else {
            super.onBackPressed()
        }
    }
}