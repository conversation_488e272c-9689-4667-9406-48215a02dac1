package com.example.ma.data.local.dao;

/**
 * DAO برای عملیات تراکنش‌ها
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\b\u0005\bg\u0018\u00002\u00020\u0001J+\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\tJ\u0011\u0010\n\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u0019\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u0011H\'J\u0014\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u0011H\'J\u0011\u0010\u0014\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u0014\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u0011H\'J\u0011\u0010\u0016\u001a\u00020\u0017H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u0011\u0010\u0018\u001a\u00020\u0017H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u0011\u0010\u0019\u001a\u00020\u0017H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u001b\u0010\u001a\u001a\u0004\u0018\u00010\u000e2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001bJ\u0011\u0010\u001c\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u001c\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010\u001e\u001a\u00020\u001fH\'J\u001c\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010!\u001a\u00020\"H\'J\u001c\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010$\u001a\u00020\u0005H\'J$\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010&\u001a\u00020\b2\u0006\u0010\'\u001a\u00020\bH\'J\u0017\u0010(\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0012H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u0019\u0010)\u001a\u00020\u00172\u0006\u0010$\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001bJ\u0019\u0010*\u001a\u00020\u00172\u0006\u0010$\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001bJ\u0019\u0010+\u001a\u00020\u00172\u0006\u0010$\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001bJ\u0019\u0010,\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ%\u0010-\u001a\b\u0012\u0004\u0012\u00020\b0\u00122\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0012H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010/J\u001c\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u00101\u001a\u00020\u0005H\'J!\u00102\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u00103\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00104J+\u00105\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u001e\u001a\u0002062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00107J\u0019\u00108\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ+\u00109\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u001e\u001a\u00020\u001f2\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010:\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006;"}, d2 = {"Lcom/example/ma/data/local/dao/TransactionDao;", "", "approveTransaction", "", "transactionId", "", "approvedBy", "timestamp", "", "(Ljava/lang/String;Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAllTransactions", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteTransaction", "transaction", "Lcom/example/ma/data/local/entity/TransactionEntity;", "(Lcom/example/ma/data/local/entity/TransactionEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllTransactions", "Lkotlinx/coroutines/flow/Flow;", "", "getApprovedTransactions", "getPendingTransactionCount", "getPendingTransactions", "getTotalExpenses", "", "getTotalSales", "getTotalWithdrawals", "getTransactionById", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionCount", "getTransactionsByStatus", "status", "Lcom/example/ma/data/local/entity/TransactionStatus;", "getTransactionsByType", "type", "Lcom/example/ma/data/local/entity/TransactionType;", "getTransactionsByUser", "userId", "getTransactionsInRange", "startTime", "endTime", "getTransactionsNeedingSync", "getUserExpenses", "getUserSales", "getUserWithdrawals", "insertTransaction", "insertTransactions", "transactions", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchTransactions", "query", "setRemoteId", "remoteId", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSyncStatus", "Lcom/example/ma/data/local/entity/SyncStatus;", "(Ljava/lang/String;Lcom/example/ma/data/local/entity/SyncStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTransaction", "updateTransactionStatus", "(Ljava/lang/String;Lcom/example/ma/data/local/entity/TransactionStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao
public abstract interface TransactionDao {
    
    /**
     * دریافت همه تراکنش‌ها
     */
    @androidx.room.Query(value = "SELECT * FROM transactions ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.TransactionEntity>> getAllTransactions();
    
    /**
     * دریافت تراکنش‌های کاربر
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE user_id = :userId ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.TransactionEntity>> getTransactionsByUser(@org.jetbrains.annotations.NotNull
    java.lang.String userId);
    
    /**
     * دریافت تراکنش‌ها بر اساس نوع
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE transaction_type = :type ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.TransactionEntity>> getTransactionsByType(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionType type);
    
    /**
     * دریافت تراکنش‌ها بر اساس وضعیت
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE status = :status ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.TransactionEntity>> getTransactionsByStatus(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionStatus status);
    
    /**
     * دریافت تراکنش‌های در انتظار تایید
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE status = \'PENDING\' ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.TransactionEntity>> getPendingTransactions();
    
    /**
     * دریافت تراکنش‌های تایید شده
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE status = \'APPROVED\' ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.TransactionEntity>> getApprovedTransactions();
    
    /**
     * دریافت تراکنش‌های نیازمند همگام‌سازی
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE sync_status != \'SYNCED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTransactionsNeedingSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.example.ma.data.local.entity.TransactionEntity>> $completion);
    
    /**
     * دریافت تراکنش بر اساس ID
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE id = :transactionId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTransactionById(@org.jetbrains.annotations.NotNull
    java.lang.String transactionId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.ma.data.local.entity.TransactionEntity> $completion);
    
    /**
     * درج تراکنش جدید
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertTransaction(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionEntity transaction, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * درج چندین تراکنش
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertTransactions(@org.jetbrains.annotations.NotNull
    java.util.List<com.example.ma.data.local.entity.TransactionEntity> transactions, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion);
    
    /**
     * بروزرسانی تراکنش
     */
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateTransaction(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionEntity transaction, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * حذف تراکنش
     */
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteTransaction(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionEntity transaction, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * بروزرسانی وضعیت تراکنش
     */
    @androidx.room.Query(value = "UPDATE transactions SET status = :status, updated_at = :timestamp WHERE id = :transactionId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateTransactionStatus(@org.jetbrains.annotations.NotNull
    java.lang.String transactionId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionStatus status, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * تایید تراکنش
     */
    @androidx.room.Query(value = "UPDATE transactions SET status = \'APPROVED\', approved_by = :approvedBy, approved_at = :timestamp, updated_at = :timestamp WHERE id = :transactionId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object approveTransaction(@org.jetbrains.annotations.NotNull
    java.lang.String transactionId, @org.jetbrains.annotations.NotNull
    java.lang.String approvedBy, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * بروزرسانی وضعیت همگام‌سازی
     */
    @androidx.room.Query(value = "UPDATE transactions SET sync_status = :status, last_sync = :timestamp WHERE id = :transactionId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateSyncStatus(@org.jetbrains.annotations.NotNull
    java.lang.String transactionId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.SyncStatus status, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * تنظیم remote ID
     */
    @androidx.room.Query(value = "UPDATE transactions SET remote_id = :remoteId WHERE id = :transactionId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object setRemoteId(@org.jetbrains.annotations.NotNull
    java.lang.String transactionId, @org.jetbrains.annotations.NotNull
    java.lang.String remoteId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * محاسبه مجموع فروش‌ها
     */
    @androidx.room.Query(value = "SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE transaction_type = \'SALE\' AND status = \'APPROVED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalSales(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    /**
     * محاسبه مجموع هزینه‌ها
     */
    @androidx.room.Query(value = "SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE transaction_type = \'EXPENSE\' AND status = \'APPROVED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalExpenses(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    /**
     * محاسبه مجموع برداشت‌ها
     */
    @androidx.room.Query(value = "SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE transaction_type = \'WITHDRAWAL\' AND status = \'APPROVED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalWithdrawals(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    /**
     * محاسبه فروش‌های کاربر
     */
    @androidx.room.Query(value = "SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = :userId AND transaction_type = \'SALE\' AND status = \'APPROVED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserSales(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    /**
     * محاسبه هزینه‌های کاربر
     */
    @androidx.room.Query(value = "SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = :userId AND transaction_type = \'EXPENSE\' AND status = \'APPROVED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserExpenses(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    /**
     * محاسبه برداشت‌های کاربر
     */
    @androidx.room.Query(value = "SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = :userId AND transaction_type = \'WITHDRAWAL\' AND status = \'APPROVED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserWithdrawals(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    /**
     * جستجو در تراکنش‌ها
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE description LIKE :query ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.TransactionEntity>> searchTransactions(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    /**
     * دریافت تراکنش‌ها در بازه زمانی
     */
    @androidx.room.Query(value = "SELECT * FROM transactions WHERE created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.TransactionEntity>> getTransactionsInRange(long startTime, long endTime);
    
    /**
     * پاک کردن همه تراکنش‌ها
     */
    @androidx.room.Query(value = "DELETE FROM transactions")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearAllTransactions(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * شمارش تراکنش‌ها
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM transactions")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTransactionCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * شمارش تراکنش‌های در انتظار
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM transactions WHERE status = \'PENDING\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPendingTransactionCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * DAO برای عملیات تراکنش‌ها
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}