package com.example.ma.ui.dialogs;

/**
 * Dialog برای انتخاب تم اپلیکیشن
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u0000 \u00132\u00020\u0001:\u0001\u0013B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\t\u001a\u00020\nH\u0002J\b\u0010\u000b\u001a\u00020\nH\u0002J\u0012\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0016J\u0010\u0010\u0010\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/ma/ui/dialogs/ThemeSelectionDialog;", "Landroidx/fragment/app/DialogFragment;", "()V", "rbDarkTheme", "Landroid/widget/RadioButton;", "rbLightTheme", "rbSystemTheme", "rgThemeMode", "Landroid/widget/RadioGroup;", "applySelectedTheme", "", "loadCurrentTheme", "onCreateDialog", "Landroid/app/Dialog;", "savedInstanceState", "Landroid/os/Bundle;", "setupViews", "view", "Landroid/view/View;", "Companion", "app_debug"})
public final class ThemeSelectionDialog extends androidx.fragment.app.DialogFragment {
    private android.widget.RadioGroup rgThemeMode;
    private android.widget.RadioButton rbLightTheme;
    private android.widget.RadioButton rbDarkTheme;
    private android.widget.RadioButton rbSystemTheme;
    @org.jetbrains.annotations.NotNull
    public static final com.example.ma.ui.dialogs.ThemeSelectionDialog.Companion Companion = null;
    
    public ThemeSelectionDialog() {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public android.app.Dialog onCreateDialog(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final void setupViews(android.view.View view) {
    }
    
    private final void loadCurrentTheme() {
    }
    
    private final void applySelectedTheme() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/example/ma/ui/dialogs/ThemeSelectionDialog$Companion;", "", "()V", "newInstance", "Lcom/example/ma/ui/dialogs/ThemeSelectionDialog;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.ui.dialogs.ThemeSelectionDialog newInstance() {
            return null;
        }
    }
}