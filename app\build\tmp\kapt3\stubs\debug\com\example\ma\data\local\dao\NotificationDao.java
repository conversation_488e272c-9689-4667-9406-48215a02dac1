package com.example.ma.data.local.dao;

/**
 * DAO برای عملیات اعلانات
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u001c\n\u0002\u0018\u0002\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0011\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0019\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u0019\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u001b\u0010\u0010\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\u0011\u0010\u0014\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u0016\u001a\u00020\u0017H\'J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u0019\u001a\u00020\u001aH\'J\u001c\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u001c\u001a\u00020\u0012H\'J$\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u001e\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u000bH\'J\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00070\u000fH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u001c\u001a\u00020\u0012H\'J\u0019\u0010\"\u001a\u00020\u00032\u0006\u0010\u001c\u001a\u00020\u0012H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\u0014\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u0019\u0010$\u001a\u00020\u00032\u0006\u0010\u001c\u001a\u00020\u0012H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013J\u0014\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u0019\u0010&\u001a\u00020\u000b2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ%\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000f2\f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00070\u000fH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010)J#\u0010*\u001a\u00020\u00032\u0006\u0010\u001c\u001a\u00020\u00122\b\b\u0002\u0010+\u001a\u00020\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010,J#\u0010-\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010+\u001a\u00020\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010,J\u001c\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010/\u001a\u00020\u0012H\'J!\u00100\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u00101\u001a\u00020\u0012H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00102J\u0019\u00103\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ+\u00104\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u00020\u00172\b\b\u0002\u0010+\u001a\u00020\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00105J+\u00106\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u0002072\b\b\u0002\u0010+\u001a\u00020\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00108\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u00069"}, d2 = {"Lcom/example/ma/data/local/dao/NotificationDao;", "", "clearAllNotifications", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteNotification", "notification", "Lcom/example/ma/data/local/entity/NotificationEntity;", "(Lcom/example/ma/data/local/entity/NotificationEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldNotifications", "cutoffTime", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllNotifications", "Lkotlinx/coroutines/flow/Flow;", "", "getNotificationById", "notificationId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getNotificationCount", "getNotificationsByStatus", "status", "Lcom/example/ma/data/local/entity/NotificationStatus;", "getNotificationsByTransactionType", "type", "Lcom/example/ma/data/local/entity/TransactionType;", "getNotificationsForUser", "userId", "getNotificationsInRange", "startTime", "endTime", "getNotificationsNeedingSync", "getNotificationsSentByUser", "getPendingCountForUser", "getPendingNotifications", "getUnreadCountForUser", "getUnreadNotifications", "insertNotification", "insertNotifications", "notifications", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markAllAsReadForUser", "timestamp", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markAsRead", "searchNotifications", "query", "setRemoteId", "remoteId", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateNotification", "updateNotificationStatus", "(Ljava/lang/String;Lcom/example/ma/data/local/entity/NotificationStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSyncStatus", "Lcom/example/ma/data/local/entity/SyncStatus;", "(Ljava/lang/String;Lcom/example/ma/data/local/entity/SyncStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao
public abstract interface NotificationDao {
    
    /**
     * دریافت همه اعلانات
     */
    @androidx.room.Query(value = "SELECT * FROM notifications ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> getAllNotifications();
    
    /**
     * دریافت اعلانات کاربر
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE to_user_id = :userId ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> getNotificationsForUser(@org.jetbrains.annotations.NotNull
    java.lang.String userId);
    
    /**
     * دریافت اعلانات ارسالی کاربر
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE from_user_id = :userId ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> getNotificationsSentByUser(@org.jetbrains.annotations.NotNull
    java.lang.String userId);
    
    /**
     * دریافت اعلانات بر اساس وضعیت
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE status = :status ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> getNotificationsByStatus(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.NotificationStatus status);
    
    /**
     * دریافت اعلانات در انتظار
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE status = \'PENDING\' ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> getPendingNotifications();
    
    /**
     * دریافت اعلانات خوانده نشده
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE read_at IS NULL ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> getUnreadNotifications();
    
    /**
     * دریافت اعلانات نیازمند همگام‌سازی
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE sync_status != \'SYNCED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getNotificationsNeedingSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.example.ma.data.local.entity.NotificationEntity>> $completion);
    
    /**
     * دریافت اعلان بر اساس ID
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE id = :notificationId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getNotificationById(@org.jetbrains.annotations.NotNull
    java.lang.String notificationId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.ma.data.local.entity.NotificationEntity> $completion);
    
    /**
     * درج اعلان جدید
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertNotification(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.NotificationEntity notification, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * درج چندین اعلان
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertNotifications(@org.jetbrains.annotations.NotNull
    java.util.List<com.example.ma.data.local.entity.NotificationEntity> notifications, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion);
    
    /**
     * بروزرسانی اعلان
     */
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateNotification(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.NotificationEntity notification, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * حذف اعلان
     */
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteNotification(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.NotificationEntity notification, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * بروزرسانی وضعیت اعلان
     */
    @androidx.room.Query(value = "UPDATE notifications SET status = :status, updated_at = :timestamp WHERE id = :notificationId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateNotificationStatus(@org.jetbrains.annotations.NotNull
    java.lang.String notificationId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.NotificationStatus status, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * علامت‌گذاری به عنوان خوانده شده
     */
    @androidx.room.Query(value = "UPDATE notifications SET read_at = :timestamp WHERE id = :notificationId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object markAsRead(@org.jetbrains.annotations.NotNull
    java.lang.String notificationId, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * علامت‌گذاری همه اعلانات کاربر به عنوان خوانده شده
     */
    @androidx.room.Query(value = "UPDATE notifications SET read_at = :timestamp WHERE to_user_id = :userId AND read_at IS NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object markAllAsReadForUser(@org.jetbrains.annotations.NotNull
    java.lang.String userId, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * بروزرسانی وضعیت همگام‌سازی
     */
    @androidx.room.Query(value = "UPDATE notifications SET sync_status = :status, last_sync = :timestamp WHERE id = :notificationId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateSyncStatus(@org.jetbrains.annotations.NotNull
    java.lang.String notificationId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.SyncStatus status, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * تنظیم remote ID
     */
    @androidx.room.Query(value = "UPDATE notifications SET remote_id = :remoteId WHERE id = :notificationId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object setRemoteId(@org.jetbrains.annotations.NotNull
    java.lang.String notificationId, @org.jetbrains.annotations.NotNull
    java.lang.String remoteId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * شمارش اعلانات خوانده نشده کاربر
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM notifications WHERE to_user_id = :userId AND read_at IS NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUnreadCountForUser(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * شمارش اعلانات در انتظار کاربر
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM notifications WHERE to_user_id = :userId AND status = \'PENDING\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPendingCountForUser(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * دریافت اعلانات بر اساس نوع تراکنش
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE transaction_type = :type ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> getNotificationsByTransactionType(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionType type);
    
    /**
     * جستجو در اعلانات
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE description LIKE :query ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> searchNotifications(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    /**
     * دریافت اعلانات در بازه زمانی
     */
    @androidx.room.Query(value = "SELECT * FROM notifications WHERE created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.NotificationEntity>> getNotificationsInRange(long startTime, long endTime);
    
    /**
     * حذف اعلانات قدیمی
     */
    @androidx.room.Query(value = "DELETE FROM notifications WHERE created_at < :cutoffTime")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteOldNotifications(long cutoffTime, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * پاک کردن همه اعلانات
     */
    @androidx.room.Query(value = "DELETE FROM notifications")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearAllNotifications(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * شمارش کل اعلانات
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM notifications")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getNotificationCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * DAO برای عملیات اعلانات
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}