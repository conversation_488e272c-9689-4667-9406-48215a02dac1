package com.example.ma.data.model

/**
 * کلاس‌های خطا برای مدیریت حرفه‌ای خطاها
 */
sealed class ApiError : Exception() {
    
    /**
     * خطای شبکه - مشکل در اتصال اینترنت
     */
    object NetworkError : ApiError() {
        override val message: String = "خطا در اتصال به اینترنت"
    }
    
    /**
     * خطای احراز هویت - نام کاربری یا رمز عبور اشتباه
     */
    object AuthenticationError : ApiError() {
        override val message: String = "نام کاربری یا رمز عبور اشتباه است"
    }
    
    /**
     * خطای سرور - مشکل در سمت سرور
     */
    data class ServerError(val code: Int, val serverMessage: String? = null) : ApiError() {
        override val message: String = "خطای سرور (کد: $code): ${serverMessage ?: "خطای نامشخص"}"
    }
    
    /**
     * خطای اعتبارسنجی - داده‌های ورودی نامعتبر
     */
    data class ValidationError(val field: String, val reason: String) : ApiError() {
        override val message: String = "خطا در فیلد $field: $reason"
    }
    
    /**
     * خطای دسترسی - عدم مجوز برای انجام عملیات
     */
    object PermissionError : ApiError() {
        override val message: String = "شما مجوز انجام این عملیات را ندارید"
    }
    
    /**
     * خطای یافت نشدن - منبع مورد نظر یافت نشد
     */
    data class NotFoundError(val resource: String) : ApiError() {
        override val message: String = "$resource یافت نشد"
    }
    
    /**
     * خطای تداخل - منبع قبلاً وجود دارد
     */
    data class ConflictError(val resource: String) : ApiError() {
        override val message: String = "$resource قبلاً وجود دارد"
    }
    
    /**
     * خطای محدودیت نرخ - تعداد درخواست‌ها بیش از حد مجاز
     */
    object RateLimitError : ApiError() {
        override val message: String = "تعداد درخواست‌ها بیش از حد مجاز است. لطفاً کمی صبر کنید"
    }
    
    /**
     * خطای نامشخص - سایر خطاها
     */
    data class UnknownError(val originalException: Throwable? = null) : ApiError() {
        override val message: String = "خطای نامشخص: ${originalException?.message ?: "علت نامعلوم"}"
    }
    
    /**
     * خطای پردازش داده - مشکل در پردازش JSON یا داده‌ها
     */
    data class DataProcessingError(val details: String) : ApiError() {
        override val message: String = "خطا در پردازش داده‌ها: $details"
    }
    
    /**
     * خطای timeout - زمان انتظار تمام شد
     */
    object TimeoutError : ApiError() {
        override val message: String = "زمان انتظار تمام شد. لطفاً دوباره تلاش کنید"
    }
}

/**
 * تبدیل HTTP status code به ApiError مناسب
 */
fun Int.toApiError(message: String? = null): ApiError {
    return when (this) {
        400 -> ApiError.ValidationError("درخواست", message ?: "درخواست نامعتبر")
        401 -> ApiError.AuthenticationError
        403 -> ApiError.PermissionError
        404 -> ApiError.NotFoundError(message ?: "منبع")
        409 -> ApiError.ConflictError(message ?: "منبع")
        429 -> ApiError.RateLimitError
        in 500..599 -> ApiError.ServerError(this, message)
        else -> ApiError.UnknownError()
    }
}
