<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.ma" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="619" endOffset="43"/></Target><Target id="@+id/fixed_header" view="LinearLayout"><Expressions/><location startLine="18" startOffset="8" endLine="168" endOffset="22"/></Target><Target id="@+id/cardProfileImage" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="47" startOffset="28" endLine="69" endOffset="79"/></Target><Target id="@+id/ivHeaderProfileImage" view="ImageView"><Expressions/><location startLine="59" startOffset="32" endLine="67" endOffset="73"/></Target><Target id="@+id/tvHeaderUserName" view="TextView"><Expressions/><location startLine="79" startOffset="32" endLine="87" endOffset="76"/></Target><Target id="@+id/cardNotifications" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="99" startOffset="28" endLine="116" endOffset="79"/></Target><Target id="@+id/tvNotificationBadge" view="TextView"><Expressions/><location startLine="119" startOffset="28" endLine="132" endOffset="59"/></Target><Target id="@+id/cardMenu" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="137" startOffset="24" endLine="154" endOffset="75"/></Target><Target id="@+id/etBottleCount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="232" startOffset="28" endLine="239" endOffset="65"/></Target><Target id="@+id/etPrice" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="252" startOffset="28" endLine="259" endOffset="65"/></Target><Target id="@+id/tvPriceInWords" view="TextView"><Expressions/><location startLine="264" startOffset="24" endLine="276" endOffset="81"/></Target><Target id="@+id/tvTotalAmount" view="TextView"><Expressions/><location startLine="279" startOffset="24" endLine="292" endOffset="61"/></Target><Target id="@+id/rbCash" view="RadioButton"><Expressions/><location startLine="311" startOffset="28" endLine="321" endOffset="65"/></Target><Target id="@+id/rbCard" view="RadioButton"><Expressions/><location startLine="323" startOffset="28" endLine="332" endOffset="65"/></Target><Target id="@+id/tvReceiverLabel" view="TextView"><Expressions/><location startLine="337" startOffset="24" endLine="346" endOffset="68"/></Target><Target id="@+id/spinnerReceiver" view="Spinner"><Expressions/><location startLine="348" startOffset="24" endLine="354" endOffset="52"/></Target><Target id="@+id/etDescription" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="368" startOffset="28" endLine="376" endOffset="65"/></Target><Target id="@+id/btnRegisterTransaction" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="381" startOffset="24" endLine="392" endOffset="68"/></Target><Target id="@+id/cardStats" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="427" startOffset="24" endLine="465" endOffset="75"/></Target><Target id="@+id/cardTransactions" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="468" startOffset="24" endLine="506" endOffset="75"/></Target><Target id="@+id/cardFinancial" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="509" startOffset="24" endLine="547" endOffset="75"/></Target><Target id="@+id/cardInventory" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="550" startOffset="24" endLine="588" endOffset="75"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="601" startOffset="4" endLine="617" endOffset="71"/></Target></Targets></Layout>