<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <application
        android:name=".MAApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"

        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MA"
        tools:targetApi="31">
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:exported="false" />

        <activity
            android:name=".ui.profile.ProfileActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <activity
            android:name=".ui.notifications.NotificationActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <!-- صفحات مالی -->
        <activity
            android:name=".ui.financial.FinancialActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <activity
            android:name=".ui.financial.ExpenseActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <activity
            android:name=".ui.financial.WithdrawalActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <activity
            android:name=".ui.financial.TransactionsActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <activity
            android:name=".ui.statistics.StatisticsActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <activity
            android:name=".ui.inventory.InventoryActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <activity
            android:name=".ui.setup.DatabaseSetupActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <!-- Image Cropper Activity -->
        <activity
            android:name="com.canhub.cropper.CropImageActivity"
            android:theme="@style/Base.Theme.AppCompat" />
    </application>

</manifest>