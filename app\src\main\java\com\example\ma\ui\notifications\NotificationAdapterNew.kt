package com.example.ma.ui.notifications

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import com.example.ma.utils.CurrencyFormatter
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter جدید برای نمایش اعلانات با طراحی کامل
 */
class NotificationAdapterNew(
    private val context: Context,
    private var viewType: String = "received" // "received" یا "sent"
) : RecyclerView.Adapter<NotificationAdapterNew.NotificationViewHolder>() {

    private val notifications = mutableListOf<Map<String, Any>>()
    private var actionListener: OnNotificationActionListener? = null

    interface OnNotificationActionListener {
        fun onApprove(notificationId: String)
        fun onReject(notificationId: String)
    }

    fun setOnNotificationActionListener(listener: OnNotificationActionListener) {
        this.actionListener = listener
    }

    fun setViewType(newViewType: String) {
        viewType = newViewType
        notifyDataSetChanged()
    }

    fun updateNotifications(newNotifications: List<Map<String, Any>>) {
        notifications.clear()
        notifications.addAll(newNotifications)
        notifyDataSetChanged()
    }

    class NotificationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivProfilePicture: ImageView = itemView.findViewById(R.id.ivProfilePicture)
        val tvSenderName: TextView = itemView.findViewById(R.id.tvSenderName)
        val cardStatus: View = itemView.findViewById(R.id.cardStatus)
        val tvStatus: TextView = itemView.findViewById(R.id.tvStatus)
        val tvTransactionType: TextView = itemView.findViewById(R.id.tvTransactionType)
        val tvAmount: TextView = itemView.findViewById(R.id.tvAmount)
        val layoutQuantity: View = itemView.findViewById(R.id.layoutQuantity)
        val tvQuantityLabel: TextView = itemView.findViewById(R.id.tvQuantityLabel)
        val tvQuantity: TextView = itemView.findViewById(R.id.tvQuantity)
        val layoutAccount: View = itemView.findViewById(R.id.layoutAccount)
        val tvAccountLabel: TextView = itemView.findViewById(R.id.tvAccountLabel)
        val tvAccount: TextView = itemView.findViewById(R.id.tvAccount)
        val layoutDescription: View = itemView.findViewById(R.id.layoutDescription)
        val tvDescription: TextView = itemView.findViewById(R.id.tvDescription)
        val tvDate: TextView = itemView.findViewById(R.id.tvDate)
        val layoutActions: View = itemView.findViewById(R.id.layoutActions)
        val btnApprove: View = itemView.findViewById(R.id.btnApprove)
        val btnReject: View = itemView.findViewById(R.id.btnReject)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_notification_new, parent, false)
        return NotificationViewHolder(view)
    }

    override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
        val notification = notifications[position]
        
        // نام فرستنده و عکس پروفایل
        val fromUserId = notification["from_user_id"] as? String ?: "نامشخص"
        val senderName = getUserDisplayName(fromUserId)
        holder.tvSenderName.text = senderName
        
        // عکس پروفایل (فعلاً آیکون پیش‌فرض)
        holder.ivProfilePicture.setImageResource(
            if (fromUserId == "Alikakai") R.drawable.ic_person else R.drawable.ic_person
        )
        
        // نوع تراکنش
        val transactionType = notification["transaction_type"] as? String ?: ""
        val typeText = getTransactionTypeText(transactionType)
        holder.tvTransactionType.text = typeText
        
        // مبلغ
        val amount = notification["amount"] as? Double ?: 0.0
        holder.tvAmount.text = CurrencyFormatter.formatToToman(amount)
        
        // تعداد (بر اساس نوع تراکنش)
        val productCount = notification["product_count"] as? Int
        if (productCount != null && productCount > 0) {
            holder.layoutQuantity.visibility = View.VISIBLE
            when (transactionType) {
                "sale" -> {
                    holder.tvQuantityLabel.text = "تعداد فروش:"
                    holder.tvQuantity.text = "$productCount بطری"
                }
                "purchase" -> {
                    holder.tvQuantityLabel.text = "تعداد خرید:"
                    holder.tvQuantity.text = "$productCount قوطی"
                }
                else -> {
                    holder.tvQuantityLabel.text = "تعداد:"
                    holder.tvQuantity.text = "$productCount عدد"
                }
            }
        } else {
            holder.layoutQuantity.visibility = View.GONE
        }
        
        // نوع حساب
        val paymentType = notification["payment_type"] as? String
        if (!paymentType.isNullOrEmpty()) {
            holder.layoutAccount.visibility = View.VISIBLE
            when (transactionType) {
                "sale" -> {
                    holder.tvAccountLabel.text = "واریز به حساب:"
                    holder.tvAccount.text = getAccountText(paymentType, fromUserId)
                }
                "withdrawal" -> {
                    holder.tvAccountLabel.text = "برداشت از حساب:"
                    holder.tvAccount.text = getAccountText(paymentType, fromUserId)
                }
                "purchase" -> {
                    holder.tvAccountLabel.text = "پرداخت از حساب:"
                    holder.tvAccount.text = getAccountText(paymentType, fromUserId)
                }
                else -> {
                    holder.tvAccountLabel.text = "حساب:"
                    holder.tvAccount.text = getAccountText(paymentType, fromUserId)
                }
            }
        } else {
            holder.layoutAccount.visibility = View.GONE
        }
        
        // توضیحات
        val description = notification["description"] as? String
        if (!description.isNullOrEmpty()) {
            holder.layoutDescription.visibility = View.VISIBLE
            holder.tvDescription.text = description
        } else {
            holder.layoutDescription.visibility = View.GONE
        }
        
        // تاریخ
        val createdAt = notification["created_at"] as? String ?: ""
        holder.tvDate.text = formatTime(createdAt)
        
        // وضعیت
        val status = notification["status"] as? String ?: "pending"
        setupStatus(holder, status)
        
        // دکمه‌های عملیات
        setupActionButtons(holder, notification)
    }

    override fun getItemCount(): Int = notifications.size

    private fun getUserDisplayName(userId: String): String {
        return when (userId) {
            "Miladnasiri" -> "میلاد نصیری"
            "Alikakai" -> "علی کاکایی"
            else -> "کاربر ناشناس"
        }
    }

    private fun getTransactionTypeText(type: String): String {
        return when (type) {
            "sale" -> "فروش"
            "purchase" -> "خرید"
            "withdrawal" -> "برداشت"
            "expense" -> "هزینه"
            "deposit" -> "واریز"
            else -> "تراکنش"
        }
    }

    private fun getAccountText(paymentType: String, userId: String): String {
        val ownerName = getUserDisplayName(userId)
        return when (paymentType) {
            "cash" -> "نقدی $ownerName"
            "card" -> "کارت $ownerName"
            "personal" -> "شخصی $ownerName"
            else -> paymentType
        }
    }

    private fun setupStatus(holder: NotificationViewHolder, status: String) {
        when (status) {
            "pending" -> {
                if (viewType == "sent") {
                    // برای اعلانات ارسال شده
                    holder.tvStatus.text = "در حال انتظار تایید"
                    holder.layoutActions.visibility = View.GONE // فرستنده نمی‌تونه تایید کنه
                } else {
                    // برای اعلانات دریافت شده
                    holder.tvStatus.text = "در انتظار تایید شما"
                    holder.layoutActions.visibility = View.VISIBLE // گیرنده می‌تونه تایید کنه
                }
                holder.cardStatus.setBackgroundColor(context.getColor(R.color.warning_color))
            }
            "approved" -> {
                holder.tvStatus.text = "تایید شده"
                holder.cardStatus.setBackgroundColor(context.getColor(R.color.success_color))
                holder.layoutActions.visibility = View.GONE
            }
            "rejected" -> {
                holder.tvStatus.text = "رد شده"
                holder.cardStatus.setBackgroundColor(context.getColor(R.color.error_color))
                holder.layoutActions.visibility = View.GONE
            }
            else -> {
                holder.tvStatus.text = "نامشخص"
                holder.cardStatus.setBackgroundColor(context.getColor(R.color.text_hint))
                holder.layoutActions.visibility = View.GONE
            }
        }
    }

    private fun setupActionButtons(holder: NotificationViewHolder, notification: Map<String, Any>) {
        val notificationId = notification["id"] as? String ?: return
        
        holder.btnApprove.setOnClickListener {
            actionListener?.onApprove(notificationId)
        }
        
        holder.btnReject.setOnClickListener {
            actionListener?.onReject(notificationId)
        }
    }

    private fun formatTime(dateString: String): String {
        return try {
            val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.ENGLISH)
            val date = sdf.parse(dateString)
            val outputFormat = SimpleDateFormat("yyyy/MM/dd - HH:mm", Locale.getDefault())
            outputFormat.format(date ?: Date())
        } catch (e: Exception) {
            dateString
        }
    }
}
