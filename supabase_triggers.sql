-- Triggers برای بروزرسانی خودکار محاسبات مالی

-- Function برای بروزرسانی محاسبات
CREATE OR REPLACE FUNCTION update_financial_calculations()
RETURNS TRIGGER AS $$
BEGIN
    -- محاسبه کل فروش‌های تایید شده
    WITH financial_summary AS (
        SELECT 
            COALESCE(SUM(CASE WHEN s.status = 'approved' THEN s.amount ELSE 0 END), 0) as total_sales,
            COALESCE(SUM(CASE WHEN e.status = 'approved' THEN e.amount ELSE 0 END), 0) as total_expenses,
            COALESCE(SUM(CASE WHEN w.status = 'approved' THEN w.amount ELSE 0 END), 0) as total_withdrawals
        FROM 
            (SELECT amount, status FROM sales) s
            FULL OUTER JOIN (SELECT amount, status FROM expenses) e ON true
            FULL OUTER JOIN (SELECT amount, status FROM withdrawals) w ON true
    ),
    user_calculations AS (
        SELECT 
            'Miladnasiri' as user_id,
            fs.total_sales,
            fs.total_expenses,
            fs.total_withdrawals,
            (fs.total_sales - fs.total_expenses) / 2 as base_share,
            COALESCE(SUM(CASE WHEN e.user_id = 'Miladnasiri' AND e.status = 'approved' THEN e.amount ELSE 0 END), 0) as milad_expenses,
            COALESCE(SUM(CASE WHEN e.user_id = 'Alikakai' AND e.status = 'approved' THEN e.amount ELSE 0 END), 0) as ali_expenses,
            COALESCE(SUM(CASE WHEN w.user_id = 'Miladnasiri' AND w.status = 'approved' THEN w.amount ELSE 0 END), 0) as milad_withdrawals,
            COALESCE(SUM(CASE WHEN s.user_id = 'Miladnasiri' AND s.status = 'approved' AND s.payment_type = 'cash' THEN s.amount ELSE 0 END), 0) as milad_cash,
            COALESCE(SUM(CASE WHEN s.user_id = 'Miladnasiri' AND s.status = 'approved' AND s.payment_type = 'card' THEN s.amount ELSE 0 END), 0) as milad_card,
            COALESCE(SUM(CASE WHEN s.user_id = 'Miladnasiri' AND s.status = 'approved' AND s.payment_type = 'personal' THEN s.amount ELSE 0 END), 0) as milad_personal
        FROM 
            financial_summary fs
            LEFT JOIN expenses e ON true
            LEFT JOIN withdrawals w ON true
            LEFT JOIN sales s ON true
        GROUP BY fs.total_sales, fs.total_expenses, fs.total_withdrawals
        
        UNION ALL
        
        SELECT 
            'Alikakai' as user_id,
            fs.total_sales,
            fs.total_expenses,
            fs.total_withdrawals,
            (fs.total_sales - fs.total_expenses) / 2 as base_share,
            COALESCE(SUM(CASE WHEN e.user_id = 'Miladnasiri' AND e.status = 'approved' THEN e.amount ELSE 0 END), 0) as milad_expenses,
            COALESCE(SUM(CASE WHEN e.user_id = 'Alikakai' AND e.status = 'approved' THEN e.amount ELSE 0 END), 0) as ali_expenses,
            COALESCE(SUM(CASE WHEN w.user_id = 'Alikakai' AND w.status = 'approved' THEN w.amount ELSE 0 END), 0) as ali_withdrawals,
            COALESCE(SUM(CASE WHEN s.user_id = 'Alikakai' AND s.status = 'approved' AND s.payment_type = 'cash' THEN s.amount ELSE 0 END), 0) as ali_cash,
            COALESCE(SUM(CASE WHEN s.user_id = 'Alikakai' AND s.status = 'approved' AND s.payment_type = 'card' THEN s.amount ELSE 0 END), 0) as ali_card,
            COALESCE(SUM(CASE WHEN s.user_id = 'Alikakai' AND s.status = 'approved' AND s.payment_type = 'personal' THEN s.amount ELSE 0 END), 0) as ali_personal
        FROM 
            financial_summary fs
            LEFT JOIN expenses e ON true
            LEFT JOIN withdrawals w ON true
            LEFT JOIN sales s ON true
        GROUP BY fs.total_sales, fs.total_expenses, fs.total_withdrawals
    )
    -- بروزرسانی account_balances برای میلاد
    INSERT INTO account_balances (
        user_id, 
        profit_share, 
        total_sales, 
        total_expenses_paid, 
        total_withdrawals,
        cash_balance,
        card_balance,
        personal_balance,
        updated_at
    )
    SELECT 
        'Miladnasiri',
        base_share - (ali_expenses - milad_expenses) - milad_withdrawals as profit_share,
        total_sales,
        milad_expenses,
        milad_withdrawals,
        milad_cash,
        milad_card,
        milad_personal,
        NOW()
    FROM user_calculations 
    WHERE user_id = 'Miladnasiri'
    ON CONFLICT (user_id) 
    DO UPDATE SET
        profit_share = EXCLUDED.profit_share,
        total_sales = EXCLUDED.total_sales,
        total_expenses_paid = EXCLUDED.total_expenses_paid,
        total_withdrawals = EXCLUDED.total_withdrawals,
        cash_balance = EXCLUDED.cash_balance,
        card_balance = EXCLUDED.card_balance,
        personal_balance = EXCLUDED.personal_balance,
        updated_at = NOW();

    -- بروزرسانی account_balances برای علی
    INSERT INTO account_balances (
        user_id, 
        profit_share, 
        total_sales, 
        total_expenses_paid, 
        total_withdrawals,
        cash_balance,
        card_balance,
        personal_balance,
        updated_at
    )
    SELECT 
        'Alikakai',
        base_share + (ali_expenses - milad_expenses) - ali_withdrawals as profit_share,
        total_sales,
        ali_expenses,
        ali_withdrawals,
        ali_cash,
        ali_card,
        ali_personal,
        NOW()
    FROM user_calculations 
    WHERE user_id = 'Alikakai'
    ON CONFLICT (user_id) 
    DO UPDATE SET
        profit_share = EXCLUDED.profit_share,
        total_sales = EXCLUDED.total_sales,
        total_expenses_paid = EXCLUDED.total_expenses_paid,
        total_withdrawals = EXCLUDED.total_withdrawals,
        cash_balance = EXCLUDED.cash_balance,
        card_balance = EXCLUDED.card_balance,
        personal_balance = EXCLUDED.personal_balance,
        updated_at = NOW();

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger برای sales
CREATE OR REPLACE TRIGGER sales_financial_update
    AFTER INSERT OR UPDATE OR DELETE ON sales
    FOR EACH ROW
    EXECUTE FUNCTION update_financial_calculations();

-- Trigger برای expenses
CREATE OR REPLACE TRIGGER expenses_financial_update
    AFTER INSERT OR UPDATE OR DELETE ON expenses
    FOR EACH ROW
    EXECUTE FUNCTION update_financial_calculations();

-- Trigger برای withdrawals
CREATE OR REPLACE TRIGGER withdrawals_financial_update
    AFTER INSERT OR UPDATE OR DELETE ON withdrawals
    FOR EACH ROW
    EXECUTE FUNCTION update_financial_calculations();

-- اجرای اولیه محاسبات
SELECT update_financial_calculations();
