package com.example.ma.ui.model

import com.example.ma.data.model.ApiError

/**
 * حالت‌های مختلف UI
 */
sealed class UiState<out T> {
    
    /**
     * حالت اولیه - هنوز هیچ عملیاتی انجام نشده
     */
    object Idle : UiState<Nothing>()
    
    /**
     * حالت بارگذاری - در حال انجام عملیات
     */
    object Loading : UiState<Nothing>()
    
    /**
     * حالت موفقیت - عملیات با موفقیت انجام شد
     */
    data class Success<T>(val data: T) : UiState<T>()
    
    /**
     * حالت خطا - خطایی رخ داده
     */
    data class Error(val error: ApiError, val canRetry: Boolean = true) : UiState<Nothing>()
    
    /**
     * بررسی حالت‌ها
     */
    val isIdle: Boolean get() = this is Idle
    val isLoading: Boolean get() = this is Loading
    val isSuccess: Boolean get() = this is Success
    val isError: Boolean get() = this is Error
    
    /**
     * دریافت داده در صورت موفقیت
     */
    fun getDataOrNull(): T? = if (this is Success) data else null
    
    /**
     * دریافت خطا در صورت وجود
     */
    fun getErrorOrNull(): ApiError? = if (this is Error) error else null
}

/**
 * UI State برای صفحه اصلی
 */
data class MainUiState(
    val userInfo: UiState<Map<String, Any>> = UiState.Idle,
    val transactions: UiState<List<Map<String, Any>>> = UiState.Idle,
    val balances: UiState<Map<String, Double>> = UiState.Idle,
    val isSubmittingTransaction: Boolean = false,
    val lastTransactionResult: UiState<Boolean> = UiState.Idle
)

/**
 * UI State برای صفحه اعلانات
 */
data class NotificationUiState(
    val notifications: UiState<List<Map<String, Any>>> = UiState.Idle,
    val isProcessingNotification: Boolean = false,
    val lastActionResult: UiState<Boolean> = UiState.Idle,
    val currentFilter: String = "همه",
    val currentStatus: String = "همه"
)

/**
 * UI State برای صفحه پروفایل
 */
data class ProfileUiState(
    val userInfo: UiState<Map<String, Any>> = UiState.Idle,
    val balances: UiState<Map<String, Double>> = UiState.Idle,
    val isUpdatingProfile: Boolean = false,
    val isUploadingImage: Boolean = false,
    val lastUpdateResult: UiState<Boolean> = UiState.Idle
)

/**
 * UI State برای صفحه آمار
 */
data class StatisticsUiState(
    val personalStats: UiState<Map<String, Double>> = UiState.Idle,
    val globalStats: UiState<Map<String, Double>> = UiState.Idle,
    val inventoryStats: UiState<Map<String, Any>> = UiState.Idle,
    val isRefreshing: Boolean = false
)

/**
 * UI State برای صفحه تراکنش‌ها
 */
data class TransactionUiState(
    val transactions: UiState<List<Map<String, Any>>> = UiState.Idle,
    val isRefreshing: Boolean = false,
    val searchQuery: String = "",
    val selectedFilter: String = "همه",
    val selectedUser: String = "همه",
    val selectedStatus: String = "همه"
)

/**
 * UI State برای احراز هویت
 */
data class AuthUiState(
    val isLoading: Boolean = false,
    val loginResult: UiState<Boolean> = UiState.Idle,
    val isLoggedIn: Boolean = false,
    val currentUser: String? = null
)

/**
 * Extension functions برای تبدیل ApiResult به UiState
 */
fun <T> com.example.ma.data.model.ApiResult<T>.toUiState(): UiState<T> {
    return when (this) {
        is com.example.ma.data.model.ApiResult.Success -> UiState.Success(data)
        is com.example.ma.data.model.ApiResult.Error -> UiState.Error(error)
        is com.example.ma.data.model.ApiResult.Loading -> UiState.Loading
    }
}
