package com.example.ma.ui.inventory

import android.os.Bundle
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.data.remote.SupabaseClient
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch

/**
 * صفحه مدیریت انبار - افزایش/کاهش موجودی
 */
class InventoryActivity : AppCompatActivity() {

    private lateinit var tvCurrentStock: TextView
    private lateinit var rgChangeType: RadioGroup
    private lateinit var rbIncrease: RadioButton
    private lateinit var rbDecrease: RadioButton
    private lateinit var etQuantity: TextInputEditText
    private lateinit var etDescription: TextInputEditText
    private lateinit var btnSubmit: MaterialButton

    private var currentStock = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_inventory)

        setupUI()
        loadCurrentStock()
    }

    private fun setupUI() {
        // Toolbar
        findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)?.setNavigationOnClickListener {
            onBackPressed()
        }

        tvCurrentStock = findViewById(R.id.tvCurrentStock)
        rgChangeType = findViewById(R.id.rgChangeType)
        rbIncrease = findViewById(R.id.rbIncrease)
        rbDecrease = findViewById(R.id.rbDecrease)
        etQuantity = findViewById(R.id.etQuantity)
        etDescription = findViewById(R.id.etDescription)
        btnSubmit = findViewById(R.id.btnSubmit)

        // انتخاب پیش‌فرض
        rbIncrease.isChecked = true

        // دکمه ثبت
        btnSubmit.setOnClickListener {
            submitInventoryChange()
        }
    }

    private fun loadCurrentStock() {
        lifecycleScope.launch {
            try {
                currentStock = SupabaseClient.getCurrentStock()
                tvCurrentStock.text = "$currentStock عدد"
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@InventoryActivity, "خطا در بارگذاری موجودی", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun submitInventoryChange() {
        val quantityText = etQuantity.text.toString().trim()
        val description = etDescription.text.toString().trim()

        // اعتبارسنجی
        if (quantityText.isEmpty()) {
            etQuantity.error = "تعداد را وارد کنید"
            return
        }

        val quantity = try {
            quantityText.toInt()
        } catch (e: NumberFormatException) {
            etQuantity.error = "تعداد نامعتبر"
            return
        }

        if (quantity <= 0) {
            etQuantity.error = "تعداد باید بیشتر از صفر باشد"
            return
        }

        if (description.isEmpty()) {
            etDescription.error = "توضیحات را وارد کنید"
            return
        }

        // تشخیص نوع تغییر
        val changeType = when (rgChangeType.checkedRadioButtonId) {
            R.id.rbIncrease -> "increase"
            R.id.rbDecrease -> "decrease"
            else -> "increase"
        }

        // بررسی موجودی کافی برای کاهش
        if (changeType == "decrease" && quantity > currentStock) {
            etQuantity.error = "موجودی کافی نیست (موجودی فعلی: $currentStock)"
            return
        }

        // محاسبه موجودی جدید
        val newStock = if (changeType == "increase") {
            currentStock + quantity
        } else {
            currentStock - quantity
        }

        // ثبت تغییر انبار
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val success = SupabaseClient.createInventoryChange(
                        userId = currentUserId,
                        changeType = changeType,
                        quantity = quantity,
                        currentStock = newStock,
                        description = description
                    )

                    if (success) {
                        Toast.makeText(this@InventoryActivity, "تغییر انبار با موفقیت ثبت شد", Toast.LENGTH_SHORT).show()
                        clearForm()
                        loadCurrentStock() // بروزرسانی موجودی
                    } else {
                        Toast.makeText(this@InventoryActivity, "خطا در ثبت تغییر انبار", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@InventoryActivity, "خطا در احراز هویت", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@InventoryActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun clearForm() {
        etQuantity.text?.clear()
        etDescription.text?.clear()
        rbIncrease.isChecked = true
    }

    private fun getCurrentUserId(): String? {
        val sharedPreferences = getSharedPreferences("auth_prefs", MODE_PRIVATE)
        return sharedPreferences.getString("current_user_id", null)
    }

    override fun onResume() {
        super.onResume()
        // بروزرسانی موجودی هنگام بازگشت به صفحه
        loadCurrentStock()
    }
}
