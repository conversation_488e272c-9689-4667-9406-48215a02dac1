package com.example.ma.utils;

/**
 * TextWatcher یکپارچه برای فرمت کردن ورودی و نمایش به حروف
 * این کلاس همه کارهای مربوط به currency formatting را انجام می‌دهد
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\r\n\u0000\n\u0002\u0010\b\n\u0002\b\u000b\u0018\u00002\u00020\u0001B;\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\u0016\b\u0002\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\t\u00a2\u0006\u0002\u0010\fJ\u0012\u0010\u000e\u001a\u00020\u000b2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0016J*\u0010\u0011\u001a\u00020\u000b2\b\u0010\u000f\u001a\u0004\u0018\u00010\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u0014H\u0016J \u0010\u0017\u001a\u00020\u00142\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0019\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\nH\u0002J*\u0010\u001b\u001a\u00020\u000b2\b\u0010\u000f\u001a\u0004\u0018\u00010\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001c\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\u0016J\u0010\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\nH\u0002R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/example/ma/utils/UnifiedCurrencyTextWatcher;", "Landroid/text/TextWatcher;", "editText", "Landroid/widget/EditText;", "displayTextView", "Landroid/widget/TextView;", "showWithWords", "", "onAmountChanged", "Lkotlin/Function1;", "", "", "(Landroid/widget/EditText;Landroid/widget/TextView;ZLkotlin/jvm/functions/Function1;)V", "isFormatting", "afterTextChanged", "s", "Landroid/text/Editable;", "beforeTextChanged", "", "start", "", "count", "after", "calculateCursorPosition", "currentPosition", "oldText", "newText", "onTextChanged", "before", "updateDisplayText", "cleanInput", "app_debug"})
public final class UnifiedCurrencyTextWatcher implements android.text.TextWatcher {
    @org.jetbrains.annotations.NotNull
    private final android.widget.EditText editText = null;
    @org.jetbrains.annotations.Nullable
    private final android.widget.TextView displayTextView = null;
    private final boolean showWithWords = false;
    @org.jetbrains.annotations.Nullable
    private final kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit> onAmountChanged = null;
    private boolean isFormatting = false;
    
    public UnifiedCurrencyTextWatcher(@org.jetbrains.annotations.NotNull
    android.widget.EditText editText, @org.jetbrains.annotations.Nullable
    android.widget.TextView displayTextView, boolean showWithWords, @org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAmountChanged) {
        super();
    }
    
    @java.lang.Override
    public void beforeTextChanged(@org.jetbrains.annotations.Nullable
    java.lang.CharSequence s, int start, int count, int after) {
    }
    
    @java.lang.Override
    public void onTextChanged(@org.jetbrains.annotations.Nullable
    java.lang.CharSequence s, int start, int before, int count) {
    }
    
    @java.lang.Override
    public void afterTextChanged(@org.jetbrains.annotations.Nullable
    android.text.Editable s) {
    }
    
    /**
     * محاسبه موقعیت صحیح cursor
     */
    private final int calculateCursorPosition(int currentPosition, java.lang.String oldText, java.lang.String newText) {
        return 0;
    }
    
    /**
     * نمایش مبلغ به حروف
     */
    private final void updateDisplayText(java.lang.String cleanInput) {
    }
}