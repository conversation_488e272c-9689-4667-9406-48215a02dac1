<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <!-- محتوای اصلی -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:fitsSystemWindows="true">

        <!-- Header ثابت -->
        <LinearLayout
            android:id="@+id/fixed_header"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:orientation="vertical"
            android:background="@drawable/gradient_primary"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp"
            android:gravity="center_vertical"
            android:elevation="8dp">



                    <!-- Header Top Row -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="4dp">

                        <!-- Profile Section -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/cardProfileImage"
                                android:layout_width="36dp"
                                android:layout_height="36dp"
                                app:cardCornerRadius="18dp"
                                app:cardElevation="4dp"
                                app:strokeColor="@color/text_white"
                                app:strokeWidth="2dp"
                                android:clickable="true"
                                android:focusable="true"
                                android:foreground="?android:attr/selectableItemBackground">

                                <ImageView
                                    android:id="@+id/ivHeaderProfileImage"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:src="@drawable/ic_person_modern"
                                    android:background="@color/surface_color"
                                    android:padding="8dp"
                                    android:scaleType="centerCrop"
                                    android:tint="@color/primary_color" />

                            </com.google.android.material.card.MaterialCardView>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginStart="12dp">



                                <TextView
                                    android:id="@+id/tvHeaderUserName"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="کاربر عزیز"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/text_white"
                                    android:fontFamily="sans-serif-medium" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Notification Button -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardNotifications"
                            android:layout_width="36dp"
                            android:layout_height="36dp"
                            android:layout_marginEnd="8dp"
                            app:cardCornerRadius="18dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/surface_color"
                            android:clickable="true"
                            android:focusable="true">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_notifications"
                                app:tint="@color/primary_color" />

                        </com.google.android.material.card.MaterialCardView>

                        <!-- Menu Button -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardMenu"
                            android:layout_width="36dp"
                            android:layout_height="36dp"
                            app:cardCornerRadius="18dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/surface_color"
                            android:clickable="true"
                            android:focusable="true">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_menu"
                                app:tint="@color/primary_color" />

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                    <!-- Welcome Text -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="به حسابداری شراکتی میکده خوش آمدید"
                        android:textSize="8sp"
                        android:textColor="@color/text_white"
                        android:alpha="0.9"
                        android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- محتوای اصلی -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- فرم ثبت تراکنش/فروش مطابق iBank UIKit -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="20dp"
                    android:layout_marginTop="-10dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="12dp"
                    app:cardBackgroundColor="@color/surface_color">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="24dp">

                        <!-- عنوان فرم -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="24dp">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_trending_up"
                                android:layout_marginEnd="12dp"
                                app:tint="@color/primary_color" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="ثبت تراکنش / فروش جدید"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/primary_color"
                                android:fontFamily="sans-serif-medium" />

                        </LinearLayout>

                        <!-- تعداد محصول -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            app:boxStrokeColor="@color/primary_color"
                            app:hintTextColor="@color/primary_color"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etBottleCount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="تعداد محصول"
                                android:inputType="number"
                                android:textSize="16sp"
                                android:fontFamily="sans-serif" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- قیمت -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="20dp"
                            app:boxStrokeColor="@color/primary_color"
                            app:hintTextColor="@color/primary_color"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etPrice"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="قیمت (تومان)"
                                android:inputType="number"
                                android:textSize="16sp"
                                android:fontFamily="sans-serif" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- نمایش قیمت با حروف -->
                        <TextView
                            android:id="@+id/tvPriceInWords"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:padding="12dp"
                            android:background="#F0F0F0"
                            android:textColor="@color/primary_color"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif"
                            android:gravity="center"
                            android:visibility="gone"
                            android:text="قیمت با حروف اینجا نمایش داده می‌شود" />

                        <!-- نمایش مبلغ کل -->
                        <TextView
                            android:id="@+id/tvTotalAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="20dp"
                            android:padding="16dp"
                            android:background="#6C5CE7"
                            android:textColor="@color/background_color"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif"
                            android:gravity="center"
                            android:visibility="gone"
                            android:text="مبلغ کل: ۰ تومان" />

                        <!-- نوع پرداخت -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="نوع پرداخت"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="12dp"
                            android:fontFamily="sans-serif-medium" />

                        <RadioGroup
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="20dp">

                            <RadioButton
                                android:id="@+id/rbCash"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="💰 نقدی"
                                android:textSize="14sp"
                                android:textColor="@color/text_primary"
                                android:buttonTint="@color/primary_color"
                                android:checked="true"
                                android:fontFamily="sans-serif" />

                            <RadioButton
                                android:id="@+id/rbCard"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="💳 کارت به کارت"
                                android:textSize="14sp"
                                android:textColor="@color/text_primary"
                                android:buttonTint="@color/primary_color"
                                android:fontFamily="sans-serif" />

                        </RadioGroup>

                        <!-- پرداخت به -->
                        <TextView
                            android:id="@+id/tvReceiverLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="پرداخت به"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="12dp"
                            android:fontFamily="sans-serif-medium" />

                        <Spinner
                            android:id="@+id/spinnerReceiver"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:layout_marginBottom="24dp"
                            android:background="@drawable/spinner_background"
                            android:padding="12dp" />

                        <!-- توضیحات (اختیاری) -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="24dp"
                            android:hint="توضیحات (اختیاری)"
                            app:boxStrokeColor="@color/primary_color"
                            app:hintTextColor="@color/primary_color"
                            app:helperText="می‌توانید توضیحات اضافی برای این تراکنش وارد کنید"
                            app:helperTextTextColor="@color/text_secondary"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etDescription"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textMultiLine"
                                android:maxLines="3"
                                android:minLines="1"
                                android:textSize="16sp"
                                android:fontFamily="sans-serif" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- دکمه ثبت -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnRegisterTransaction"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:text="ثبت تراکنش"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_white"
                            android:backgroundTint="@color/primary_color"
                            app:cornerRadius="16dp"
                            app:elevation="8dp"
                            android:fontFamily="sans-serif-medium" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- بخش عملیات سریع مطابق iBank UIKit -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="16dp">

                    <!-- عنوان بخش -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="عملیات سریع"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:fontFamily="sans-serif-medium" />

                    <!-- Grid عملیات سریع -->
                    <GridLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:columnCount="2"
                        android:rowCount="2"
                        android:alignmentMode="alignMargins"
                        android:useDefaultMargins="true">

                        <!-- دکمه ثبت فروش -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardStats"
                            android:layout_width="0dp"
                            android:layout_height="100dp"
                            android:layout_columnWeight="1"
                            android:layout_margin="8dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="6dp"
                            app:cardBackgroundColor="@color/surface_color"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="16dp">

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:src="@drawable/ic_bar_chart"
                                    android:layout_marginBottom="8dp"
                                    app:tint="@color/info_color" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="آمار"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/text_primary"
                                    android:fontFamily="sans-serif-medium" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <!-- دکمه تراکنش‌ها -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardTransactions"
                            android:layout_width="0dp"
                            android:layout_height="100dp"
                            android:layout_columnWeight="1"
                            android:layout_margin="8dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="6dp"
                            app:cardBackgroundColor="@color/surface_color"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="16dp">

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:src="@drawable/ic_receipt"
                                    android:layout_marginBottom="8dp"
                                    app:tint="@color/info_color" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="تراکنش‌ها"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/text_primary"
                                    android:fontFamily="sans-serif-medium" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <!-- دکمه مالی -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardFinancial"
                            android:layout_width="0dp"
                            android:layout_height="100dp"
                            android:layout_columnWeight="1"
                            android:layout_margin="8dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="6dp"
                            app:cardBackgroundColor="@color/surface_color"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="16dp">

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:src="@drawable/ic_account_balance"
                                    android:layout_marginBottom="8dp"
                                    app:tint="@color/balance_color" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="مالی"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/text_primary"
                                    android:fontFamily="sans-serif-medium" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <!-- دکمه انبار -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardInventory"
                            android:layout_width="0dp"
                            android:layout_height="100dp"
                            android:layout_columnWeight="1"
                            android:layout_margin="8dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="6dp"
                            app:cardBackgroundColor="@color/surface_color"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="16dp">

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:src="@drawable/ic_inventory"
                                    android:layout_marginBottom="8dp"
                                    app:tint="@color/warning_color" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="انبار"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/text_primary"
                                    android:fontFamily="sans-serif-medium" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </GridLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="320dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/surface_color"
        android:elevation="16dp"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/activity_main_drawer"
        app:itemIconTint="@color/primary_color"
        app:itemTextColor="@color/text_primary"
        app:itemBackground="@drawable/nav_item_background"
        app:itemIconSize="24dp"
        app:itemHorizontalPadding="16dp"
        app:itemVerticalPadding="12dp"
        app:subheaderTextAppearance="@style/NavigationSubheaderStyle" />

</androidx.drawerlayout.widget.DrawerLayout>
