// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final MaterialButton btnRegisterTransaction;

  @NonNull
  public final MaterialCardView cardFinancial;

  @NonNull
  public final MaterialCardView cardInventory;

  @NonNull
  public final MaterialCardView cardMenu;

  @NonNull
  public final MaterialCardView cardNotifications;

  @NonNull
  public final MaterialCardView cardProfileImage;

  @NonNull
  public final MaterialCardView cardStats;

  @NonNull
  public final MaterialCardView cardTransactions;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final TextInputEditText etBottleCount;

  @NonNull
  public final TextInputEditText etDescription;

  @NonNull
  public final TextInputEditText etPrice;

  @NonNull
  public final LinearLayout fixedHeader;

  @NonNull
  public final ImageView ivHeaderProfileImage;

  @NonNull
  public final NavigationView navView;

  @NonNull
  public final RadioButton rbCard;

  @NonNull
  public final RadioButton rbCash;

  @NonNull
  public final Spinner spinnerReceiver;

  @NonNull
  public final TextView tvHeaderUserName;

  @NonNull
  public final TextView tvNotificationBadge;

  @NonNull
  public final TextView tvPriceInWords;

  @NonNull
  public final TextView tvReceiverLabel;

  @NonNull
  public final TextView tvTotalAmount;

  private ActivityMainBinding(@NonNull DrawerLayout rootView,
      @NonNull MaterialButton btnRegisterTransaction, @NonNull MaterialCardView cardFinancial,
      @NonNull MaterialCardView cardInventory, @NonNull MaterialCardView cardMenu,
      @NonNull MaterialCardView cardNotifications, @NonNull MaterialCardView cardProfileImage,
      @NonNull MaterialCardView cardStats, @NonNull MaterialCardView cardTransactions,
      @NonNull DrawerLayout drawerLayout, @NonNull TextInputEditText etBottleCount,
      @NonNull TextInputEditText etDescription, @NonNull TextInputEditText etPrice,
      @NonNull LinearLayout fixedHeader, @NonNull ImageView ivHeaderProfileImage,
      @NonNull NavigationView navView, @NonNull RadioButton rbCard, @NonNull RadioButton rbCash,
      @NonNull Spinner spinnerReceiver, @NonNull TextView tvHeaderUserName,
      @NonNull TextView tvNotificationBadge, @NonNull TextView tvPriceInWords,
      @NonNull TextView tvReceiverLabel, @NonNull TextView tvTotalAmount) {
    this.rootView = rootView;
    this.btnRegisterTransaction = btnRegisterTransaction;
    this.cardFinancial = cardFinancial;
    this.cardInventory = cardInventory;
    this.cardMenu = cardMenu;
    this.cardNotifications = cardNotifications;
    this.cardProfileImage = cardProfileImage;
    this.cardStats = cardStats;
    this.cardTransactions = cardTransactions;
    this.drawerLayout = drawerLayout;
    this.etBottleCount = etBottleCount;
    this.etDescription = etDescription;
    this.etPrice = etPrice;
    this.fixedHeader = fixedHeader;
    this.ivHeaderProfileImage = ivHeaderProfileImage;
    this.navView = navView;
    this.rbCard = rbCard;
    this.rbCash = rbCash;
    this.spinnerReceiver = spinnerReceiver;
    this.tvHeaderUserName = tvHeaderUserName;
    this.tvNotificationBadge = tvNotificationBadge;
    this.tvPriceInWords = tvPriceInWords;
    this.tvReceiverLabel = tvReceiverLabel;
    this.tvTotalAmount = tvTotalAmount;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnRegisterTransaction;
      MaterialButton btnRegisterTransaction = ViewBindings.findChildViewById(rootView, id);
      if (btnRegisterTransaction == null) {
        break missingId;
      }

      id = R.id.cardFinancial;
      MaterialCardView cardFinancial = ViewBindings.findChildViewById(rootView, id);
      if (cardFinancial == null) {
        break missingId;
      }

      id = R.id.cardInventory;
      MaterialCardView cardInventory = ViewBindings.findChildViewById(rootView, id);
      if (cardInventory == null) {
        break missingId;
      }

      id = R.id.cardMenu;
      MaterialCardView cardMenu = ViewBindings.findChildViewById(rootView, id);
      if (cardMenu == null) {
        break missingId;
      }

      id = R.id.cardNotifications;
      MaterialCardView cardNotifications = ViewBindings.findChildViewById(rootView, id);
      if (cardNotifications == null) {
        break missingId;
      }

      id = R.id.cardProfileImage;
      MaterialCardView cardProfileImage = ViewBindings.findChildViewById(rootView, id);
      if (cardProfileImage == null) {
        break missingId;
      }

      id = R.id.cardStats;
      MaterialCardView cardStats = ViewBindings.findChildViewById(rootView, id);
      if (cardStats == null) {
        break missingId;
      }

      id = R.id.cardTransactions;
      MaterialCardView cardTransactions = ViewBindings.findChildViewById(rootView, id);
      if (cardTransactions == null) {
        break missingId;
      }

      DrawerLayout drawerLayout = (DrawerLayout) rootView;

      id = R.id.etBottleCount;
      TextInputEditText etBottleCount = ViewBindings.findChildViewById(rootView, id);
      if (etBottleCount == null) {
        break missingId;
      }

      id = R.id.etDescription;
      TextInputEditText etDescription = ViewBindings.findChildViewById(rootView, id);
      if (etDescription == null) {
        break missingId;
      }

      id = R.id.etPrice;
      TextInputEditText etPrice = ViewBindings.findChildViewById(rootView, id);
      if (etPrice == null) {
        break missingId;
      }

      id = R.id.fixed_header;
      LinearLayout fixedHeader = ViewBindings.findChildViewById(rootView, id);
      if (fixedHeader == null) {
        break missingId;
      }

      id = R.id.ivHeaderProfileImage;
      ImageView ivHeaderProfileImage = ViewBindings.findChildViewById(rootView, id);
      if (ivHeaderProfileImage == null) {
        break missingId;
      }

      id = R.id.nav_view;
      NavigationView navView = ViewBindings.findChildViewById(rootView, id);
      if (navView == null) {
        break missingId;
      }

      id = R.id.rbCard;
      RadioButton rbCard = ViewBindings.findChildViewById(rootView, id);
      if (rbCard == null) {
        break missingId;
      }

      id = R.id.rbCash;
      RadioButton rbCash = ViewBindings.findChildViewById(rootView, id);
      if (rbCash == null) {
        break missingId;
      }

      id = R.id.spinnerReceiver;
      Spinner spinnerReceiver = ViewBindings.findChildViewById(rootView, id);
      if (spinnerReceiver == null) {
        break missingId;
      }

      id = R.id.tvHeaderUserName;
      TextView tvHeaderUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvHeaderUserName == null) {
        break missingId;
      }

      id = R.id.tvNotificationBadge;
      TextView tvNotificationBadge = ViewBindings.findChildViewById(rootView, id);
      if (tvNotificationBadge == null) {
        break missingId;
      }

      id = R.id.tvPriceInWords;
      TextView tvPriceInWords = ViewBindings.findChildViewById(rootView, id);
      if (tvPriceInWords == null) {
        break missingId;
      }

      id = R.id.tvReceiverLabel;
      TextView tvReceiverLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvReceiverLabel == null) {
        break missingId;
      }

      id = R.id.tvTotalAmount;
      TextView tvTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalAmount == null) {
        break missingId;
      }

      return new ActivityMainBinding((DrawerLayout) rootView, btnRegisterTransaction, cardFinancial,
          cardInventory, cardMenu, cardNotifications, cardProfileImage, cardStats, cardTransactions,
          drawerLayout, etBottleCount, etDescription, etPrice, fixedHeader, ivHeaderProfileImage,
          navView, rbCard, rbCash, spinnerReceiver, tvHeaderUserName, tvNotificationBadge,
          tvPriceInWords, tvReceiverLabel, tvTotalAmount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
