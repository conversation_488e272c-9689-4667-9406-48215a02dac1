package com.example.ma.data.remote

import com.example.ma.data.model.ApiError
import com.example.ma.data.model.ApiResult
import kotlinx.coroutines.test.runTest
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.kotlin.mock
import java.net.HttpURLConnection

/**
 * تست‌های واحد برای SupabaseClient
 */
class SupabaseClientTest {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var supabaseClient: SupabaseClient

    @Before
    fun setup() {
        mockWebServer = MockWebServer()
        mockWebServer.start()
        
        // Note: در تست واقعی باید SupabaseClient را قابل تست کنیم
        // اینجا فقط نمونه‌ای از ساختار تست ارائه می‌دهیم
    }

    @After
    fun tearDown() {
        mockWebServer.shutdown()
    }

    @Test
    fun authenticateUser_withValidCredentials_shouldReturnSuccess() = runTest {
        // Given
        val username = "testuser"
        val password = "testpass"
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_OK)
                .setBody("true")
        )

        // When
        val result = SupabaseClient.authenticateUser(username, password)

        // Then
        assertTrue(result is ApiResult.Success)
        if (result is ApiResult.Success) {
            assertTrue(result.data)
        }
    }

    @Test
    fun authenticateUser_withInvalidCredentials_shouldReturnError() = runTest {
        // Given
        val username = "invaliduser"
        val password = "wrongpass"
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_UNAUTHORIZED)
                .setBody("false")
        )

        // When
        val result = SupabaseClient.authenticateUser(username, password)

        // Then
        assertTrue(result is ApiResult.Error)
        if (result is ApiResult.Error) {
            assertTrue(result.error is ApiError.AuthenticationError)
        }
    }

    @Test
    fun authenticateUser_withEmptyCredentials_shouldReturnValidationError() = runTest {
        // Given
        val username = ""
        val password = ""

        // When
        val result = SupabaseClient.authenticateUser(username, password)

        // Then
        assertTrue(result is ApiResult.Error)
        if (result is ApiResult.Error) {
            assertTrue(result.error is ApiError.ValidationError)
        }
    }

    @Test
    fun authenticateUser_withNetworkError_shouldReturnNetworkError() = runTest {
        // Given
        val username = "testuser"
        val password = "testpass"
        
        // Simulate network error by not enqueuing any response
        mockWebServer.shutdown()

        // When
        val result = SupabaseClient.authenticateUser(username, password)

        // Then
        assertTrue(result is ApiResult.Error)
        if (result is ApiResult.Error) {
            assertTrue(
                result.error is ApiError.NetworkError || 
                result.error is ApiError.UnknownError
            )
        }
    }

    @Test
    fun getUserInfo_withValidUserId_shouldReturnUserData() = runTest {
        // Given
        val userId = "user123"
        val expectedUserData = """
            [{
                "id": "user123",
                "username": "testuser",
                "display_name": "Test User",
                "email": "<EMAIL>"
            }]
        """.trimIndent()
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_OK)
                .setBody(expectedUserData)
        )

        // When
        val result = SupabaseClient.getUserInfo(userId)

        // Then
        assertNotNull(result)
        assertTrue(result is Map<*, *>)
        if (result is Map<*, *>) {
            assertEquals("user123", result["id"])
            assertEquals("testuser", result["username"])
        }
    }

    @Test
    fun getUserInfo_withInvalidUserId_shouldReturnNull() = runTest {
        // Given
        val userId = "invaliduser"
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_NOT_FOUND)
                .setBody("[]")
        )

        // When
        val result = SupabaseClient.getUserInfo(userId)

        // Then
        assertNull(result)
    }

    @Test
    fun registerSaleTransaction_withValidData_shouldReturnSuccess() = runTest {
        // Given
        val amount = 100.0
        val description = "Test sale"
        val userId = "user123"
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_CREATED)
                .setBody("{\"id\": \"transaction123\"}")
        )

        // When
        val result = SupabaseClient.registerSaleTransaction(
            amount = amount,
            description = description,
            userId = userId,
            bottleCount = 2,
            paymentType = "نقدی",
            receiver = "customer1"
        )

        // Then
        assertTrue(result)
    }

    @Test
    fun registerSaleTransaction_withInvalidData_shouldReturnFailure() = runTest {
        // Given
        val amount = -100.0 // Invalid amount
        val description = ""
        val userId = "user123"
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_BAD_REQUEST)
                .setBody("{\"error\": \"Invalid data\"}")
        )

        // When
        val result = SupabaseClient.registerSaleTransaction(
            amount = amount,
            description = description,
            userId = userId,
            bottleCount = 0,
            paymentType = "",
            receiver = ""
        )

        // Then
        assertFalse(result)
    }

    @Test
    fun getNotifications_withValidUserId_shouldReturnNotifications() = runTest {
        // Given
        val userId = "user123"
        val expectedNotifications = """
            [{
                "id": "notif1",
                "from_user_id": "user456",
                "to_user_id": "user123",
                "transaction_type": "sale",
                "amount": 100.0,
                "status": "pending"
            }]
        """.trimIndent()
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_OK)
                .setBody(expectedNotifications)
        )

        // When
        val result = SupabaseClient.getNotifications(userId)

        // Then
        assertNotNull(result)
        assertTrue(result is List<*>)
        if (result is List<*>) {
            assertEquals(1, result.size)
            val notification = result[0] as Map<*, *>
            assertEquals("notif1", notification["id"])
            assertEquals("pending", notification["status"])
        }
    }

    @Test
    fun updateNotificationStatus_withValidData_shouldReturnSuccess() = runTest {
        // Given
        val notificationId = "notif123"
        val status = "approved"
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_OK)
                .setBody("{\"success\": true}")
        )

        // When
        val result = SupabaseClient.updateNotificationStatus(notificationId, status)

        // Then
        assertTrue(result)
    }

    @Test
    fun updateNotificationStatus_withInvalidId_shouldReturnFailure() = runTest {
        // Given
        val notificationId = "invalid_id"
        val status = "approved"
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_NOT_FOUND)
                .setBody("{\"error\": \"Notification not found\"}")
        )

        // When
        val result = SupabaseClient.updateNotificationStatus(notificationId, status)

        // Then
        assertFalse(result)
    }

    @Test
    fun getAccountBalances_shouldReturnBalanceData() = runTest {
        // Given
        val expectedBalances = """
            {
                "user123": 1500.0,
                "user456": 2000.0
            }
        """.trimIndent()
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(HttpURLConnection.HTTP_OK)
                .setBody(expectedBalances)
        )

        // When
        val result = SupabaseClient.getAccountBalances()

        // Then
        assertNotNull(result)
        assertTrue(result is Map<*, *>)
        if (result is Map<*, *>) {
            assertEquals(1500.0, result["user123"])
            assertEquals(2000.0, result["user456"])
        }
    }
}
