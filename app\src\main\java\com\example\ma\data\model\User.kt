package com.example.ma.data.model

/**
 * مدل کاربر برای دو شریک کسب‌وکار
 * این کلاس اطلاعات کاربران سیستم را نگهداری می‌کند
 */
data class User(
    val id: String,                    // شناسه یکتای کاربر
    val username: String,              // نام کاربری
    val displayName: String,           // نام نمایشی فارسی
    val email: String? = null,         // ایمیل کاربر
    val phone: String? = null,         // شماره تماس
    val profileImage: String? = null,  // عکس پروفایل (base64 یا مسیر فایل)
    val isActive: Boolean = false      // وضعیت فعال بودن کاربر
) {
    companion object {
        // نام‌های کاربری ثابت
        const val ALIKAKAI = "Alikakai"
        const val MILADNASIRI = "Miladnasiri"

        /**
         * دریافت لیست همه کاربران سیستم با ID های واقعی از Supabase
         * @return لیست کاربران
         */
        fun getAllUsers() = listOf(
            User(
                id = "ad28ba8f-0fa0-4420-8119-70fcacfd237e",
                username = ALIKAKAI,
                displayName = "علی کاکایی",
                phone = "09172558813"
            ),
            User(
                id = "930b5d13-0408-4c57-965b-235c5532b35a",
                username = MILADNASIRI,
                displayName = "میلاد نصیری",
                phone = "09184352395"
            )
        )
    }
}
