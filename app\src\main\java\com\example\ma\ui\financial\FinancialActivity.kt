package com.example.ma.ui.financial

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.data.remote.SupabaseClient
import com.google.android.material.card.MaterialCardView
import kotlinx.coroutines.launch

/**
 * صفحه مالی - مرکز مدیریت تراکنش‌های مالی
 */
class FinancialActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_financial)

        setupUI()
        loadAccountBalances()
    }

    private fun setupUI() {
        // دکمه بازگشت
        findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)?.setNavigationOnClickListener {
            onBackPressed()
        }

        // کارت ثبت هزینه شخصی مشترک
        findViewById<MaterialCardView>(R.id.cardSharedExpense)?.setOnClickListener {
            openExpenseActivity("shared_personal")
        }

        // کارت ثبت هزینه شرکتی
        findViewById<MaterialCardView>(R.id.cardBusinessExpense)?.setOnClickListener {
            openExpenseActivity("business")
        }

        // کارت برداشت شخصی
        findViewById<MaterialCardView>(R.id.cardPersonalWithdrawal)?.setOnClickListener {
            openWithdrawalActivity()
        }

        // کارت تراکنش‌ها
        findViewById<MaterialCardView>(R.id.cardTransactions)?.setOnClickListener {
            openTransactionsActivity()
        }
    }

    private fun loadAccountBalances() {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val balances = SupabaseClient.getAccountBalances()
                    if (balances != null) {
                        updateBalancesUI(balances)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@FinancialActivity, "خطا در بارگذاری موجودی‌ها", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun updateBalancesUI(balances: Map<String, Any>) {
        val companyBalance = balances["company_balance"] as? Double ?: 0.0
        val personalCardBalance = balances["personal_card_balance"] as? Double ?: 0.0
        val personalCashBalance = balances["personal_cash_balance"] as? Double ?: 0.0
        val profitShare = balances["profit_share"] as? Double ?: 0.0

        findViewById<android.widget.TextView>(R.id.tvCompanyBalance)?.text = 
            formatCurrency(companyBalance)
        findViewById<android.widget.TextView>(R.id.tvPersonalCardBalance)?.text = 
            formatCurrency(personalCardBalance)
        findViewById<android.widget.TextView>(R.id.tvPersonalCashBalance)?.text = 
            formatCurrency(personalCashBalance)
        findViewById<android.widget.TextView>(R.id.tvProfitShare)?.text = 
            formatCurrency(profitShare)
    }

    private fun openExpenseActivity(type: String) {
        val intent = Intent(this, ExpenseActivity::class.java)
        intent.putExtra("expense_type", type)
        startActivity(intent)
    }

    private fun openWithdrawalActivity() {
        val intent = Intent(this, WithdrawalActivity::class.java)
        startActivity(intent)
    }

    private fun openTransactionsActivity() {
        val intent = Intent(this, TransactionsActivity::class.java)
        startActivity(intent)
    }

    private fun getCurrentUserId(): String? {
        val sharedPreferences = getSharedPreferences("auth_prefs", MODE_PRIVATE)
        return sharedPreferences.getString("current_user_id", null)
    }

    private fun formatCurrency(amount: Double): String {
        return String.format("%,.0f تومان", amount)
    }
}
