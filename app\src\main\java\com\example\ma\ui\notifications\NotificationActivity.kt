package com.example.ma.ui.notifications

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import com.example.ma.data.remote.SupabaseClient
import com.example.ma.data.remote.SupabaseRealtimeClient
import com.example.ma.utils.DatabaseSetup
import com.google.android.material.appbar.MaterialToolbar
import kotlinx.coroutines.launch

/**
 * صفحه نمایش اعلانات با عکس پروفایل کاربران
 */
class NotificationActivity : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: NotificationAdapter
    private var currentFilter = "all"
    private var currentStatus = "all"

    // Realtime client برای دریافت اعلانات لحظه‌ای
    private val realtimeClient = SupabaseRealtimeClient()
    private var subscriptionRef: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_notification)

        initializeViews()
        setupToolbar()
        setupRecyclerView()
        setupFilters()

        // راه‌اندازی دیتابیس و بارگذاری اعلانات
        setupDatabase()
    }

    private fun initializeViews() {
        recyclerView = findViewById(R.id.rvNotifications)

        // تنظیم دکمه بازگشت
        findViewById<View>(R.id.btnBack)?.setOnClickListener {
            onBackPressed()
        }
    }

    private fun setupToolbar() {
        // Header جدید نیاز به تنظیم خاصی ندارد
    }

    private fun setupRecyclerView() {
        adapter = NotificationAdapter(this)
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter

        // تنظیم listener برای دکمه‌های تایید/رد
        adapter.setOnNotificationActionListener(object : NotificationAdapter.OnNotificationActionListener {
            override fun onApprove(notificationId: String, position: Int) {
                approveNotification(notificationId, position)
            }

            override fun onReject(notificationId: String, position: Int) {
                rejectNotification(notificationId, position)
            }
        })
    }

    private fun loadNotifications() {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val notifications = SupabaseClient.getFilteredNotifications(
                        currentUserId,
                        currentFilter,
                        currentStatus
                    )
                    if (notifications != null) {
                        runOnUiThread {
                            adapter.updateNotifications(notifications)
                            updateEmptyState(notifications.isEmpty())
                        }
                    } else {
                        runOnUiThread {
                            Toast.makeText(this@NotificationActivity, "خطا در بارگذاری اعلانات", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    Toast.makeText(this@NotificationActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun loadNotificationCounts() {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val counts = SupabaseClient.getNotificationCounts(currentUserId)
                    if (counts != null) {
                        runOnUiThread {
                            updateCountsUI(counts)
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun startNotificationSync() {
        val currentUserId = getCurrentUserId()
        if (currentUserId == null) {
            println("❌ User ID not found, cannot start notification sync")
            return
        }

        println("🔄 Starting real-time notification sync for user: $currentUserId")

        // اتصال به Realtime
        realtimeClient.connect()

        // Subscribe به اعلانات کاربر
        realtimeClient.subscribeToNotifications(currentUserId) { notifications ->
            runOnUiThread {
                println("📨 Received real-time notifications: ${notifications.size}")
                adapter.updateNotifications(notifications)
            }
        }

        // مشاهده وضعیت اتصال
        lifecycleScope.launch {
            realtimeClient.connectionState.collect { state ->
                when (state) {
                    SupabaseRealtimeClient.ConnectionState.CONNECTED -> {
                        println("✅ Real-time connection established")
                        // بارگذاری اولیه اعلانات
                        loadInitialNotifications()
                    }
                    SupabaseRealtimeClient.ConnectionState.DISCONNECTED -> {
                        println("❌ Real-time connection lost")
                    }
                    SupabaseRealtimeClient.ConnectionState.ERROR -> {
                        println("⚠️ Real-time connection error, falling back to polling")
                        startPollingFallback()
                    }
                    else -> {
                        println("🔄 Real-time connection state: $state")
                    }
                }
            }
        }
    }

    /**
     * بارگذاری اولیه اعلانات
     */
    private fun loadInitialNotifications() {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val notifications = SupabaseClient.getFilteredNotifications(
                        currentUserId,
                        currentFilter,
                        currentStatus
                    )
                    if (notifications != null) {
                        adapter.updateNotifications(notifications)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * Fallback به polling در صورت خطا در realtime
     */
    private fun startPollingFallback() {
        lifecycleScope.launch {
            while (true) {
                try {
                    kotlinx.coroutines.delay(10000) // هر 10 ثانیه چک کن (کمتر از قبل)

                    val currentUserId = getCurrentUserId()
                    if (currentUserId != null) {
                        val notifications = SupabaseClient.getFilteredNotifications(
                            currentUserId,
                            currentFilter,
                            currentStatus
                        )
                        if (notifications != null) {
                            runOnUiThread {
                                adapter.updateNotifications(notifications)
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    kotlinx.coroutines.delay(30000) // در صورت خطا، 30 ثانیه صبر کن
                }
            }
        }
    }

    private fun getCurrentUserId(): String? {
        return try {
            val authPrefs = getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
            // ابتدا تلاش برای دریافت username
            val username = authPrefs.getString("current_username", null)
            if (username != null) {
                println("🔍 NotificationActivity: Current username = $username")
                return username
            }

            // اگر username نبود، از user_id استفاده کن
            val userId = authPrefs.getString("current_user_id", null)
            println("🔍 NotificationActivity: Current user_id = $userId")
            return userId
        } catch (e: Exception) {
            println("❌ NotificationActivity: خطا در دریافت user ID: ${e.message}")
            null
        }
    }

    private fun setupFilters() {
        // تنظیم فیلتر نوع تراکنش
        findViewById<View>(R.id.chipAll)?.setOnClickListener { applyFilter("all") }
        findViewById<View>(R.id.chipSale)?.setOnClickListener { applyFilter("sale") }
        findViewById<View>(R.id.chipWithdrawal)?.setOnClickListener { applyFilter("withdrawal") }
        findViewById<View>(R.id.chipExpense)?.setOnClickListener { applyFilter("expense") }
        findViewById<View>(R.id.chipPurchase)?.setOnClickListener { applyFilter("purchase") }

        // تنظیم فیلتر وضعیت
        findViewById<View>(R.id.chipPending)?.setOnClickListener { applyStatusFilter("pending") }
        findViewById<View>(R.id.chipApproved)?.setOnClickListener { applyStatusFilter("approved") }
        findViewById<View>(R.id.chipRejected)?.setOnClickListener { applyStatusFilter("rejected") }
    }

    private fun applyFilter(filter: String) {
        currentFilter = filter
        loadNotifications()
    }

    private fun applyStatusFilter(status: String) {
        currentStatus = status
        loadNotifications()
    }

    private fun updateCountsUI(counts: Map<String, Int>) {
        findViewById<android.widget.TextView>(R.id.tvNewNotifications)?.text = counts["pending"]?.toString() ?: "0"
        findViewById<android.widget.TextView>(R.id.tvPendingApprovals)?.text = counts["pending"]?.toString() ?: "0"
    }

    private fun setupDatabase() {
        lifecycleScope.launch {
            try {
                // تلاش برای ایجاد جداول
                val tablesCreated = DatabaseSetup.createTables()

                if (tablesCreated) {
                    // بارگذاری اعلانات
                    loadNotifications()
                    loadNotificationCounts()

                    // شروع real-time sync اعلانات
                    startNotificationSync()
                } else {
                    runOnUiThread {
                        Toast.makeText(
                            this@NotificationActivity,
                            "لطفاً ابتدا جداول را در Supabase ایجاد کنید",
                            Toast.LENGTH_LONG
                        ).show()

                        // نمایش SQL script
                        println("SQL Script برای ایجاد جداول:")
                        println(DatabaseSetup.getSQLScript())
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    Toast.makeText(this@NotificationActivity, "خطا در راه‌اندازی دیتابیس", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        findViewById<View>(R.id.layoutEmpty)?.visibility = if (isEmpty) View.VISIBLE else View.GONE
        recyclerView.visibility = if (isEmpty) View.GONE else View.VISIBLE
    }

    private fun approveNotification(notificationId: String, position: Int) {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val success = SupabaseClient.updateNotificationStatus(
                        notificationId,
                        "approved"
                    )

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(this@NotificationActivity, "تراکنش تایید شد", Toast.LENGTH_SHORT).show()
                            loadNotifications() // بروزرسانی لیست
                            loadNotificationCounts() // بروزرسانی شمارش‌ها
                        } else {
                            Toast.makeText(this@NotificationActivity, "خطا در تایید تراکنش", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    Toast.makeText(this@NotificationActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun rejectNotification(notificationId: String, position: Int) {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val success = SupabaseClient.updateNotificationStatus(
                        notificationId,
                        "rejected"
                    )

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(this@NotificationActivity, "تراکنش رد شد", Toast.LENGTH_SHORT).show()
                            loadNotifications() // بروزرسانی لیست
                            loadNotificationCounts() // بروزرسانی شمارش‌ها
                        } else {
                            Toast.makeText(this@NotificationActivity, "خطا در رد تراکنش", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    Toast.makeText(this@NotificationActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // اتصال مجدد در صورت نیاز
        val lastState = realtimeClient.connectionState.replayCache.lastOrNull()
        if (lastState != SupabaseRealtimeClient.ConnectionState.CONNECTED) {
            startNotificationSync()
        }
    }

    override fun onPause() {
        super.onPause()
        // قطع اتصال برای صرفه‌جویی در باتری
        realtimeClient.disconnect()
    }

    override fun onDestroy() {
        super.onDestroy()
        // تمیز کردن منابع
        realtimeClient.disconnect()
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }
}
