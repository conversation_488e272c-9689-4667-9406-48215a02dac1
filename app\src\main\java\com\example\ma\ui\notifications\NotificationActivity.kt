package com.example.ma.ui.notifications

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import com.example.ma.data.remote.SupabaseClient
import com.example.ma.data.remote.SupabaseRealtimeClient
import com.example.ma.utils.DatabaseSetup
import com.google.android.material.appbar.MaterialToolbar
import kotlinx.coroutines.launch

/**
 * صفحه نمایش اعلانات با عکس پروفایل کاربران
 */
class NotificationActivity : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: NotificationAdapterNew
    private var currentViewType = "received" // "received" یا "sent"

    // Realtime client برای دریافت اعلانات لحظه‌ای
    private val realtimeClient = SupabaseRealtimeClient()
    private var subscriptionRef: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_notification)

        initializeViews()
        setupToolbar()
        setupRecyclerView()
        setupFilters()

        // راه‌اندازی دیتابیس و بارگذاری اعلانات
        setupDatabase()
    }

    private fun initializeViews() {
        recyclerView = findViewById(R.id.rvNotifications)

        // تنظیم دکمه بازگشت
        findViewById<View>(R.id.btnBack)?.setOnClickListener {
            onBackPressed()
        }
    }

    private fun setupToolbar() {
        // Header جدید نیاز به تنظیم خاصی ندارد
    }

    private fun setupRecyclerView() {
        adapter = NotificationAdapterNew(this, currentViewType)
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter

        // تنظیم listener برای دکمه‌های تایید/رد
        adapter.setOnNotificationActionListener(object : NotificationAdapter.OnNotificationActionListener {
            override fun onApprove(notificationId: String, position: Int) {
                approveNotification(notificationId, position)
            }

            override fun onReject(notificationId: String, position: Int) {
                rejectNotification(notificationId, position)
            }
        })
    }

    private fun loadNotifications() {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val notifications = if (currentViewType == "sent") {
                        // اعلانات ارسال شده توسط کاربر فعلی
                        SupabaseClient.getSentNotifications(currentUserId, null, null)
                    } else {
                        // اعلانات دریافت شده توسط کاربر فعلی
                        SupabaseClient.getFilteredNotifications(currentUserId, null, null)
                    }
                    if (notifications != null) {
                        runOnUiThread {
                            adapter.updateNotifications(notifications)
                            updateEmptyState(notifications.isEmpty())

                            // بروزرسانی تعداد اعلانات
                            loadNotificationCounts()

                            // شروع بروزرسانی خودکار
                            scheduleAutoRefresh()
                        }
                    } else {
                        runOnUiThread {
                            Toast.makeText(this@NotificationActivity, "خطا در بارگذاری اعلانات", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    Toast.makeText(this@NotificationActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun loadNotificationCounts() {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    // شمارش اعلانات دریافت شده (pending فقط)
                    val receivedNotifications = SupabaseClient.getFilteredNotifications(currentUserId, null, "pending")
                    val receivedCount = receivedNotifications?.size ?: 0

                    // شمارش اعلانات ارسال شده (pending فقط)
                    val sentNotifications = SupabaseClient.getSentNotifications(currentUserId, null, "pending")
                    val sentCount = sentNotifications?.size ?: 0

                    runOnUiThread {
                        // بروزرسانی UI با تعداد واقعی
                        updateNotificationCounts(receivedCount, sentCount)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun startNotificationSync() {
        val currentUserId = getCurrentUserId()
        if (currentUserId == null) {
            println("❌ User ID not found, cannot start notification sync")
            return
        }

        println("🔄 Starting real-time notification sync for user: $currentUserId")

        // اتصال به Realtime
        realtimeClient.connect()

        // Subscribe به اعلانات کاربر
        realtimeClient.subscribeToNotifications(currentUserId) { notifications ->
            runOnUiThread {
                println("📨 Received real-time notifications: ${notifications.size}")
                adapter.updateNotifications(notifications)
            }
        }

        // مشاهده وضعیت اتصال
        lifecycleScope.launch {
            realtimeClient.connectionState.collect { state ->
                when (state) {
                    SupabaseRealtimeClient.ConnectionState.CONNECTED -> {
                        println("✅ Real-time connection established")
                        // بارگذاری اولیه اعلانات
                        loadInitialNotifications()
                    }
                    SupabaseRealtimeClient.ConnectionState.DISCONNECTED -> {
                        println("❌ Real-time connection lost")
                    }
                    SupabaseRealtimeClient.ConnectionState.ERROR -> {
                        println("⚠️ Real-time connection error, falling back to polling")
                        startPollingFallback()
                    }
                    else -> {
                        println("🔄 Real-time connection state: $state")
                    }
                }
            }
        }
    }

    /**
     * بارگذاری اولیه اعلانات
     */
    private fun loadInitialNotifications() {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val notifications = SupabaseClient.getFilteredNotifications(
                        currentUserId,
                        currentFilter,
                        currentStatus
                    )
                    if (notifications != null) {
                        adapter.updateNotifications(notifications)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * Fallback به polling در صورت خطا در realtime
     */
    private fun startPollingFallback() {
        lifecycleScope.launch {
            while (true) {
                try {
                    kotlinx.coroutines.delay(10000) // هر 10 ثانیه چک کن (کمتر از قبل)

                    val currentUserId = getCurrentUserId()
                    if (currentUserId != null) {
                        val notifications = SupabaseClient.getFilteredNotifications(
                            currentUserId,
                            currentFilter,
                            currentStatus
                        )
                        if (notifications != null) {
                            runOnUiThread {
                                adapter.updateNotifications(notifications)
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    kotlinx.coroutines.delay(30000) // در صورت خطا، 30 ثانیه صبر کن
                }
            }
        }
    }

    private fun getCurrentUserId(): String? {
        return try {
            val authPrefs = getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
            var username = authPrefs.getString("current_username", null)
            println("🔍 NotificationActivity: Current username from prefs = $username")

            // اگر username موجود نیست، از user_id استخراج کن
            if (username.isNullOrEmpty()) {
                val userId = authPrefs.getString("current_user_id", null)
                println("🔍 NotificationActivity: Current user_id = $userId")

                username = when (userId) {
                    "ad28ba8f-0fa0-4420-8119-70fcacfd237e" -> {
                        // ذخیره username برای دفعات بعد
                        authPrefs.edit().putString("current_username", "Alikakai").apply()
                        println("✅ NotificationActivity: Username ذخیره شد: Alikakai")
                        "Alikakai"
                    }
                    "930b5d13-0408-4c57-965b-235c5532b35a" -> {
                        // ذخیره username برای دفعات بعد
                        authPrefs.edit().putString("current_username", "Miladnasiri").apply()
                        println("✅ NotificationActivity: Username ذخیره شد: Miladnasiri")
                        "Miladnasiri"
                    }
                    else -> {
                        println("❌ NotificationActivity: User ID ناشناخته: $userId")
                        null
                    }
                }
                println("🔍 NotificationActivity: Username extracted from user_id = $username")
            }

            return username
        } catch (e: Exception) {
            println("❌ NotificationActivity: خطا در دریافت user ID: ${e.message}")
            null
        }
    }

    private fun setupFilters() {
        // تنظیم فیلتر نوع نمایش (فقط دو تب ساده)
        findViewById<View>(R.id.chipReceived)?.setOnClickListener { applyViewTypeFilter("received") }
        findViewById<View>(R.id.chipSent)?.setOnClickListener { applyViewTypeFilter("sent") }
    }

    private fun applyViewTypeFilter(viewType: String) {
        currentViewType = viewType
        adapter.setViewType(viewType)
        loadNotifications()
    }

    /**
     * بروزرسانی تعداد اعلانات در UI
     */
    private fun updateNotificationCounts(receivedCount: Int, sentCount: Int) {
        // بروزرسانی تب دریافت شده
        val chipReceived = findViewById<View>(R.id.chipReceived)
        if (chipReceived is com.google.android.material.chip.Chip) {
            chipReceived.text = "📥 دریافت شده ($receivedCount)"
        }

        // بروزرسانی تب ارسال شده
        val chipSent = findViewById<View>(R.id.chipSent)
        if (chipSent is com.google.android.material.chip.Chip) {
            chipSent.text = "📤 ارسال شده ($sentCount)"
        }
    }

    /**
     * بروزرسانی خودکار هر 10 ثانیه
     */
    private fun scheduleAutoRefresh() {
        lifecycleScope.launch {
            kotlinx.coroutines.delay(10000) // 10 ثانیه
            if (!isFinishing && !isDestroyed) {
                loadNotifications()
            }
        }
    }

    private fun updateCountsUI(counts: Map<String, Int>) {
        findViewById<android.widget.TextView>(R.id.tvNewNotifications)?.text = counts["pending"]?.toString() ?: "0"
        findViewById<android.widget.TextView>(R.id.tvPendingApprovals)?.text = counts["pending"]?.toString() ?: "0"
    }

    private fun setupDatabase() {
        lifecycleScope.launch {
            try {
                // تلاش برای ایجاد جداول
                val tablesCreated = DatabaseSetup.createTables()

                if (tablesCreated) {
                    // بارگذاری اعلانات
                    loadNotifications()
                    loadNotificationCounts()

                    // شروع real-time sync اعلانات
                    startNotificationSync()
                } else {
                    runOnUiThread {
                        Toast.makeText(
                            this@NotificationActivity,
                            "لطفاً ابتدا جداول را در Supabase ایجاد کنید",
                            Toast.LENGTH_LONG
                        ).show()

                        // نمایش SQL script
                        println("SQL Script برای ایجاد جداول:")
                        println(DatabaseSetup.getSQLScript())
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    Toast.makeText(this@NotificationActivity, "خطا در راه‌اندازی دیتابیس", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        findViewById<View>(R.id.layoutEmpty)?.visibility = if (isEmpty) View.VISIBLE else View.GONE
        recyclerView.visibility = if (isEmpty) View.GONE else View.VISIBLE
    }

    private fun approveNotification(notificationId: String, position: Int) {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val success = SupabaseClient.updateNotificationStatus(
                        notificationId,
                        "approved"
                    )

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(this@NotificationActivity, "تراکنش تایید شد", Toast.LENGTH_SHORT).show()
                            loadNotifications() // بروزرسانی لیست
                            loadNotificationCounts() // بروزرسانی شمارش‌ها
                        } else {
                            Toast.makeText(this@NotificationActivity, "خطا در تایید تراکنش", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    Toast.makeText(this@NotificationActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun rejectNotification(notificationId: String, position: Int) {
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val success = SupabaseClient.updateNotificationStatus(
                        notificationId,
                        "rejected"
                    )

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(this@NotificationActivity, "تراکنش رد شد", Toast.LENGTH_SHORT).show()
                            loadNotifications() // بروزرسانی لیست
                            loadNotificationCounts() // بروزرسانی شمارش‌ها
                        } else {
                            Toast.makeText(this@NotificationActivity, "خطا در رد تراکنش", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    Toast.makeText(this@NotificationActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // اتصال مجدد در صورت نیاز
        val lastState = realtimeClient.connectionState.replayCache.lastOrNull()
        if (lastState != SupabaseRealtimeClient.ConnectionState.CONNECTED) {
            startNotificationSync()
        }
    }

    override fun onPause() {
        super.onPause()
        // قطع اتصال برای صرفه‌جویی در باتری
        realtimeClient.disconnect()
    }

    override fun onDestroy() {
        super.onDestroy()
        // تمیز کردن منابع
        realtimeClient.disconnect()
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }
}
