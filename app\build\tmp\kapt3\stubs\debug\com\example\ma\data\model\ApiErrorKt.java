package com.example.ma.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a\u0016\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\n\b\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u0004\u00a8\u0006\u0005"}, d2 = {"toApiError", "Lcom/example/ma/data/model/ApiError;", "", "message", "", "app_debug"})
public final class ApiErrorKt {
    
    /**
     * تبدیل HTTP status code به ApiError مناسب
     */
    @org.jetbrains.annotations.NotNull
    public static final com.example.ma.data.model.ApiError toApiError(int $this$toApiError, @org.jetbrains.annotations.Nullable
    java.lang.String message) {
        return null;
    }
}