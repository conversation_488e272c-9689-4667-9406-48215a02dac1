// تنظیمات Proxy های متعدد برای fallback
def proxyList = [
    [host: "************", port: 80],
    [host: "***************", port: 5326],
    [host: "***********", port: 18080],
    [host: "**************", port: 6289],
    [host: "*************", port: 3129],
    [host: "**************", port: 6816],
    [host: "************", port: 3128],
    [host: "*************", port: 1000]
]

// تابع تست proxy
def testProxy(host, port) {
    try {
        def url = new URL("http://www.google.com")
        def proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port))
        def connection = url.openConnection(proxy)
        connection.setConnectTimeout(5000)
        connection.setReadTimeout(5000)
        connection.connect()
        return true
    } catch (Exception e) {
        return false
    }
}

// پیدا کردن اولین proxy که کار می‌کنه
def workingProxy = null
for (proxy in proxyList) {
    println "Testing proxy: ${proxy.host}:${proxy.port}"
    if (testProxy(proxy.host, proxy.port)) {
        workingProxy = proxy
        println "Working proxy found: ${proxy.host}:${proxy.port}"
        break
    }
}

// تنظیم proxy در صورت پیدا شدن
if (workingProxy) {
    System.setProperty("http.proxyHost", workingProxy.host)
    System.setProperty("http.proxyPort", workingProxy.port.toString())
    System.setProperty("https.proxyHost", workingProxy.host)
    System.setProperty("https.proxyPort", workingProxy.port.toString())
    println "Proxy configured: ${workingProxy.host}:${workingProxy.port}"
} else {
    println "No working proxy found, using direct connection"
}
