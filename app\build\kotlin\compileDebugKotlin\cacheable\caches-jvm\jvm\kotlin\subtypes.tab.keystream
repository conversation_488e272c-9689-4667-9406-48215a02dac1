"com.example.ma.data.model.ApiError#com.example.ma.data.model.ApiResultkotlin.Enum"com.example.ma.ui.auth.LoginResult#androidx.lifecycle.AndroidViewModelcom.example.ma.ui.model.UiState1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder(androidx.appcompat.app.AppCompatActivityandroid.text.TextWatcherandroid.app.Applicationandroidx.room.RoomDatabase#androidx.room.RoomDatabase.Callbackjava.lang.Exception$androidx.fragment.app.DialogFragment!kotlinx.coroutines.CoroutineScope androidx.viewbinding.ViewBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          