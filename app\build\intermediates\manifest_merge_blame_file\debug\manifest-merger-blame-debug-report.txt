1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.ma"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
14-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:9:5-81
15-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.CAMERA" />
16-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:10:5-65
16-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:10:22-62
17
18    <queries>
18-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:9:5-20:15
19        <intent>
19-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:10:9-16:18
20            <action android:name="android.intent.action.GET_CONTENT" />
20-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:11:13-72
20-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:11:21-69
21
22            <category android:name="android.intent.category.OPENABLE" />
22-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:13:13-73
22-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:13:23-70
23
24            <data android:mimeType="*/*" />
24-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:15:13-44
24-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:15:19-41
25        </intent>
26        <intent>
26-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:17:9-19:18
27            <action android:name="android.media.action.IMAGE_CAPTURE" />
27-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:18:13-73
27-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:18:21-70
28        </intent>
29    </queries>
30
31    <permission
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
32        android:name="com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
36
37    <application
37-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:12:5-93:19
38        android:name="com.example.ma.MAApplication"
38-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:13:9-38
39        android:allowBackup="true"
39-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:14:9-35
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8687115333779df90d0ee3592bb62d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
41        android:dataExtractionRules="@xml/data_extraction_rules"
41-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:15:9-65
42        android:debuggable="true"
43        android:fullBackupContent="@xml/backup_rules"
43-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:16:9-54
44        android:icon="@mipmap/ic_launcher"
44-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:18:9-43
45        android:label="@string/app_name"
45-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:19:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:20:9-54
47        android:supportsRtl="true"
47-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:21:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.MA" >
49-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:22:9-40
50        <activity
50-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:24:9-32:20
51            android:name="com.example.ma.ui.auth.LoginActivity"
51-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:25:13-50
52            android:exported="true" >
52-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:26:13-36
53            <intent-filter>
53-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:27:13-31:29
54                <action android:name="android.intent.action.MAIN" />
54-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:28:17-69
54-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:28:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:30:17-77
56-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:30:27-74
57            </intent-filter>
58        </activity>
59        <activity
59-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:34:9-36:40
60            android:name="com.example.ma.MainActivity"
60-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:35:13-41
61            android:exported="false" />
61-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:36:13-37
62        <activity
62-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:38:9-41:47
63            android:name="com.example.ma.ui.profile.ProfileActivity"
63-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:39:13-55
64            android:exported="false"
64-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:40:13-37
65            android:theme="@style/Theme.MA" />
65-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:41:13-44
66        <activity
66-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:43:9-46:47
67            android:name="com.example.ma.ui.notifications.NotificationActivity"
67-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:44:13-66
68            android:exported="false"
68-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:45:13-37
69            android:theme="@style/Theme.MA" />
69-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:46:13-44
70        <activity
70-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:48:9-51:47
71            android:name="com.example.ma.ui.reports.ReportsActivity"
71-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:49:13-55
72            android:exported="false"
72-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:50:13-37
73            android:theme="@style/Theme.MA" />
73-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:51:13-44
74
75        <!-- صفحات مالی -->
76        <activity
76-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:54:9-57:47
77            android:name="com.example.ma.ui.financial.FinancialActivity"
77-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:55:13-59
78            android:exported="false"
78-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:56:13-37
79            android:theme="@style/Theme.MA" />
79-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:57:13-44
80        <activity
80-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:59:9-62:47
81            android:name="com.example.ma.ui.financial.ExpenseActivity"
81-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:60:13-57
82            android:exported="false"
82-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:61:13-37
83            android:theme="@style/Theme.MA" />
83-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:62:13-44
84        <activity
84-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:64:9-67:47
85            android:name="com.example.ma.ui.financial.WithdrawalActivity"
85-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:65:13-60
86            android:exported="false"
86-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:66:13-37
87            android:theme="@style/Theme.MA" />
87-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:67:13-44
88        <activity
88-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:69:9-72:47
89            android:name="com.example.ma.ui.financial.TransactionsActivity"
89-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:70:13-62
90            android:exported="false"
90-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:71:13-37
91            android:theme="@style/Theme.MA" />
91-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:72:13-44
92        <activity
92-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:74:9-77:47
93            android:name="com.example.ma.ui.statistics.StatisticsActivity"
93-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:75:13-61
94            android:exported="false"
94-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:76:13-37
95            android:theme="@style/Theme.MA" />
95-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:77:13-44
96        <activity
96-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:79:9-82:47
97            android:name="com.example.ma.ui.inventory.InventoryActivity"
97-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:80:13-59
98            android:exported="false"
98-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:81:13-37
99            android:theme="@style/Theme.MA" />
99-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:82:13-44
100        <activity
100-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:84:9-87:47
101            android:name="com.example.ma.ui.setup.DatabaseSetupActivity"
101-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:85:13-59
102            android:exported="false"
102-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:86:13-37
103            android:theme="@style/Theme.MA" />
103-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:87:13-44
104
105        <!-- Image Cropper Activity -->
106        <activity
106-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:90:9-92:59
107            android:name="com.canhub.cropper.CropImageActivity"
107-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:91:13-64
108            android:exported="true"
108-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:35:13-36
109            android:theme="@style/Base.Theme.AppCompat" />
109-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:92:13-56
110
111        <provider
111-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:23:9-31:20
112            android:name="com.canhub.cropper.CropFileProvider"
112-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:24:13-63
113            android:authorities="com.example.ma.cropper.fileprovider"
113-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:25:13-72
114            android:exported="false"
114-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:26:13-37
115            android:grantUriPermissions="true" >
115-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:27:13-47
116            <meta-data
116-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:28:13-30:62
117                android:name="android.support.FILE_PROVIDER_PATHS"
117-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:29:17-67
118                android:resource="@xml/library_file_paths" />
118-->[com.github.CanHub:Android-Image-Cropper:4.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\9a32c99e97ad43150df9bf3ac7474efb\transformed\Android-Image-Cropper-4.3.3\AndroidManifest.xml:30:17-59
119        </provider>
120        <provider
120-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
121            android:name="androidx.startup.InitializationProvider"
121-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
122            android:authorities="com.example.ma.androidx-startup"
122-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
123            android:exported="false" >
123-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
124            <meta-data
124-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.emoji2.text.EmojiCompatInitializer"
125-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
126                android:value="androidx.startup" />
126-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cc3d4663c780a4f3438da517e912c0d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\635dbda20e34315f137b58ba99f130f9\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
132                android:value="androidx.startup" />
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
133        </provider>
134
135        <uses-library
135-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
136            android:name="androidx.window.extensions"
136-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
137            android:required="false" />
137-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
138        <uses-library
138-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
139            android:name="androidx.window.sidecar"
139-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
140            android:required="false" />
140-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b8c99ce2c19ed9ca486645347a0147b\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
141
142        <service
142-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
143            android:name="androidx.room.MultiInstanceInvalidationService"
143-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
144            android:directBootAware="true"
144-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
145            android:exported="false" />
145-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a24d57f787f9eab76c4036e8c585ea3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
146
147        <receiver
147-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
148            android:name="androidx.profileinstaller.ProfileInstallReceiver"
148-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
149            android:directBootAware="false"
149-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
150            android:enabled="true"
150-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
151            android:exported="true"
151-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
152            android:permission="android.permission.DUMP" >
152-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
154                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
154-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
154-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
155            </intent-filter>
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
157                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
157-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
157-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
160                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
160-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
160-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
161            </intent-filter>
162            <intent-filter>
162-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
163                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
163-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
163-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad064ffd64e190525d45a5f7adcbb659\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
164            </intent-filter>
165        </receiver>
166    </application>
167
168</manifest>
