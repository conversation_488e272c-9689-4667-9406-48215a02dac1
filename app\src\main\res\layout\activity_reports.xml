<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- عنوان صفحه -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="گزارش کامل مالی شراکت"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:fontFamily="sans-serif-medium" />

        <!-- کارت اطلاعات کلی -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 اطلاعات کلی"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    android:layout_marginBottom="12dp" />

                <!-- کل فروش -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="کل فروش:"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvTotalSales"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color" />

                </LinearLayout>

                <!-- کل هزینه -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="کل هزینه:"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvTotalExpenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/error_color" />

                </LinearLayout>

                <!-- سود خالص -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="سود خالص:"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvNetProfit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- کارت سهم میلاد -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="👤 میلاد نصیری"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/info_color"
                    android:layout_marginBottom="12dp" />

                <!-- سهم میلاد -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="سهم از سود:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvMiladShare"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- برداشت میلاد -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="برداشت شخصی:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvMiladWithdrawals"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/warning_color" />

                </LinearLayout>

                <!-- موجودی نهایی میلاد -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="موجودی نهایی:"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvMiladFinalBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color" />

                </LinearLayout>

                <!-- تفکیک حساب‌های میلاد -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="تفکیک حساب‌ها:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💰 نقدی:"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvMiladCashBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="12sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💳 کارت:"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvMiladCardBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="12sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="👤 شخصی:"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvMiladPersonalBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="12sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- کارت سهم علی -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="👤 علی کاکایی"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/warning_color"
                    android:layout_marginBottom="12dp" />

                <!-- سهم علی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="سهم از سود:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvAliShare"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- برداشت علی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="برداشت شخصی:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvAliWithdrawals"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/warning_color" />

                </LinearLayout>

                <!-- موجودی نهایی علی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="موجودی نهایی:"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvAliFinalBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color" />

                </LinearLayout>

                <!-- تفکیک حساب‌های علی -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="تفکیک حساب‌ها:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💰 نقدی:"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvAliCashBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="12sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💳 کارت:"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvAliCardBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="12sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="👤 شخصی:"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvAliPersonalBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="12sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- زمان آخرین بروزرسانی -->
        <TextView
            android:id="@+id/tvLastCalculated"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="آخرین بروزرسانی: --"
            android:textSize="12sp"
            android:textColor="@color/text_hint"
            android:gravity="center"
            android:layout_marginTop="16dp" />

    </LinearLayout>

</ScrollView>
