<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color"
    android:fitsSystemWindows="true">

    <!-- Header مدرن -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:orientation="horizontal"
        android:background="@drawable/gradient_primary"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:gravity="center_vertical"
        android:elevation="8dp">

        <!-- دکمه بازگشت -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/btnBack"
            android:layout_width="36dp"
            android:layout_height="36dp"
            app:cardCornerRadius="18dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_arrow_back"
                app:tint="@color/primary_color" />

        </com.google.android.material.card.MaterialCardView>

        <!-- عنوان -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="اعلانات و تاییدیه‌ها"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_white"
            android:gravity="center"
            android:fontFamily="sans-serif-medium" />

        <!-- آیکون اعلانات -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="36dp"
            android:layout_height="36dp"
            app:cardCornerRadius="18dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_notifications"
                app:tint="@color/primary_color" />

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <!-- محتوای اصلی -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- آمار اعلانات -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center">

                <!-- اعلانات جدید -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/surface_color">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvNewNotifications"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/warning_color"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="جدید"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:fontFamily="sans-serif" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- در انتظار تایید -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/surface_color">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvPendingApprovals"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="2"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/info_color"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="در انتظار"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:fontFamily="sans-serif" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- فیلترهای نوع تراکنش -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="فیلتر بر اساس نوع:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="sans-serif-medium" />

                <com.google.android.material.chip.ChipGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:singleSelection="true"
                    app:checkedChip="@id/chipAll">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipAll"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="همه"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/primary_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipSale"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="فروش"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/success_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipWithdrawal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="برداشت شخصی"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/info_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipExpense"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="هزینه"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/error_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipPurchase"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="خرید"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/warning_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                </com.google.android.material.chip.ChipGroup>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="فیلتر بر اساس وضعیت:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="sans-serif-medium" />

                <com.google.android.material.chip.ChipGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipPending"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="در انتظار"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/warning_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipApproved"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تایید شده"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/success_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipRejected"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="رد شده"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/error_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                </com.google.android.material.chip.ChipGroup>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="نوع نمایش:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="sans-serif-medium" />

                <com.google.android.material.chip.ChipGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:singleSelection="true"
                    app:checkedChip="@id/chipReceived">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipReceived"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📥 دریافت شده"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/primary_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipSent"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📤 ارسال شده"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/surface_color"
                        app:chipStrokeColor="@color/info_color"
                        app:chipStrokeWidth="1dp"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                </com.google.android.material.chip.ChipGroup>

            </LinearLayout>

            <!-- لیست اعلانات -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvNotifications"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                android:padding="16dp"
                android:clipToPadding="false"
                tools:listitem="@layout/item_notification" />

            <!-- پیام خالی بودن -->
            <LinearLayout
                android:id="@+id/layoutEmpty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_notifications"
                    android:layout_marginBottom="16dp"
                    app:tint="@color/text_hint" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="هیچ اعلانی موجود نیست"
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="تمام تراکنش‌ها و فعالیت‌ها اینجا نمایش داده می‌شوند"
                    android:textSize="14sp"
                    android:textColor="@color/text_hint"
                    android:gravity="center"
                    android:layout_marginTop="8dp"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
