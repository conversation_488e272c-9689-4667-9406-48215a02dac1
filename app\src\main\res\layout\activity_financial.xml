<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:background="@color/primary_color"
            android:elevation="4dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="مدیریت مالی"
            app:titleTextColor="@color/text_white" />

        <!-- موجودی حساب‌ها -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💰 موجودی حساب‌ها"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp"
                    android:fontFamily="sans-serif-medium" />

                <!-- موجودی شرکت -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🏢 موجودی شرکت:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvCompanyBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:fontFamily="sans-serif-medium"
                        tools:text="2,500,000 تومان" />

                </LinearLayout>

                <!-- کارت شخصی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💳 کارت شخصی:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvPersonalCardBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:fontFamily="sans-serif-medium"
                        tools:text="1,200,000 تومان" />

                </LinearLayout>

                <!-- نقد شخصی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💵 نقد شخصی:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvPersonalCashBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:fontFamily="sans-serif-medium"
                        tools:text="800,000 تومان" />

                </LinearLayout>

                <!-- سود قابل برداشت -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/outline"
                    android:layout_marginVertical="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💎 سود قابل برداشت:"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color"
                        android:fontFamily="sans-serif-medium" />

                    <TextView
                        android:id="@+id/tvProfitShare"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="4,500,000 تومان" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- گزینه‌های مالی -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">

            <!-- ردیف اول -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- هزینه مشارکتی -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardSharedExpense"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="@color/warning_color"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🤝"
                            android:textSize="32sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="هزینه مشارکتی"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_white"
                            android:gravity="center"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="غذا، تفریح (50/50)"
                            android:textSize="10sp"
                            android:textColor="@color/text_white"
                            android:gravity="center"
                            android:fontFamily="sans-serif" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- هزینه شرکتی -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardBusinessExpense"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="@color/error_color"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💼"
                            android:textSize="32sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="هزینه عملیاتی"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_white"
                            android:gravity="center"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="مواد اولیه، اجاره"
                            android:textSize="10sp"
                            android:textColor="@color/text_white"
                            android:gravity="center"
                            android:fontFamily="sans-serif" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- ردیف دوم -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- برداشت شخصی -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardPersonalWithdrawal"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="@color/info_color"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="👤"
                            android:textSize="32sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="برداشت سرمایه"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_white"
                            android:gravity="center"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="از سهم شخصی"
                            android:textSize="10sp"
                            android:textColor="@color/text_white"
                            android:gravity="center"
                            android:fontFamily="sans-serif" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- تراکنش‌ها -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardTransactions"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="@color/success_color"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📋"
                            android:textSize="32sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تراکنش‌ها"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_white"
                            android:gravity="center"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="مشاهده و جستجو"
                            android:textSize="10sp"
                            android:textColor="@color/text_white"
                            android:gravity="center"
                            android:fontFamily="sans-serif" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
