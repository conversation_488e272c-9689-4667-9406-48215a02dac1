<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- تم مشکی -->
    <style name="Base.Theme.MA" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- رنگ‌های اصلی (طلایی) -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/background_color</item>

        <!-- رنگ‌های ثانویه -->
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/secondary_color</item>
        <item name="colorOnSecondary">@color/background_color</item>

        <!-- پس‌زمینه مشکی -->
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- رنگ‌های خطا -->
        <item name="colorError">@color/error_color</item>
        <item name="colorOnError">@color/text_white</item>

        <!-- وضعیت نوار وضعیت -->
        <item name="android:statusBarColor">@color/background_color</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- رنگ نوار ناوبری -->
        <item name="android:navigationBarColor">@color/background_color</item>
        <item name="android:windowLightNavigationBar">false</item>

        <!-- رنگ‌های اضافی برای Material Components -->
        <item name="colorControlNormal">@color/text_secondary</item>
        <item name="colorControlActivated">@color/primary_color</item>
        <item name="colorControlHighlight">@color/primary_light</item>
    </style>

    <style name="Theme.MA" parent="Base.Theme.MA" />
</resources>