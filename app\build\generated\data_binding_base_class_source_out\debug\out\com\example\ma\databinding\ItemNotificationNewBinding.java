// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNotificationNewBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton btnApprove;

  @NonNull
  public final MaterialButton btnReject;

  @NonNull
  public final MaterialCardView cardStatus;

  @NonNull
  public final ImageView ivProfilePicture;

  @NonNull
  public final LinearLayout layoutAccount;

  @NonNull
  public final LinearLayout layoutActions;

  @NonNull
  public final LinearLayout layoutDescription;

  @NonNull
  public final LinearLayout layoutQuantity;

  @NonNull
  public final TextView tvAccount;

  @NonNull
  public final TextView tvAccountLabel;

  @NonNull
  public final TextView tvAmount;

  @NonNull
  public final TextView tvDate;

  @NonNull
  public final TextView tvDescription;

  @NonNull
  public final TextView tvQuantity;

  @NonNull
  public final TextView tvQuantityLabel;

  @NonNull
  public final TextView tvSenderName;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvTransactionType;

  private ItemNotificationNewBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton btnApprove, @NonNull MaterialButton btnReject,
      @NonNull MaterialCardView cardStatus, @NonNull ImageView ivProfilePicture,
      @NonNull LinearLayout layoutAccount, @NonNull LinearLayout layoutActions,
      @NonNull LinearLayout layoutDescription, @NonNull LinearLayout layoutQuantity,
      @NonNull TextView tvAccount, @NonNull TextView tvAccountLabel, @NonNull TextView tvAmount,
      @NonNull TextView tvDate, @NonNull TextView tvDescription, @NonNull TextView tvQuantity,
      @NonNull TextView tvQuantityLabel, @NonNull TextView tvSenderName, @NonNull TextView tvStatus,
      @NonNull TextView tvTransactionType) {
    this.rootView = rootView;
    this.btnApprove = btnApprove;
    this.btnReject = btnReject;
    this.cardStatus = cardStatus;
    this.ivProfilePicture = ivProfilePicture;
    this.layoutAccount = layoutAccount;
    this.layoutActions = layoutActions;
    this.layoutDescription = layoutDescription;
    this.layoutQuantity = layoutQuantity;
    this.tvAccount = tvAccount;
    this.tvAccountLabel = tvAccountLabel;
    this.tvAmount = tvAmount;
    this.tvDate = tvDate;
    this.tvDescription = tvDescription;
    this.tvQuantity = tvQuantity;
    this.tvQuantityLabel = tvQuantityLabel;
    this.tvSenderName = tvSenderName;
    this.tvStatus = tvStatus;
    this.tvTransactionType = tvTransactionType;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNotificationNewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNotificationNewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_notification_new, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNotificationNewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnApprove;
      MaterialButton btnApprove = ViewBindings.findChildViewById(rootView, id);
      if (btnApprove == null) {
        break missingId;
      }

      id = R.id.btnReject;
      MaterialButton btnReject = ViewBindings.findChildViewById(rootView, id);
      if (btnReject == null) {
        break missingId;
      }

      id = R.id.cardStatus;
      MaterialCardView cardStatus = ViewBindings.findChildViewById(rootView, id);
      if (cardStatus == null) {
        break missingId;
      }

      id = R.id.ivProfilePicture;
      ImageView ivProfilePicture = ViewBindings.findChildViewById(rootView, id);
      if (ivProfilePicture == null) {
        break missingId;
      }

      id = R.id.layoutAccount;
      LinearLayout layoutAccount = ViewBindings.findChildViewById(rootView, id);
      if (layoutAccount == null) {
        break missingId;
      }

      id = R.id.layoutActions;
      LinearLayout layoutActions = ViewBindings.findChildViewById(rootView, id);
      if (layoutActions == null) {
        break missingId;
      }

      id = R.id.layoutDescription;
      LinearLayout layoutDescription = ViewBindings.findChildViewById(rootView, id);
      if (layoutDescription == null) {
        break missingId;
      }

      id = R.id.layoutQuantity;
      LinearLayout layoutQuantity = ViewBindings.findChildViewById(rootView, id);
      if (layoutQuantity == null) {
        break missingId;
      }

      id = R.id.tvAccount;
      TextView tvAccount = ViewBindings.findChildViewById(rootView, id);
      if (tvAccount == null) {
        break missingId;
      }

      id = R.id.tvAccountLabel;
      TextView tvAccountLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountLabel == null) {
        break missingId;
      }

      id = R.id.tvAmount;
      TextView tvAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvAmount == null) {
        break missingId;
      }

      id = R.id.tvDate;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      id = R.id.tvDescription;
      TextView tvDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDescription == null) {
        break missingId;
      }

      id = R.id.tvQuantity;
      TextView tvQuantity = ViewBindings.findChildViewById(rootView, id);
      if (tvQuantity == null) {
        break missingId;
      }

      id = R.id.tvQuantityLabel;
      TextView tvQuantityLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvQuantityLabel == null) {
        break missingId;
      }

      id = R.id.tvSenderName;
      TextView tvSenderName = ViewBindings.findChildViewById(rootView, id);
      if (tvSenderName == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tvTransactionType;
      TextView tvTransactionType = ViewBindings.findChildViewById(rootView, id);
      if (tvTransactionType == null) {
        break missingId;
      }

      return new ItemNotificationNewBinding((MaterialCardView) rootView, btnApprove, btnReject,
          cardStatus, ivProfilePicture, layoutAccount, layoutActions, layoutDescription,
          layoutQuantity, tvAccount, tvAccountLabel, tvAmount, tvDate, tvDescription, tvQuantity,
          tvQuantityLabel, tvSenderName, tvStatus, tvTransactionType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
