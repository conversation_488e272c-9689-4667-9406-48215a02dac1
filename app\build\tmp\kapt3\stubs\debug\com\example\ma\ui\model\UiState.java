package com.example.ma.ui.model;

/**
 * حالت‌های مختلف UI
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u00012\u00020\u0002:\u0004\u000e\u000f\u0010\u0011B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0003J\r\u0010\n\u001a\u0004\u0018\u00018\u0000\u00a2\u0006\u0002\u0010\u000bJ\b\u0010\f\u001a\u0004\u0018\u00010\rR\u0011\u0010\u0004\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\u0004\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\u0007\u0010\u0006R\u0011\u0010\b\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\u0006R\u0011\u0010\t\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\t\u0010\u0006\u0082\u0001\u0004\u0012\u0013\u0014\u0015\u00a8\u0006\u0016"}, d2 = {"Lcom/example/ma/ui/model/UiState;", "T", "", "()V", "isError", "", "()Z", "isIdle", "isLoading", "isSuccess", "getDataOrNull", "()Ljava/lang/Object;", "getErrorOrNull", "Lcom/example/ma/data/model/ApiError;", "Error", "Idle", "Loading", "Success", "Lcom/example/ma/ui/model/UiState$Error;", "Lcom/example/ma/ui/model/UiState$Idle;", "Lcom/example/ma/ui/model/UiState$Loading;", "Lcom/example/ma/ui/model/UiState$Success;", "app_debug"})
public abstract class UiState<T extends java.lang.Object> {
    
    private UiState() {
        super();
    }
    
    public final boolean isIdle() {
        return false;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isSuccess() {
        return false;
    }
    
    public final boolean isError() {
        return false;
    }
    
    /**
     * دریافت داده در صورت موفقیت
     */
    @org.jetbrains.annotations.Nullable
    public final T getDataOrNull() {
        return null;
    }
    
    /**
     * دریافت خطا در صورت وجود
     */
    @org.jetbrains.annotations.Nullable
    public final com.example.ma.data.model.ApiError getErrorOrNull() {
        return null;
    }
    
    /**
     * حالت خطا - خطایی رخ داده
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0001\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\t\u0010\f\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0006H\u00c6\u0003J\u001d\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00062\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0016"}, d2 = {"Lcom/example/ma/ui/model/UiState$Error;", "Lcom/example/ma/ui/model/UiState;", "", "error", "Lcom/example/ma/data/model/ApiError;", "canRetry", "", "(Lcom/example/ma/data/model/ApiError;Z)V", "getCanRetry", "()Z", "getError", "()Lcom/example/ma/data/model/ApiError;", "component1", "component2", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class Error extends com.example.ma.ui.model.UiState {
        @org.jetbrains.annotations.NotNull
        private final com.example.ma.data.model.ApiError error = null;
        private final boolean canRetry = false;
        
        public Error(@org.jetbrains.annotations.NotNull
        com.example.ma.data.model.ApiError error, boolean canRetry) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError getError() {
            return null;
        }
        
        public final boolean getCanRetry() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError component1() {
            return null;
        }
        
        public final boolean component2() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.ui.model.UiState.Error copy(@org.jetbrains.annotations.NotNull
        com.example.ma.data.model.ApiError error, boolean canRetry) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * حالت اولیه - هنوز هیچ عملیاتی انجام نشده
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0001\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/example/ma/ui/model/UiState$Idle;", "Lcom/example/ma/ui/model/UiState;", "", "()V", "app_debug"})
    public static final class Idle extends com.example.ma.ui.model.UiState {
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.ui.model.UiState.Idle INSTANCE = null;
        
        private Idle() {
        }
    }
    
    /**
     * حالت بارگذاری - در حال انجام عملیات
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0001\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/example/ma/ui/model/UiState$Loading;", "Lcom/example/ma/ui/model/UiState;", "", "()V", "app_debug"})
    public static final class Loading extends com.example.ma.ui.model.UiState {
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.ui.model.UiState.Loading INSTANCE = null;
        
        private Loading() {
        }
    }
    
    /**
     * حالت موفقیت - عملیات با موفقیت انجام شد
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u0000*\u0004\b\u0001\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00028\u0001\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\b\u001a\u00028\u0001H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00010\u00002\b\b\u0002\u0010\u0003\u001a\u00028\u0001H\u00c6\u0001\u00a2\u0006\u0002\u0010\nJ\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001R\u0013\u0010\u0003\u001a\u00028\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0013"}, d2 = {"Lcom/example/ma/ui/model/UiState$Success;", "T", "Lcom/example/ma/ui/model/UiState;", "data", "(Ljava/lang/Object;)V", "getData", "()Ljava/lang/Object;", "Ljava/lang/Object;", "component1", "copy", "(Ljava/lang/Object;)Lcom/example/ma/ui/model/UiState$Success;", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class Success<T extends java.lang.Object> extends com.example.ma.ui.model.UiState<T> {
        private final T data = null;
        
        public Success(T data) {
        }
        
        public final T getData() {
            return null;
        }
        
        public final T component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.ui.model.UiState.Success<T> copy(T data) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
}