package com.example.ma.config;

/**
 * تنظیمات اپلیکیشن
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010$\n\u0002\b\t\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0007\u0016\u0017\u0018\u0019\u001a\u001b\u001cB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u0014J\u0006\u0010\u0015\u001a\u00020\fR\u0014\u0010\u0003\u001a\u00020\u0004X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0014\u0010\u0007\u001a\u00020\bX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0014\u0010\u000b\u001a\u00020\fX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0014\u0010\u000f\u001a\u00020\bX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\nR\u0014\u0010\u0011\u001a\u00020\bX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\n\u00a8\u0006\u001d"}, d2 = {"Lcom/example/ma/config/AppConfig;", "", "()V", "APP_VERSION_CODE", "", "getAPP_VERSION_CODE", "()I", "APP_VERSION_NAME", "", "getAPP_VERSION_NAME", "()Ljava/lang/String;", "DEBUG_MODE", "", "getDEBUG_MODE", "()Z", "SUPABASE_ANON_KEY", "getSUPABASE_ANON_KEY", "SUPABASE_URL", "getSUPABASE_URL", "getConfigInfo", "", "validateConfig", "Business", "Cache", "File", "Network", "Realtime", "Security", "UI", "app_debug"})
public final class AppConfig {
    
    /**
     * URL سرور Supabase
     */
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String SUPABASE_URL = "https://secoqjdcrszjseedprqk.supabase.co";
    
    /**
     * کلید Anonymous Supabase
     */
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNlY29xamRjcnN6anNlZWRwcnFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2NTQwMDksImV4cCI6MjA2NzIzMDAwOX0.UTKnCn7TDPLdy9ohdoh5fp2h6he3lnpEu-0MZv1P6fQ";
    
    /**
     * نسخه اپلیکیشن
     */
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String APP_VERSION_NAME = "1.0.0";
    
    /**
     * کد نسخه اپلیکیشن
     */
    private static final int APP_VERSION_CODE = com.example.ma.BuildConfig.APP_VERSION_CODE;
    
    /**
     * حالت دیباگ
     */
    private static final boolean DEBUG_MODE = com.example.ma.BuildConfig.DEBUG_MODE;
    @org.jetbrains.annotations.NotNull
    public static final com.example.ma.config.AppConfig INSTANCE = null;
    
    private AppConfig() {
        super();
    }
    
    /**
     * URL سرور Supabase
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSUPABASE_URL() {
        return null;
    }
    
    /**
     * کلید Anonymous Supabase
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSUPABASE_ANON_KEY() {
        return null;
    }
    
    /**
     * نسخه اپلیکیشن
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getAPP_VERSION_NAME() {
        return null;
    }
    
    /**
     * کد نسخه اپلیکیشن
     */
    public final int getAPP_VERSION_CODE() {
        return 0;
    }
    
    /**
     * حالت دیباگ
     */
    public final boolean getDEBUG_MODE() {
        return false;
    }
    
    /**
     * بررسی صحت تنظیمات
     */
    public final boolean validateConfig() {
        return false;
    }
    
    /**
     * دریافت اطلاعات تنظیمات برای لاگ
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.lang.Object> getConfigInfo() {
        return null;
    }
    
    /**
     * تنظیمات کسب‌وکار
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010 \n\u0002\b\t\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\f\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000bR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000b\u00a8\u0006\u0012"}, d2 = {"Lcom/example/ma/config/AppConfig$Business;", "", "()V", "CURRENCY_CODE", "", "CURRENCY_SYMBOL", "DEFAULT_BOTTLE_PRICE", "", "EXPENSE_CATEGORIES", "", "getEXPENSE_CATEGORIES", "()Ljava/util/List;", "MAX_TRANSACTION_AMOUNT", "MIN_TRANSACTION_AMOUNT", "PAYMENT_TYPES", "getPAYMENT_TYPES", "WITHDRAWAL_TYPES", "getWITHDRAWAL_TYPES", "app_debug"})
    public static final class Business {
        public static final double DEFAULT_BOTTLE_PRICE = 50000.0;
        public static final double MIN_TRANSACTION_AMOUNT = 1000.0;
        public static final double MAX_TRANSACTION_AMOUNT = 1.0E7;
        @org.jetbrains.annotations.NotNull
        public static final java.lang.String CURRENCY_SYMBOL = "\u062a\u0648\u0645\u0627\u0646";
        @org.jetbrains.annotations.NotNull
        public static final java.lang.String CURRENCY_CODE = "IRR";
        @org.jetbrains.annotations.NotNull
        private static final java.util.List<java.lang.String> PAYMENT_TYPES = null;
        @org.jetbrains.annotations.NotNull
        private static final java.util.List<java.lang.String> EXPENSE_CATEGORIES = null;
        @org.jetbrains.annotations.NotNull
        private static final java.util.List<java.lang.String> WITHDRAWAL_TYPES = null;
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.config.AppConfig.Business INSTANCE = null;
        
        private Business() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getPAYMENT_TYPES() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getEXPENSE_CATEGORIES() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getWITHDRAWAL_TYPES() {
            return null;
        }
    }
    
    /**
     * تنظیمات Cache
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/ma/config/AppConfig$Cache;", "", "()V", "API_CACHE_DURATION_MINUTES", "", "API_CACHE_SIZE", "PROFILE_IMAGE_CACHE_DURATION_HOURS", "PROFILE_IMAGE_CACHE_SIZE", "app_debug"})
    public static final class Cache {
        public static final int PROFILE_IMAGE_CACHE_SIZE = 52428800;
        public static final int PROFILE_IMAGE_CACHE_DURATION_HOURS = 24;
        public static final int API_CACHE_SIZE = 10485760;
        public static final int API_CACHE_DURATION_MINUTES = 30;
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.config.AppConfig.Cache INSTANCE = null;
        
        private Cache() {
            super();
        }
    }
    
    /**
     * تنظیمات فایل
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/ma/config/AppConfig$File;", "", "()V", "IMAGE_MAX_HEIGHT", "", "IMAGE_MAX_WIDTH", "IMAGE_QUALITY", "MAX_IMAGE_SIZE_MB", "PROFILE_IMAGES_DIR", "", "TEMP_DIR", "app_debug"})
    public static final class File {
        public static final int MAX_IMAGE_SIZE_MB = 5;
        public static final int IMAGE_QUALITY = 80;
        public static final int IMAGE_MAX_WIDTH = 1024;
        public static final int IMAGE_MAX_HEIGHT = 1024;
        @org.jetbrains.annotations.NotNull
        public static final java.lang.String PROFILE_IMAGES_DIR = "profile_images";
        @org.jetbrains.annotations.NotNull
        public static final java.lang.String TEMP_DIR = "temp";
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.config.AppConfig.File INSTANCE = null;
        
        private File() {
            super();
        }
    }
    
    /**
     * تنظیمات شبکه
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/ma/config/AppConfig$Network;", "", "()V", "CALL_TIMEOUT", "", "CONNECT_TIMEOUT", "MAX_RETRIES", "", "READ_TIMEOUT", "RETRY_BACKOFF_FACTOR", "", "RETRY_DELAY_MS", "WRITE_TIMEOUT", "app_debug"})
    public static final class Network {
        public static final long CONNECT_TIMEOUT = 30L;
        public static final long READ_TIMEOUT = 30L;
        public static final long WRITE_TIMEOUT = 30L;
        public static final long CALL_TIMEOUT = 60L;
        public static final int MAX_RETRIES = 3;
        public static final long RETRY_DELAY_MS = 1000L;
        public static final double RETRY_BACKOFF_FACTOR = 2.0;
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.config.AppConfig.Network INSTANCE = null;
        
        private Network() {
            super();
        }
    }
    
    /**
     * تنظیمات Real-time
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\f"}, d2 = {"Lcom/example/ma/config/AppConfig$Realtime;", "", "()V", "HEARTBEAT_INTERVAL_MS", "", "MAX_RECONNECT_ATTEMPTS", "", "RECONNECT_DELAY_MS", "WEBSOCKET_URL", "", "getWEBSOCKET_URL", "()Ljava/lang/String;", "app_debug"})
    public static final class Realtime {
        public static final long HEARTBEAT_INTERVAL_MS = 30000L;
        public static final long RECONNECT_DELAY_MS = 5000L;
        public static final int MAX_RECONNECT_ATTEMPTS = 5;
        @org.jetbrains.annotations.NotNull
        private static final java.lang.String WEBSOCKET_URL = null;
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.config.AppConfig.Realtime INSTANCE = null;
        
        private Realtime() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getWEBSOCKET_URL() {
            return null;
        }
    }
    
    /**
     * تنظیمات امنیتی
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/example/ma/config/AppConfig$Security;", "", "()V", "LOGIN_LOCKOUT_MINUTES", "", "MAX_LOGIN_ATTEMPTS", "PASSWORD_MIN_LENGTH", "SESSION_TIMEOUT_MINUTES", "USERNAME_MIN_LENGTH", "app_debug"})
    public static final class Security {
        public static final int SESSION_TIMEOUT_MINUTES = 60;
        public static final int MAX_LOGIN_ATTEMPTS = 5;
        public static final int LOGIN_LOCKOUT_MINUTES = 15;
        public static final int PASSWORD_MIN_LENGTH = 6;
        public static final int USERNAME_MIN_LENGTH = 3;
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.config.AppConfig.Security INSTANCE = null;
        
        private Security() {
            super();
        }
    }
    
    /**
     * تنظیمات UI
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/ma/config/AppConfig$UI;", "", "()V", "ANIMATION_DURATION_MS", "", "DEBOUNCE_DELAY_MS", "LOAD_MORE_THRESHOLD", "", "PAGINATION_PAGE_SIZE", "THROTTLE_INTERVAL_MS", "app_debug"})
    public static final class UI {
        public static final long ANIMATION_DURATION_MS = 300L;
        public static final long DEBOUNCE_DELAY_MS = 300L;
        public static final long THROTTLE_INTERVAL_MS = 1000L;
        public static final int PAGINATION_PAGE_SIZE = 20;
        public static final int LOAD_MORE_THRESHOLD = 5;
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.config.AppConfig.UI INSTANCE = null;
        
        private UI() {
            super();
        }
    }
}