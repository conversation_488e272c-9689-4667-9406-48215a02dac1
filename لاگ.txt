2025-07-11 17:58:31.571  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396530541, downTime=396530541, phoneEventTime=۰۴:۲۸:۳۱.۵۶۱ } moveCount:0
2025-07-11 17:58:31.679  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396530650, downTime=396530541, phoneEventTime=۰۴:۲۸:۳۱.۶۷۰ } moveCount:0
2025-07-11 17:58:31.709  2992-2992  System.out              com.example.ma                       I  🔍 Debug - Original price: '800,000'
2025-07-11 17:58:31.710  2992-2992  System.out              com.example.ma                       I  🔍 Debug - Original price bytes: 56, 48, 48, 44, 48, 48, 48
2025-07-11 17:58:31.711  2992-2992  System.out              com.example.ma                       I  🔍 Debug - Clean price: '800000'
2025-07-11 17:58:31.711  2992-2992  System.out              com.example.ma                       I  🔍 Debug - Clean price bytes: 56, 48, 48, 48, 48, 48
2025-07-11 17:58:31.713  2992-2992  System.out              com.example.ma                       I  🔍 Debug - Final price: 800000.0
2025-07-11 17:58:31.729  2992-2992  System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-11 17:58:31.730  2992-2992  System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: currentUsername = null
2025-07-11 17:58:31.730  2992-2992  System.out              com.example.ma                       I  ❌ MainActivity.registerTransaction: کاربر شناسایی نشد
2025-07-11 17:58:35.978  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396534945, downTime=396534945, phoneEventTime=۰۴:۲۸:۳۵.۹۶۵ } moveCount:0
2025-07-11 17:58:35.986  2992-2992  Editor                  com.example.ma                       D  Double tap detection is fail
2025-07-11 17:58:38.216  2992-3149  System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-11 17:58:38.217  2992-3149  System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-11 17:58:38.217  2992-3149  System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Alikakai
2025-07-11 17:58:38.217  2992-3149  System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-11 17:58:38.217  2992-3149  System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-11 17:58:38.217  2992-3149  System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Miladnasiri
2025-07-11 17:58:38.217  2992-2992  System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-11 17:58:38.217  2992-2992  System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-11 17:58:38.319  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396537294, downTime=396534945, phoneEventTime=۰۴:۲۸:۳۸.۳۱۴ } moveCount:138
2025-07-11 17:58:38.735  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396537712, downTime=396537712, phoneEventTime=۰۴:۲۸:۳۸.۷۳۲ } moveCount:0
2025-07-11 17:58:40.108  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396539084, downTime=396537712, phoneEventTime=۰۴:۲۸:۴۰.۱۰۴ } moveCount:75
2025-07-11 17:58:40.110  2992-2992  SplineOverScroller      com.example.ma                       D  mFlingDuration: 1423,mFlingDistance: 472.55810546875,mVelocity: 1066
2025-07-11 17:58:40.704  2992-3147  RenderInspector         com.example.ma                       W  DequeueBuffer time out on com.example.ma/com.example.ma.MainActivity, count=1, avg=16 ms, max=16 ms.
2025-07-11 17:58:41.341  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396540314, downTime=396540314, phoneEventTime=۰۴:۲۸:۴۱.۳۳۴ } moveCount:0
2025-07-11 17:58:41.343  2992-2992  SplineOverScroller      com.example.ma                       E  unnecessary adjustClockInNanos, timeInNanos = 396540322835188
2025-07-11 17:58:41.857  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396540832, downTime=396540314, phoneEventTime=۰۴:۲۸:۴۱.۸۵۲ } moveCount:31
2025-07-11 17:58:44.224  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396543192, downTime=396543192, phoneEventTime=۰۴:۲۸:۴۴.۲۱۲ } moveCount:0
2025-07-11 17:58:44.363  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396543332, downTime=396543192, phoneEventTime=۰۴:۲۸:۴۴.۳۵۲ } moveCount:0
2025-07-11 17:58:44.445  2992-2992  ActivityThread          com.example.ma                       I  HardwareRenderer preload  done
2025-07-11 17:58:44.508  2992-2992  ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-11 17:58:44.637  2992-2992  ScrollerOp...ionManager com.example.ma                       D  registerConfigChangedListener
2025-07-11 17:58:44.652  2992-3149  System.out              com.example.ma                       I  🌐 SupabaseClient.get() - درخواست GET
2025-07-11 17:58:44.652  2992-3149  System.out              com.example.ma                       I  📝 Endpoint: notifications?limit=1
2025-07-11 17:58:44.657  2992-2992  SecurityManager         com.example.ma                       D  checkAccessControl flag0
2025-07-11 17:58:44.658  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:44.658  2992-2992  System.out              com.example.ma                       I  🔄 Starting real-time notification sync for user: ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:44.658  2992-2992  SupabaseRealtime        com.example.ma                       D  Connecting to Supabase Realtime...
2025-07-11 17:58:44.672  2992-2992  SupabaseRealtime        com.example.ma                       D  Subscribed to notifications for user: ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:44.689  2992-3149  System.out              com.example.ma                       I  ❌ Exception in get: Unable to resolve host "secoqjdcrszjseedprqk.supabase.conotifications": No address associated with hostname
2025-07-11 17:58:44.752  2992-2992  libc                    com.example.ma                       W  Access denied finding property "vendor.display.enable_optimal_refresh_rate"
2025-07-11 17:58:44.752  2992-2992  libc                    com.example.ma                       W  Access denied finding property "vendor.gpp.create_frc_extension"
2025-07-11 17:58:44.744  2992-2992  com.example.ma          com.example.ma                       W  type=1400 audit(0.0:359084): avc:  denied  { read } for  name="u:object_r:vendor_display_prop:s0" dev="tmpfs" ino=550 scontext=u:r:untrusted_app:s0:c222,c257,c512,c768 tcontext=u:object_r:vendor_display_prop:s0 tclass=file permissive=0 app=com.example.ma
2025-07-11 17:58:44.801  2992-2992  VRI[Notifi...nActivity] com.example.ma                       D  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4945 android.view.ViewRootImpl.doTraversal:3556 android.view.ViewRootImpl$TraversalRunnable.run:11567 android.view.Choreographer$CallbackRecord.run:1747 android.view.Choreographer$CallbackRecord.run:1756 
2025-07-11 17:58:44.802  2992-2992  VRI[Notifi...nActivity] com.example.ma                       D  vri.Setup new sync=wmsSync-VRI[NotificationActivity]#2
2025-07-11 17:58:44.859  2992-3147  HWUI                    com.example.ma                       D  makeCurrent grContext:0xb40000737663a250 reset mTextureAvailable
2025-07-11 17:58:44.866  2992-2992  VRI[Notifi...nActivity] com.example.ma                       D  vri.reportDrawFinished
2025-07-11 17:58:44.870  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:44.871  2992-3149  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-11 17:58:44.871  2992-3149  System.out              com.example.ma                       I  📝 User: ad28ba8f-0fa0-4420-8119-70fcacfd237e, Filter: all, Status: all
2025-07-11 17:58:44.872  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:44.873  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:44.873  2992-2992  System.out              com.example.ma                       I  🔄 Starting real-time notification sync for user: ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:44.873  2992-2992  SupabaseRealtime        com.example.ma                       D  Already connected or connecting
2025-07-11 17:58:44.873  2992-2992  SupabaseRealtime        com.example.ma                       D  Subscribed to notifications for user: ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:44.968  2992-2992  UserSceneDetector       com.example.ma                       D  invoke error.
2025-07-11 17:58:45.030  2992-2992  HandWritingStubImpl     com.example.ma                       I  refreshLastKeyboardType: 1
2025-07-11 17:58:45.032  2992-2992  HandWritingStubImpl     com.example.ma                       I  getCurrentKeyboardType: 1
2025-07-11 17:58:45.404  2992-2992  VRI[MainActivity]       com.example.ma                       D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-11 17:58:45.451  2992-2992  ActivityThread          com.example.ma                       D  Fail to check app heapsize due to java.lang.NoSuchMethodException: dalvik.system.VMRuntime.getBlockingGcCountForAlloc []
2025-07-11 17:58:46.015  2992-3669  SupabaseRealtime        com.example.ma                       D  WebSocket connected
2025-07-11 17:58:46.032  2992-3669  SupabaseRealtime        com.example.ma                       D  Sending message: {"topic":"realtime:public:notifications:to_user_id\u003deq.ad28ba8f-0fa0-4420-8119-70fcacfd237e","event":"phx_join","payload":{},"ref":"1"}
2025-07-11 17:58:46.033  2992-3669  SupabaseRealtime        com.example.ma                       D  Sending message: {"topic":"realtime:public:notifications:to_user_id\u003deq.ad28ba8f-0fa0-4420-8119-70fcacfd237e","event":"phx_join","payload":{},"ref":"2"}
2025-07-11 17:58:46.669  2992-3149  System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-11 17:58:46.669  2992-3149  System.out              com.example.ma                       I  📄 Response Body: []
2025-07-11 17:58:47.401  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396546372, downTime=396546372, phoneEventTime=۰۴:۲۸:۴۷.۳۹۲ } moveCount:0
2025-07-11 17:58:47.540  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396546488, downTime=396546372, phoneEventTime=۰۴:۲۸:۴۷.۵۰۹ } moveCount:0
2025-07-11 17:58:47.555  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:47.557  2992-3149  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-11 17:58:47.557  2992-3149  System.out              com.example.ma                       I  📝 User: ad28ba8f-0fa0-4420-8119-70fcacfd237e, Filter: all, Status: pending
2025-07-11 17:58:47.591  2992-3669  SupabaseRealtime        com.example.ma                       D  Received message: {"ref":"1","event":"phx_reply","payload":{"status":"ok","response":{"postgres_changes":[{"id":49230859,"filter":"to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e","schema":"public","table":"notifications"}]}},"topic":"realtime:public:notifications:to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e"}
2025-07-11 17:58:47.598  2992-3669  SupabaseRealtime        com.example.ma                       D  Subscription reply: {status=ok, response={postgres_changes=[{id=4.9230859E7, filter=to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e, schema=public, table=notifications}]}}
2025-07-11 17:58:47.598  2992-3669  SupabaseRealtime        com.example.ma                       D  Received message: {"ref":"2","event":"phx_reply","payload":{"status":"ok","response":{"postgres_changes":[{"id":49230859,"filter":"to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e","schema":"public","table":"notifications"}]}},"topic":"realtime:public:notifications:to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e"}
2025-07-11 17:58:47.605  2992-3669  SupabaseRealtime        com.example.ma                       D  Subscription reply: {status=ok, response={postgres_changes=[{id=4.9230859E7, filter=to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e, schema=public, table=notifications}]}}
2025-07-11 17:58:47.605  2992-3669  SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"phx_close","payload":{},"topic":"realtime:public:notifications:to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e"}
2025-07-11 17:58:47.771  2992-3669  SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"presence_state","payload":{},"topic":"realtime:public:notifications:to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e"}
2025-07-11 17:58:47.774  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396546748, downTime=396546748, phoneEventTime=۰۴:۲۸:۴۷.۷۶۸ } moveCount:0
2025-07-11 17:58:47.870  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396546844, downTime=396546748, phoneEventTime=۰۴:۲۸:۴۷.۸۶۴ } moveCount:0
2025-07-11 17:58:47.893  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:47.896  2992-3151  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-11 17:58:47.896  2992-3151  System.out              com.example.ma                       I  📝 User: ad28ba8f-0fa0-4420-8119-70fcacfd237e, Filter: all, Status: approved
2025-07-11 17:58:48.072  2992-3149  System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-11 17:58:48.072  2992-3149  System.out              com.example.ma                       I  📄 Response Body: []
2025-07-11 17:58:48.092  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396547066, downTime=396547066, phoneEventTime=۰۴:۲۸:۴۸.۰۸۶ } moveCount:0
2025-07-11 17:58:48.174  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396547149, downTime=396547066, phoneEventTime=۰۴:۲۸:۴۸.۱۶۹ } moveCount:0
2025-07-11 17:58:48.194  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:48.197  2992-3149  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-11 17:58:48.197  2992-3149  System.out              com.example.ma                       I  📝 User: ad28ba8f-0fa0-4420-8119-70fcacfd237e, Filter: all, Status: rejected
2025-07-11 17:58:48.222  2992-3150  System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-11 17:58:48.223  2992-3150  System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-11 17:58:48.223  2992-3150  System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Alikakai
2025-07-11 17:58:48.223  2992-3150  System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-11 17:58:48.223  2992-3150  System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-11 17:58:48.223  2992-3150  System.out              com.example.ma                       I  ❌ عکس پروفایل پیدا نشد برای Miladnasiri
2025-07-11 17:58:48.227  2992-2992  System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-11 17:58:48.227  2992-2992  System.out              com.example.ma                       I  🔍 MainActivity: Current username = null
2025-07-11 17:58:48.429  2992-3151  System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-11 17:58:48.430  2992-3151  System.out              com.example.ma                       I  📄 Response Body: []
2025-07-11 17:58:48.691  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396547664, downTime=396547664, phoneEventTime=۰۴:۲۸:۴۸.۶۸۴ } moveCount:0
2025-07-11 17:58:48.710  2992-3149  System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-11 17:58:48.710  2992-3149  System.out              com.example.ma                       I  📄 Response Body: []
2025-07-11 17:58:48.776  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396547751, downTime=396547664, phoneEventTime=۰۴:۲۸:۴۸.۷۷۱ } moveCount:0
2025-07-11 17:58:48.794  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:48.795  2992-3149  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-11 17:58:48.796  2992-3149  System.out              com.example.ma                       I  📝 User: ad28ba8f-0fa0-4420-8119-70fcacfd237e, Filter: all, Status: rejected
2025-07-11 17:58:48.982  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396547956, downTime=396547956, phoneEventTime=۰۴:۲۸:۴۸.۹۷۶ } moveCount:0
2025-07-11 17:58:49.060  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396548036, downTime=396547956, phoneEventTime=۰۴:۲۸:۴۹.۰۵۷ } moveCount:0
2025-07-11 17:58:49.078  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:49.079  2992-3151  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-11 17:58:49.080  2992-3151  System.out              com.example.ma                       I  📝 User: ad28ba8f-0fa0-4420-8119-70fcacfd237e, Filter: sale, Status: rejected
2025-07-11 17:58:49.259  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396548233, downTime=396548233, phoneEventTime=۰۴:۲۸:۴۹.۲۵۳ } moveCount:0
2025-07-11 17:58:49.272  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396548244, downTime=396548233, phoneEventTime=۰۴:۲۸:۴۹.۲۶۵ } moveCount:0
2025-07-11 17:58:49.297  2992-2992  System.out              com.example.ma                       I  🔍 NotificationActivity: Current user_id = ad28ba8f-0fa0-4420-8119-70fcacfd237e
2025-07-11 17:58:49.299  2992-3839  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-11 17:58:49.300  2992-3839  System.out              com.example.ma                       I  📝 User: ad28ba8f-0fa0-4420-8119-70fcacfd237e, Filter: withdrawal, Status: rejected
2025-07-11 17:58:49.319  2992-3149  System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-11 17:58:49.319  2992-3149  System.out              com.example.ma                       I  📄 Response Body: []
2025-07-11 17:58:49.410  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396548374, downTime=396548374, phoneEventTime=۰۴:۲۸:۴۹.۳۹۴ } moveCount:0
2025-07-11 17:58:49.500  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396548474, downTime=396548374, phoneEventTime=۰۴:۲۸:۴۹.۴۹۴ } moveCount:0
2025-07-11 17:58:49.580  2992-3669  SupabaseRealtime        com.example.ma                       D  Received message: {"ref":null,"event":"system","payload":{"message":"{:error, \"Unable to subscribe to changes with given parameters. Please check Realtime is enabled for the given connect parameters: [filter: to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e, schema: public, table: notifications]\"}","status":"error","extension":"postgres_changes","channel":"public:notifications:to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e"},"topic":"realtime:public:notifications:to_user_id=eq.ad28ba8f-0fa0-4420-8119-70fcacfd237e"}
2025-07-11 17:58:49.950  2992-3151  System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-11 17:58:49.950  2992-3151  System.out              com.example.ma                       I  📄 Response Body: []
2025-07-11 17:58:50.127  2992-3839  System.out              com.example.ma                       I  🌐 Response Code: 200
2025-07-11 17:58:50.127  2992-3839  System.out              com.example.ma                       I  📄 Response Body: []
2025-07-11 17:58:50.877  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396549847, downTime=396549847, phoneEventTime=۰۴:۲۸:۵۰.۸۶۷ } moveCount:0
2025-07-11 17:58:51.320  2992-2992  MiInputConsumer         com.example.ma                       I  optimized resample latency: 2517448 ns
2025-07-11 17:58:51.348  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396550318, downTime=396549847, phoneEventTime=۰۴:۲۸:۵۱.۳۳۸ } moveCount:28
2025-07-11 17:58:51.989  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396550959, downTime=396550959, phoneEventTime=۰۴:۲۸:۵۱.۹۸۰ } moveCount:0
2025-07-11 17:58:52.088  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396551058, downTime=396550959, phoneEventTime=۰۴:۲۸:۵۲.۰۷۹ } moveCount:1
2025-07-11 17:58:52.281  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=396551251, downTime=396551251, phoneEventTime=۰۴:۲۸:۵۲.۲۷۱ } moveCount:0
2025-07-11 17:58:52.375  2992-2992  MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.ui.notifications.NotificationActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=396551346, downTime=396551251, phoneEventTime=۰۴:۲۸:۵۲.۳۶۶ } moveCount:0
