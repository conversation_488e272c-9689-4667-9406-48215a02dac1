2025-07-13 05:47:05.754  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155868445, oldVsyncId=155868372, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:06.238  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155868452, oldVsyncId=155868445, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:06.507  5812-5812  System.out              com.example.ma                       I  🔍 NotificationActivity: Current username from prefs = Alikakai
2025-07-13 05:47:06.511  5812-5892  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-13 05:47:06.511  5812-5892  System.out              com.example.ma                       I  📝 User: Ali<PERSON><PERSON>, Filter: null, Status: null
2025-07-13 05:47:06.534  5812-5892  System.out              com.example.ma                       I  ❌ Exception in getNotifications: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:06.534  5812-5892  System.err              com.example.ma                       W  java.net.UnknownHostException: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:06.535  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:156)
2025-07-13 05:47:06.535  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
2025-07-13 05:47:06.535  5812-5892  System.err              com.example.ma                       W  	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
2025-07-13 05:47:06.535  5812-5892  System.err              com.example.ma                       W  	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
2025-07-13 05:47:06.535  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
2025-07-13 05:47:06.535  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
2025-07-13 05:47:06.536  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
2025-07-13 05:47:06.536  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
2025-07-13 05:47:06.536  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
2025-07-13 05:47:06.536  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
2025-07-13 05:47:06.537  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
2025-07-13 05:47:06.537  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
2025-07-13 05:47:06.538  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.539  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
2025-07-13 05:47:06.539  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.540  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
2025-07-13 05:47:06.540  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.541  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
2025-07-13 05:47:06.541  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.541  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
2025-07-13 05:47:06.541  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
2025-07-13 05:47:06.542  5812-5892  System.err              com.example.ma                       W  	at com.example.ma.data.remote.SupabaseClient$getNotifications$2.invokeSuspend(SupabaseClient.kt:532)
2025-07-13 05:47:06.542  5812-5892  System.err              com.example.ma                       W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-07-13 05:47:06.542  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
2025-07-13 05:47:06.542  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
2025-07-13 05:47:06.543  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
2025-07-13 05:47:06.543  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
2025-07-13 05:47:06.543  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
2025-07-13 05:47:06.544  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
2025-07-13 05:47:06.545  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-07-13 05:47:06.549  5812-5892  System.err              com.example.ma                       W  Caused by: android.system.GaiException: android_getaddrinfo failed: EAI_NODATA (No address associated with hostname)
2025-07-13 05:47:06.549  5812-5892  System.err              com.example.ma                       W  	at libcore.io.Linux.android_getaddrinfo(Native Method)
2025-07-13 05:47:06.549  5812-5892  System.err              com.example.ma                       W  	at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
2025-07-13 05:47:06.550  5812-5892  System.err              com.example.ma                       W  	at libcore.io.BlockGuardOs.android_getaddrinfo(BlockGuardOs.java:222)
2025-07-13 05:47:06.550  5812-5892  System.err              com.example.ma                       W  	at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
2025-07-13 05:47:06.550  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:135)
2025-07-13 05:47:06.550  5812-5892  System.err              com.example.ma                       W  	... 29 more
2025-07-13 05:47:06.554  5812-5812  System.out              com.example.ma                       I  🔍 NotificationActivity: Current username from prefs = Alikakai
2025-07-13 05:47:06.557  5812-5892  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-13 05:47:06.558  5812-5892  System.out              com.example.ma                       I  📝 User: Alikakai, Filter: null, Status: pending
2025-07-13 05:47:06.579  5812-5892  System.out              com.example.ma                       I  ❌ Exception in getNotifications: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:06.580  5812-5892  System.err              com.example.ma                       W  java.net.UnknownHostException: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:06.580  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:124)
2025-07-13 05:47:06.581  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
2025-07-13 05:47:06.581  5812-5892  System.err              com.example.ma                       W  	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
2025-07-13 05:47:06.581  5812-5892  System.err              com.example.ma                       W  	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
2025-07-13 05:47:06.582  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
2025-07-13 05:47:06.582  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
2025-07-13 05:47:06.582  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
2025-07-13 05:47:06.582  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
2025-07-13 05:47:06.582  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
2025-07-13 05:47:06.583  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
2025-07-13 05:47:06.583  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
2025-07-13 05:47:06.583  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
2025-07-13 05:47:06.583  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.584  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
2025-07-13 05:47:06.584  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.584  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
2025-07-13 05:47:06.584  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.584  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
2025-07-13 05:47:06.585  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.585  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
2025-07-13 05:47:06.585  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
2025-07-13 05:47:06.585  5812-5892  System.err              com.example.ma                       W  	at com.example.ma.data.remote.SupabaseClient$getNotifications$2.invokeSuspend(SupabaseClient.kt:532)
2025-07-13 05:47:06.585  5812-5892  System.err              com.example.ma                       W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-07-13 05:47:06.586  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
2025-07-13 05:47:06.586  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
2025-07-13 05:47:06.586  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
2025-07-13 05:47:06.586  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
2025-07-13 05:47:06.586  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
2025-07-13 05:47:06.587  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
2025-07-13 05:47:06.587  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-07-13 05:47:06.591  5812-5892  System.out              com.example.ma                       I  📤 SupabaseClient.getSentNotifications() - دریافت اعلانات ارسال شده
2025-07-13 05:47:06.591  5812-5892  System.out              com.example.ma                       I  📝 User: Alikakai, Filter: null, Status: pending
2025-07-13 05:47:06.612  5812-5892  System.out              com.example.ma                       I  ❌ خطا در getSentNotifications: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:06.613  5812-5892  System.err              com.example.ma                       W  java.net.UnknownHostException: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:06.613  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:124)
2025-07-13 05:47:06.614  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
2025-07-13 05:47:06.614  5812-5892  System.err              com.example.ma                       W  	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
2025-07-13 05:47:06.614  5812-5892  System.err              com.example.ma                       W  	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
2025-07-13 05:47:06.614  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
2025-07-13 05:47:06.614  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
2025-07-13 05:47:06.615  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
2025-07-13 05:47:06.615  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
2025-07-13 05:47:06.615  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
2025-07-13 05:47:06.615  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
2025-07-13 05:47:06.615  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
2025-07-13 05:47:06.616  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
2025-07-13 05:47:06.616  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.616  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
2025-07-13 05:47:06.616  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.616  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
2025-07-13 05:47:06.617  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.617  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
2025-07-13 05:47:06.617  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:06.617  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
2025-07-13 05:47:06.617  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
2025-07-13 05:47:06.618  5812-5892  System.err              com.example.ma                       W  	at com.example.ma.data.remote.SupabaseClient$getSentNotifications$2.invokeSuspend(SupabaseClient.kt:1535)
2025-07-13 05:47:06.618  5812-5892  System.err              com.example.ma                       W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-07-13 05:47:06.618  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
2025-07-13 05:47:06.618  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
2025-07-13 05:47:06.618  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
2025-07-13 05:47:06.619  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
2025-07-13 05:47:06.619  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
2025-07-13 05:47:06.619  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
2025-07-13 05:47:06.619  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-07-13 05:47:06.756  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155868507, oldVsyncId=155868452, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.241  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155868700, oldVsyncId=155868507, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.758  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155868997, oldVsyncId=155868700, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.776 14391-14391 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=512713480, downTime=512713480, phoneEventTime=۱۶:۱۷:۰۷.۷۶۷ } moveCount:0
2025-07-13 05:47:07.878 14391-14391 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=512713586, downTime=512713480, phoneEventTime=۱۶:۱۷:۰۷.۸۷۳ } moveCount:0
2025-07-13 05:47:07.902 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Original price: '500,000'
2025-07-13 05:47:07.903 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Original price bytes: 53, 48, 48, 44, 48, 48, 48
2025-07-13 05:47:07.903 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Clean price: '500000'
2025-07-13 05:47:07.904 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Clean price bytes: 53, 48, 48, 48, 48, 48
2025-07-13 05:47:07.904 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Final price: 500000.0
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Miladnasiri
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: currentUsername = Miladnasiri
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: شروع ثبت تراکنش
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: amount = 1000000.0
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: description = فروش 2 بطری - نقدی - میلاد نصیری - توضیحات: تست میلاد
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: userId = Miladnasiri
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: شروع
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: userId = Miladnasiri
2025-07-13 05:47:07.906 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: amount = 1000000.0
2025-07-13 05:47:07.907 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: ثبت فروش در جدول sales
2025-07-13 05:47:07.908 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: شروع
2025-07-13 05:47:07.909 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: saleData = {id=ee15413f-f00b-451a-a583-b9d01face284, user_id=Miladnasiri, amount=1000000.0, quantity=2, payment_type=نقدی, description=فروش 2 بطری - نقدی - میلاد نصیری - توضیحات: تست میلاد, status=pending}
2025-07-13 05:47:07.909 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: jsonData = {"id":"ee15413f-f00b-451a-a583-b9d01face284","user_id":"Miladnasiri","amount":1000000.0,"quantity":2,"payment_type":"نقدی","description":"فروش 2 بطری - نقدی - میلاد نصیری - توضیحات: تست میلاد","status":"pending"}
2025-07-13 05:47:07.926  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869076, oldVsyncId=155868997, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.926  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869076, oldVsyncId=155869076, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.942  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869149, oldVsyncId=155869076, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.942  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869149, oldVsyncId=155869149, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.959  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869168, oldVsyncId=155869149, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.959  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869168, oldVsyncId=155869168, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.975  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869187, oldVsyncId=155869168, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.975  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869187, oldVsyncId=155869187, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.992  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869206, oldVsyncId=155869187, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:07.992  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869206, oldVsyncId=155869206, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.009  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869225, oldVsyncId=155869206, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.009  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869225, oldVsyncId=155869225, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.026  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869244, oldVsyncId=155869225, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.026  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869244, oldVsyncId=155869244, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.042  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869263, oldVsyncId=155869244, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.042  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869263, oldVsyncId=155869263, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.059  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869282, oldVsyncId=155869263, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.059  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869282, oldVsyncId=155869282, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.076  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869301, oldVsyncId=155869282, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.076  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869301, oldVsyncId=155869301, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.092  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869320, oldVsyncId=155869301, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.092  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869320, oldVsyncId=155869320, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.109  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869339, oldVsyncId=155869320, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.109  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869339, oldVsyncId=155869339, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.126  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869352, oldVsyncId=155869339, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.126  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869352, oldVsyncId=155869352, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.142  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869365, oldVsyncId=155869352, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.142  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869365, oldVsyncId=155869365, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.159  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869378, oldVsyncId=155869365, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.160  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869378, oldVsyncId=155869378, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.176  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869391, oldVsyncId=155869378, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.176  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869391, oldVsyncId=155869391, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.193  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869404, oldVsyncId=155869391, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.193  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869404, oldVsyncId=155869404, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.209  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869417, oldVsyncId=155869404, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.209  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869417, oldVsyncId=155869417, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.226  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869430, oldVsyncId=155869417, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.226  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869430, oldVsyncId=155869430, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.243  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869443, oldVsyncId=155869430, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.259  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869456, oldVsyncId=155869443, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.276  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869475, oldVsyncId=155869456, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.293  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869488, oldVsyncId=155869475, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.310  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869501, oldVsyncId=155869488, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.326  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869514, oldVsyncId=155869501, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.343  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869527, oldVsyncId=155869514, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.360  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869540, oldVsyncId=155869527, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.376  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869553, oldVsyncId=155869540, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.393  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869572, oldVsyncId=155869553, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.410  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869585, oldVsyncId=155869572, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.427  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869598, oldVsyncId=155869585, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.443  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869611, oldVsyncId=155869598, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.460  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869624, oldVsyncId=155869611, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.476  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869637, oldVsyncId=155869624, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.494  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869650, oldVsyncId=155869637, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.510  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869663, oldVsyncId=155869650, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.527  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869676, oldVsyncId=155869663, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.543  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869689, oldVsyncId=155869676, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.560  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869702, oldVsyncId=155869689, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.577  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869715, oldVsyncId=155869702, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.593  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869728, oldVsyncId=155869715, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.610  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869741, oldVsyncId=155869728, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.627  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869754, oldVsyncId=155869741, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.644  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869767, oldVsyncId=155869754, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.660  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869780, oldVsyncId=155869767, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.677  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869793, oldVsyncId=155869780, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.694  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869806, oldVsyncId=155869793, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.710  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869819, oldVsyncId=155869806, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.727  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869832, oldVsyncId=155869819, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.761  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869845, oldVsyncId=155869832, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.772 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: response code = 400
2025-07-13 05:47:08.772 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: response body = {"code":"23514","details":"Failing row contains (ee15413f-f00b-451a-a583-b9d01face284, Miladnasiri, 1000000.00, 2, نقدی, فروش 2 بطری - نقدی - میلاد نصیری - توض..., pending, 2025-07-13 12:47:08.577974+00, 2025-07-13 12:47:08.577974+00).","hint":null,"message":"new row for relation \"sales\" violates check constraint \"sales_payment_type_check\""}
2025-07-13 05:47:08.772 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: result = false
2025-07-13 05:47:08.773 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: insertSale result = false
2025-07-13 05:47:08.773 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: success = false
2025-07-13 05:47:08.777  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869876, oldVsyncId=155869845, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:08.794  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869895, oldVsyncId=155869876, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:09.262  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155869920, oldVsyncId=155869895, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:09.763  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155870101, oldVsyncId=155869920, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:10.264  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155870288, oldVsyncId=155870101, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:10.766  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155870475, oldVsyncId=155870288, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:11.267  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155870662, oldVsyncId=155870475, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:11.768  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155870849, oldVsyncId=155870662, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:12.019  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871036, oldVsyncId=155870849, layerName=e35bc75 com.example.ma/com.example.ma.MainActivity#67539
2025-07-13 05:47:12.270  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871133, oldVsyncId=155871036, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:12.403  5812-5892  System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-13 05:47:12.403  5812-5892  System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-13 05:47:12.404  5812-5892  System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Alikakai
2025-07-13 05:47:12.407  5812-5892  System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-13 05:47:12.407  5812-5892  System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-13 05:47:12.408  5812-5892  System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Miladnasiri
2025-07-13 05:47:12.410  5812-5812  System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-13 05:47:12.410  5812-5812  System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Alikakai
2025-07-13 05:47:12.554  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871347, oldVsyncId=155871347, layerName=e35bc75 com.example.ma/com.example.ma.MainActivity#67539
2025-07-13 05:47:12.603  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871374, oldVsyncId=155871374, layerName=e35bc75 com.example.ma/com.example.ma.MainActivity#67539
2025-07-13 05:47:12.620  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871381, oldVsyncId=155871381, layerName=e35bc75 com.example.ma/com.example.ma.MainActivity#67539
2025-07-13 05:47:12.770  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871474, oldVsyncId=155871467, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:13.272  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871557, oldVsyncId=155871550, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:13.773  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871684, oldVsyncId=155871557, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.274  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871721, oldVsyncId=155871684, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.729 14391-14391 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=512720433, downTime=512720433, phoneEventTime=۱۶:۱۷:۱۴.۷۲۱ } moveCount:0
2025-07-13 05:47:14.775  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871728, oldVsyncId=155871721, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.787 14391-14391 MIUIInput               com.example.ma                       I  [MotionEvent] ViewRootImpl windowName 'com.example.ma/com.example.ma.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=512720493, downTime=512720433, phoneEventTime=۱۶:۱۷:۱۴.۷۸۰ } moveCount:2
2025-07-13 05:47:14.802 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Original price: '500,000'
2025-07-13 05:47:14.802 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Original price bytes: 53, 48, 48, 44, 48, 48, 48
2025-07-13 05:47:14.803 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Clean price: '500000'
2025-07-13 05:47:14.803 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Clean price bytes: 53, 48, 48, 48, 48, 48
2025-07-13 05:47:14.804 14391-14391 System.out              com.example.ma                       I  🔍 Debug - Final price: 500000.0
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Miladnasiri
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: currentUsername = Miladnasiri
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: شروع ثبت تراکنش
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: amount = 1000000.0
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: description = فروش 2 بطری - نقدی - میلاد نصیری - توضیحات: تست میلاد
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: userId = Miladnasiri
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: شروع
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: userId = Miladnasiri
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: amount = 1000000.0
2025-07-13 05:47:14.806 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: ثبت فروش در جدول sales
2025-07-13 05:47:14.808 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: شروع
2025-07-13 05:47:14.808 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: saleData = {id=01bdfe3f-5430-4a4c-8e9c-7835e8b1e4c3, user_id=Miladnasiri, amount=1000000.0, quantity=2, payment_type=نقدی, description=فروش 2 بطری - نقدی - میلاد نصیری - توضیحات: تست میلاد, status=pending}
2025-07-13 05:47:14.809 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: jsonData = {"id":"01bdfe3f-5430-4a4c-8e9c-7835e8b1e4c3","user_id":"Miladnasiri","amount":1000000.0,"quantity":2,"payment_type":"نقدی","description":"فروش 2 بطری - نقدی - میلاد نصیری - توضیحات: تست میلاد","status":"pending"}
2025-07-13 05:47:14.825  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871819, oldVsyncId=155871728, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.825  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871819, oldVsyncId=155871819, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.842  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871850, oldVsyncId=155871819, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.842  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871850, oldVsyncId=155871850, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.858  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871869, oldVsyncId=155871850, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.859  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871869, oldVsyncId=155871869, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.864 14391-14527 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-13 05:47:14.864 14391-14527 System.out              com.example.ma                       I  📝 UserId: 'Alikakai'
2025-07-13 05:47:14.865 14391-14527 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Alikakai
2025-07-13 05:47:14.867 14391-14527 System.out              com.example.ma                       I  🖼️ SupabaseClient.getUserProfileImage شروع شد
2025-07-13 05:47:14.867 14391-14527 System.out              com.example.ma                       I  📝 UserId: 'Miladnasiri'
2025-07-13 05:47:14.868 14391-14527 System.out              com.example.ma                       I  ✅ عکس پروفایل از cache پیدا شد برای Miladnasiri
2025-07-13 05:47:14.875  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871888, oldVsyncId=155871869, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.875  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871888, oldVsyncId=155871888, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.889 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Miladnasiri
2025-07-13 05:47:14.889 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity: Current username from prefs = Miladnasiri
2025-07-13 05:47:14.892  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871907, oldVsyncId=155871888, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.892  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871907, oldVsyncId=155871907, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.909  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871926, oldVsyncId=155871907, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.909  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871926, oldVsyncId=155871926, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.925  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871945, oldVsyncId=155871926, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.926  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871945, oldVsyncId=155871945, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.942  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871964, oldVsyncId=155871945, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.942  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871964, oldVsyncId=155871964, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.959  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871983, oldVsyncId=155871964, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.959  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155871983, oldVsyncId=155871983, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.975  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872002, oldVsyncId=155871983, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.975  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872002, oldVsyncId=155872002, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.992  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872021, oldVsyncId=155872002, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:14.992  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872021, oldVsyncId=155872021, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.009  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872040, oldVsyncId=155872021, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.009  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872040, oldVsyncId=155872040, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.026  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872053, oldVsyncId=155872040, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.026  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872053, oldVsyncId=155872053, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.042  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872066, oldVsyncId=155872053, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.043  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872066, oldVsyncId=155872066, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.059  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872079, oldVsyncId=155872066, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.059  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872079, oldVsyncId=155872079, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.076  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872092, oldVsyncId=155872079, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.076  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872092, oldVsyncId=155872092, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.092  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872105, oldVsyncId=155872092, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.092  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872105, oldVsyncId=155872105, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.109  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872118, oldVsyncId=155872105, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.109  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872118, oldVsyncId=155872118, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.126  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872131, oldVsyncId=155872118, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.126  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872131, oldVsyncId=155872131, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.143  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872144, oldVsyncId=155872131, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.143  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872144, oldVsyncId=155872144, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.159  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872157, oldVsyncId=155872144, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.159  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872157, oldVsyncId=155872157, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.176  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872170, oldVsyncId=155872157, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.176  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872170, oldVsyncId=155872170, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.193  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872183, oldVsyncId=155872170, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.193  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872183, oldVsyncId=155872183, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.209  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872196, oldVsyncId=155872183, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.209  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872196, oldVsyncId=155872196, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.226  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872209, oldVsyncId=155872196, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.226  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872209, oldVsyncId=155872209, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.238 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: response code = 400
2025-07-13 05:47:15.238 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: response body = {"code":"23514","details":"Failing row contains (01bdfe3f-5430-4a4c-8e9c-7835e8b1e4c3, Miladnasiri, 1000000.00, 2, نقدی, فروش 2 بطری - نقدی - میلاد نصیری - توض..., pending, 2025-07-13 12:47:15.029253+00, 2025-07-13 12:47:15.029253+00).","hint":null,"message":"new row for relation \"sales\" violates check constraint \"sales_payment_type_check\""}
2025-07-13 05:47:15.238 14391-14436 System.out              com.example.ma                       I  🔍 SupabaseClient.insertSale: result = false
2025-07-13 05:47:15.239 14391-14391 System.out              com.example.ma                       I  🔍 MainViewModel.registerSaleTransaction: insertSale result = false
2025-07-13 05:47:15.239 14391-14391 System.out              com.example.ma                       I  🔍 MainActivity.registerTransaction: success = false
2025-07-13 05:47:15.243  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872222, oldVsyncId=155872209, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.243  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872222, oldVsyncId=155872222, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.260  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872235, oldVsyncId=155872235, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.260  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872235, oldVsyncId=155872235, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.276  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872254, oldVsyncId=155872235, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.276  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872254, oldVsyncId=155872254, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.293  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872279, oldVsyncId=155872254, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.293  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872279, oldVsyncId=155872279, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.309  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872298, oldVsyncId=155872279, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.310  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872298, oldVsyncId=155872298, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.326  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872311, oldVsyncId=155872298, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.327  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872311, oldVsyncId=155872311, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.343  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872324, oldVsyncId=155872311, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.343  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872324, oldVsyncId=155872324, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.360  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872337, oldVsyncId=155872324, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.360  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872337, oldVsyncId=155872337, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.376  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872350, oldVsyncId=155872337, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.377  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872350, oldVsyncId=155872350, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.393  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872363, oldVsyncId=155872350, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.393  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872363, oldVsyncId=155872363, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.410  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872376, oldVsyncId=155872363, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.410  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872376, oldVsyncId=155872376, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.426  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872389, oldVsyncId=155872376, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.427  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872389, oldVsyncId=155872389, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.443  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872402, oldVsyncId=155872389, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.443  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872402, oldVsyncId=155872402, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.460  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872415, oldVsyncId=155872402, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.460  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872415, oldVsyncId=155872415, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.477  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872428, oldVsyncId=155872415, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.477  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872428, oldVsyncId=155872428, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.493  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872441, oldVsyncId=155872428, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.493  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872441, oldVsyncId=155872441, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.510  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872454, oldVsyncId=155872441, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.510  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872454, oldVsyncId=155872454, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.527  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872467, oldVsyncId=155872454, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.527  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872467, oldVsyncId=155872467, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.544  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872480, oldVsyncId=155872467, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.544  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872480, oldVsyncId=155872480, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.560  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872493, oldVsyncId=155872480, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.560  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872493, oldVsyncId=155872493, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.577  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872506, oldVsyncId=155872493, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.577  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872506, oldVsyncId=155872506, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.594  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872519, oldVsyncId=155872506, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.594  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872519, oldVsyncId=155872519, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.610  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872532, oldVsyncId=155872519, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.610  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872532, oldVsyncId=155872532, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.627  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872545, oldVsyncId=155872532, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.627  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872545, oldVsyncId=155872545, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.644  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872558, oldVsyncId=155872545, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.644  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872558, oldVsyncId=155872558, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.660  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872571, oldVsyncId=155872558, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.661  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872571, oldVsyncId=155872571, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.677  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872590, oldVsyncId=155872571, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.677  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872590, oldVsyncId=155872590, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.694  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872603, oldVsyncId=155872590, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:15.794  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872610, oldVsyncId=155872603, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:16.296  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872653, oldVsyncId=155872610, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:16.566  5812-5812  System.out              com.example.ma                       I  🔍 NotificationActivity: Current username from prefs = Alikakai
2025-07-13 05:47:16.568  5812-5892  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-13 05:47:16.568  5812-5892  System.out              com.example.ma                       I  📝 User: Alikakai, Filter: null, Status: null
2025-07-13 05:47:16.592  5812-5892  System.out              com.example.ma                       I  ❌ Exception in getNotifications: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:16.592  5812-5892  System.err              com.example.ma                       W  java.net.UnknownHostException: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:16.593  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:156)
2025-07-13 05:47:16.593  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
2025-07-13 05:47:16.593  5812-5892  System.err              com.example.ma                       W  	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
2025-07-13 05:47:16.593  5812-5892  System.err              com.example.ma                       W  	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
2025-07-13 05:47:16.593  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
2025-07-13 05:47:16.593  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
2025-07-13 05:47:16.594  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
2025-07-13 05:47:16.594  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
2025-07-13 05:47:16.594  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
2025-07-13 05:47:16.594  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
2025-07-13 05:47:16.594  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
2025-07-13 05:47:16.595  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
2025-07-13 05:47:16.595  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.595  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
2025-07-13 05:47:16.595  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.596  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
2025-07-13 05:47:16.596  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.596  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
2025-07-13 05:47:16.596  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.596  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
2025-07-13 05:47:16.596  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
2025-07-13 05:47:16.597  5812-5892  System.err              com.example.ma                       W  	at com.example.ma.data.remote.SupabaseClient$getNotifications$2.invokeSuspend(SupabaseClient.kt:532)
2025-07-13 05:47:16.597  5812-5892  System.err              com.example.ma                       W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-07-13 05:47:16.597  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
2025-07-13 05:47:16.597  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
2025-07-13 05:47:16.597  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
2025-07-13 05:47:16.598  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
2025-07-13 05:47:16.598  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
2025-07-13 05:47:16.598  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
2025-07-13 05:47:16.598  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-07-13 05:47:16.601  5812-5892  System.err              com.example.ma                       W  Caused by: android.system.GaiException: android_getaddrinfo failed: EAI_NODATA (No address associated with hostname)
2025-07-13 05:47:16.601  5812-5892  System.err              com.example.ma                       W  	at libcore.io.Linux.android_getaddrinfo(Native Method)
2025-07-13 05:47:16.601  5812-5892  System.err              com.example.ma                       W  	at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
2025-07-13 05:47:16.601  5812-5892  System.err              com.example.ma                       W  	at libcore.io.BlockGuardOs.android_getaddrinfo(BlockGuardOs.java:222)
2025-07-13 05:47:16.601  5812-5892  System.err              com.example.ma                       W  	at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
2025-07-13 05:47:16.602  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:135)
2025-07-13 05:47:16.602  5812-5892  System.err              com.example.ma                       W  	... 29 more
2025-07-13 05:47:16.606  5812-5812  System.out              com.example.ma                       I  🔍 NotificationActivity: Current username from prefs = Alikakai
2025-07-13 05:47:16.608  5812-5892  System.out              com.example.ma                       I  🔔 SupabaseClient.getNotifications() - دریافت اعلانات
2025-07-13 05:47:16.608  5812-5892  System.out              com.example.ma                       I  📝 User: Alikakai, Filter: null, Status: pending
2025-07-13 05:47:16.630  5812-5892  System.out              com.example.ma                       I  ❌ Exception in getNotifications: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:16.630  5812-5892  System.err              com.example.ma                       W  java.net.UnknownHostException: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:16.632  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:124)
2025-07-13 05:47:16.632  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
2025-07-13 05:47:16.632  5812-5892  System.err              com.example.ma                       W  	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
2025-07-13 05:47:16.632  5812-5892  System.err              com.example.ma                       W  	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
2025-07-13 05:47:16.633  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
2025-07-13 05:47:16.633  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
2025-07-13 05:47:16.633  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
2025-07-13 05:47:16.633  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
2025-07-13 05:47:16.634  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
2025-07-13 05:47:16.634  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
2025-07-13 05:47:16.634  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
2025-07-13 05:47:16.634  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
2025-07-13 05:47:16.634  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.635  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
2025-07-13 05:47:16.635  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.635  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
2025-07-13 05:47:16.636  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.636  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
2025-07-13 05:47:16.636  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.636  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
2025-07-13 05:47:16.636  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
2025-07-13 05:47:16.637  5812-5892  System.err              com.example.ma                       W  	at com.example.ma.data.remote.SupabaseClient$getNotifications$2.invokeSuspend(SupabaseClient.kt:532)
2025-07-13 05:47:16.637  5812-5892  System.err              com.example.ma                       W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-07-13 05:47:16.637  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
2025-07-13 05:47:16.637  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
2025-07-13 05:47:16.637  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
2025-07-13 05:47:16.638  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
2025-07-13 05:47:16.638  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
2025-07-13 05:47:16.638  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
2025-07-13 05:47:16.638  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-07-13 05:47:16.642  5812-5892  System.out              com.example.ma                       I  📤 SupabaseClient.getSentNotifications() - دریافت اعلانات ارسال شده
2025-07-13 05:47:16.642  5812-5892  System.out              com.example.ma                       I  📝 User: Alikakai, Filter: null, Status: pending
2025-07-13 05:47:16.665  5812-5892  System.out              com.example.ma                       I  ❌ خطا در getSentNotifications: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:16.665  5812-5892  System.err              com.example.ma                       W  java.net.UnknownHostException: Unable to resolve host "secoqjdcrszjseedprqk.supabase.co": No address associated with hostname
2025-07-13 05:47:16.665  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:124)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
2025-07-13 05:47:16.666  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
2025-07-13 05:47:16.667  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
2025-07-13 05:47:16.667  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
2025-07-13 05:47:16.667  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.667  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
2025-07-13 05:47:16.667  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.667  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
2025-07-13 05:47:16.668  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.668  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
2025-07-13 05:47:16.668  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-07-13 05:47:16.668  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
2025-07-13 05:47:16.668  5812-5892  System.err              com.example.ma                       W  	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
2025-07-13 05:47:16.668  5812-5892  System.err              com.example.ma                       W  	at com.example.ma.data.remote.SupabaseClient$getSentNotifications$2.invokeSuspend(SupabaseClient.kt:1535)
2025-07-13 05:47:16.669  5812-5892  System.err              com.example.ma                       W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-07-13 05:47:16.669  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
2025-07-13 05:47:16.669  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
2025-07-13 05:47:16.669  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
2025-07-13 05:47:16.669  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
2025-07-13 05:47:16.669  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
2025-07-13 05:47:16.669  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
2025-07-13 05:47:16.669  5812-5892  System.err              com.example.ma                       W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-07-13 05:47:16.797  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155872840, oldVsyncId=155872653, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:17.298  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155873027, oldVsyncId=155872840, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:17.799  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155873214, oldVsyncId=155873027, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:18.300  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155873407, oldVsyncId=155873214, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:18.801  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155873594, oldVsyncId=155873407, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:19.303  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155873823, oldVsyncId=155873594, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:19.803  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155874131, oldVsyncId=155873823, layerName=com.example.ma/com.example.ma.MainActivity#67558
2025-07-13 05:47:20.306  1788-1788  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=155874523, oldVsyncId=155874498, layerName=com.example.ma/com.example.ma.MainActivity#67558
