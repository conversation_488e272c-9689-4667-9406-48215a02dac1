<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    app:cardBackgroundColor="@color/surface_color"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Header اعلان -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <!-- آیکون نوع تراکنش -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="40dp"
                android:layout_height="40dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/income_color">

                <ImageView
                    android:id="@+id/ivNotificationType"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_trending_up"
                    app:tint="@color/text_white"
                    tools:src="@drawable/ic_trending_up" />

            </com.google.android.material.card.MaterialCardView>

            <!-- اطلاعات کاربر و نوع -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="12dp">

                <TextView
                    android:id="@+id/tvSenderName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="علی کاکایی"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:fontFamily="sans-serif-medium"
                    tools:text="علی کاکایی" />

                <TextView
                    android:id="@+id/tvTransactionType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="درخواست تایید فروش"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif"
                    tools:text="درخواست تایید فروش" />

            </LinearLayout>

            <!-- وضعیت -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/warning_color">

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="در انتظار"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_white"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp"
                    android:fontFamily="sans-serif-medium"
                    tools:text="در انتظار" />

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- جزئیات تراکنش -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/rounded_background_light"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- مبلغ -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💰 مبلغ:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/tvAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="2,500,000 تومان"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/income_color"
                    android:gravity="end"
                    android:fontFamily="sans-serif-medium"
                    tools:text="2,500,000 تومان" />

            </LinearLayout>

            <!-- تعداد محصول (اختیاری) -->
            <LinearLayout
                android:id="@+id/layoutProductCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📦 تعداد:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/tvProductCount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="25 بطری"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary"
                    android:gravity="end"
                    android:fontFamily="sans-serif"
                    tools:text="25 بطری" />

            </LinearLayout>

            <!-- توضیحات -->
            <LinearLayout
                android:id="@+id/layoutDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝 توضیحات:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/tvDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="فروش محصولات ماه جاری"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary"
                    android:gravity="end"
                    android:fontFamily="sans-serif"
                    tools:text="فروش محصولات ماه جاری" />

            </LinearLayout>

        </LinearLayout>

        <!-- زمان و دکمه‌های عمل -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- زمان -->
            <TextView
                android:id="@+id/tvTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="2 ساعت پیش"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                android:fontFamily="sans-serif"
                tools:text="2 ساعت پیش" />

            <!-- دکمه‌های تایید/رد -->
            <LinearLayout
                android:id="@+id/layoutActions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">

                <!-- دکمه رد -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnReject"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="رد"
                    android:textSize="12sp"
                    android:textColor="@color/error_color"
                    android:backgroundTint="@android:color/transparent"
                    app:strokeColor="@color/error_color"
                    app:strokeWidth="1dp"
                    app:cornerRadius="18dp"
                    android:layout_marginEnd="8dp"
                    android:fontFamily="sans-serif-medium"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                <!-- دکمه تایید -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnApprove"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="تایید"
                    android:textSize="12sp"
                    android:textColor="@color/text_white"
                    android:backgroundTint="@color/success_color"
                    app:cornerRadius="18dp"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
