com.example.ma.MainActivity%com.example.ma.MainActivity.Companion$com.example.ma.ui.main.MainViewModel%com.example.ma.ui.main.FinancialStats)com.example.ma.data.remote.SupabaseClient-com.example.ma.data.repository.AuthRepository7com.example.ma.data.repository.AuthRepository.Companion4com.example.ma.data.repository.TransactionRepository0com.example.ma.ui.financial.TransactionsActivity/com.example.ma.ui.financial.TransactionsAdapterEcom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder4com.example.ma.ui.notifications.NotificationActivity3com.example.ma.ui.notifications.NotificationAdapterPcom.example.ma.ui.notifications.NotificationAdapter.OnNotificationActionListenerJcom.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder'com.example.ma.utils.CalculationManager6com.example.ma.databinding.ActivityNotificationBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   