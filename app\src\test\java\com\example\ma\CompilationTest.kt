package com.example.ma

import com.example.ma.data.model.ApiResult
import com.example.ma.data.model.ApiError
import com.example.ma.utils.CurrencyFormatter
import com.example.ma.utils.ThemeManager
import org.junit.Test
import org.junit.Assert.*

/**
 * تست کامپایل برای اطمینان از عدم وجود خطا
 */
class CompilationTest {

    @Test
    fun apiResult_shouldCompileCorrectly() {
        // Test Success
        val success = ApiResult.Success("test")
        assertTrue(success.isSuccess)
        assertFalse(success.isError)
        assertFalse(success.isLoading)
        assertEquals("test", success.getDataOrNull())

        // Test Error
        val error = ApiResult.Error<String>(ApiError.NetworkError)
        assertFalse(error.isSuccess)
        assertTrue(error.isError)
        assertFalse(error.isLoading)
        assertNull(error.getDataOrNull())

        // Test Loading
        val loading = ApiResult.Loading<String>()
        assertFalse(loading.isSuccess)
        assertFalse(loading.isError)
        assertTrue(loading.isLoading)
        assertNull(loading.getDataOrNull())
    }

    @Test
    fun currencyFormatter_shouldCompileCorrectly() {
        // Test basic formatting
        val formatted = CurrencyFormatter.formatToToman(1000.0)
        assertEquals("1,000 تومان", formatted)

        // Test number to words
        val words = CurrencyFormatter.numberToWords(123)
        assertEquals("یکصد و بیست و سه", words)

        // Test format with words
        val withWords = CurrencyFormatter.formatWithWords(1000L)
        assertEquals("1,000 (یک هزار) تومان", withWords)
    }

    @Test
    fun themeManager_shouldCompileCorrectly() {
        // Test theme constants
        assertEquals(0, ThemeManager.THEME_LIGHT)
        assertEquals(1, ThemeManager.THEME_DARK)
        assertEquals(2, ThemeManager.THEME_SYSTEM)
    }

    @Test
    fun apiError_shouldCompileCorrectly() {
        // Test different error types
        val networkError = ApiError.NetworkError
        assertEquals("خطا در اتصال به اینترنت", networkError.message)

        val authError = ApiError.AuthenticationError
        assertEquals("نام کاربری یا رمز عبور اشتباه است", authError.message)

        val validationError = ApiError.ValidationError("test", "invalid")
        assertEquals("خطا در فیلد test: invalid", validationError.message)

        val serverError = ApiError.ServerError(500, "Internal Server Error")
        assertEquals("خطای سرور (کد: 500): Internal Server Error", serverError.message)
    }
}
