package com.example.ma.utils;

/**
 * TextWatcher برای فرمت کردن ورودی با جداکننده ۳ رقمی
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\r\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0012\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\nH\u0016J*\u0010\u000b\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000eH\u0016J*\u0010\u0011\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/example/ma/utils/CurrencyInputTextWatcher;", "Landroid/text/TextWatcher;", "editText", "Landroid/widget/EditText;", "(Landroid/widget/EditText;)V", "isFormatting", "", "afterTextChanged", "", "s", "Landroid/text/Editable;", "beforeTextChanged", "", "start", "", "count", "after", "onTextChanged", "before", "app_debug"})
public final class CurrencyInputTextWatcher implements android.text.TextWatcher {
    @org.jetbrains.annotations.NotNull
    private final android.widget.EditText editText = null;
    private boolean isFormatting = false;
    
    public CurrencyInputTextWatcher(@org.jetbrains.annotations.NotNull
    android.widget.EditText editText) {
        super();
    }
    
    @java.lang.Override
    public void beforeTextChanged(@org.jetbrains.annotations.Nullable
    java.lang.CharSequence s, int start, int count, int after) {
    }
    
    @java.lang.Override
    public void onTextChanged(@org.jetbrains.annotations.Nullable
    java.lang.CharSequence s, int start, int before, int count) {
    }
    
    @java.lang.Override
    public void afterTextChanged(@org.jetbrains.annotations.Nullable
    android.text.Editable s) {
    }
}