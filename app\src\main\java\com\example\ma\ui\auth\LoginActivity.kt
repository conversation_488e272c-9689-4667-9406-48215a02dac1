package com.example.ma.ui.auth

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.example.ma.MainActivity
import com.example.ma.R
import android.widget.*
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout

/**
 * صفحه ورود به سیستم
 * این صفحه احراز هویت کاربران را انجام می‌دهد
 */
class LoginActivity : AppCompatActivity() {

    private lateinit var viewModel: LoginViewModel

    // UI Elements
    private lateinit var usernameEditText: TextInputEditText
    private lateinit var passwordEditText: TextInputEditText
    private lateinit var loginButton: MaterialButton
    // private lateinit var showPasswordCheckBox: CheckBox // حذف شد چون در layout نیست
    private lateinit var errorTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)

        // راه‌اندازی ViewModel
        viewModel = ViewModelProvider(this)[LoginViewModel::class.java]

        setupUI()
        observeViewModel()

        // بررسی اینکه آیا کاربر قبلاً وارد شده یا نه
        val isLoggedIn = viewModel.isUserLoggedIn()
        println("🔍 LoginActivity.onCreate() - isUserLoggedIn: $isLoggedIn")

        if (isLoggedIn) {
            println("🏠 کاربر قبلاً وارد شده، انتقال به MainActivity")
            navigateToMain()
        } else {
            println("🔐 کاربر وارد نشده، نمایش صفحه ورود")
        }
    }
    
    private fun setupUI() {
        // پیدا کردن view ها
        usernameEditText = findViewById(R.id.etUsername)
        passwordEditText = findViewById(R.id.etPassword)
        loginButton = findViewById(R.id.btnLogin)
        // showPasswordCheckBox = findViewById(R.id.cbShowPassword) // حذف شد
        errorTextView = findViewById(R.id.tvError)

        // تنظیم دکمه ورود
        loginButton.setOnClickListener {
            val username = usernameEditText.text.toString().trim()
            val password = passwordEditText.text.toString().trim()

            if (validateInput(username, password)) {
                hideError()
                showLoading(true)
                viewModel.login(username, password)
            }
        }

        // تنظیم نمایش/مخفی کردن رمز - غیرفعال شده
        /*
        showPasswordCheckBox.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                passwordEditText.inputType = android.text.InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            } else {
                passwordEditText.inputType = android.text.InputType.TYPE_CLASS_TEXT or android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD
            }
            passwordEditText.setSelection(passwordEditText.text?.length ?: 0)
        }
        */

        // پاک کردن خطاها هنگام تایپ
        usernameEditText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) hideError()
        }

        passwordEditText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) hideError()
        }
    }
    
    private fun observeViewModel() {
        viewModel.loginResult.observe(this) { result ->
            when (result) {
                is LoginResult.Success -> {
                    showLoading(false)
                    hideError()
                    showSuccess("خوش آمدید ${result.user.displayName}")
                    navigateToMain()
                }
                is LoginResult.Error -> {
                    showLoading(false)
                    showError(result.message)
                    enableLoginButton()
                }
                is LoginResult.Loading -> {
                    showLoading(true)
                    disableLoginButton()
                }
            }
        }
    }
    
    private fun validateInput(username: String, password: String): Boolean {
        return when {
            username.isEmpty() -> {
                showError("نام کاربری را وارد کنید")
                usernameEditText.requestFocus()
                false
            }
            password.isEmpty() -> {
                showError("رمز عبور را وارد کنید")
                passwordEditText.requestFocus()
                false
            }
            username.length < 3 -> {
                showError("نام کاربری باید حداقل 3 کاراکتر باشد")
                usernameEditText.requestFocus()
                false
            }
            password.length < 3 -> {
                showError("رمز عبور باید حداقل 3 کاراکتر باشد")
                passwordEditText.requestFocus()
                false
            }
            else -> true
        }
    }

    private fun showError(message: String) {
        errorTextView.text = message
        errorTextView.visibility = View.VISIBLE
    }

    private fun hideError() {
        errorTextView.visibility = View.GONE
    }

    private fun showSuccess(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun disableLoginButton() {
        loginButton.isEnabled = false
        loginButton.text = "در حال ورود..."
    }

    private fun enableLoginButton() {
        loginButton.isEnabled = true
        loginButton.text = getString(R.string.login_button)
    }

    private fun showLoading(isLoading: Boolean) {
        if (isLoading) {
            loginButton.isEnabled = false
            loginButton.text = "در حال اتصال به سرور..."
        } else {
            loginButton.isEnabled = true
            loginButton.text = getString(R.string.login_button)
        }
    }

    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
}
