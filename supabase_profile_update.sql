-- بروزرسانی جدول notifications برای پشتیبانی از عکس پروفایل
ALTER TABLE notifications 
ADD COLUMN IF NOT EXISTS notification_type VARCHAR(50) DEFAULT 'transaction',
ADD COLUMN IF NOT EXISTS from_user_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS from_user_profile_image TEXT,
ADD COLUMN IF NOT EXISTS timestamp BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000;

-- بروزرسانی جدول users برای تطبیق نام فیلد
-- (اگر فیلد profile_image_url وجود نداشته باشد)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'profile_image_url'
    ) THEN
        ALTER TABLE users ADD COLUMN profile_image_url TEXT;
    END IF;
END $$;

-- حذف فیلد قدیمی profile_image اگر وجود داشته باشد
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'profile_image'
    ) THEN
        -- کپی داده‌ها از فیلد قدیمی به جدید
        UPDATE users SET profile_image_url = profile_image WHERE profile_image IS NOT NULL;
        -- حذف فیلد قدیمی
        ALTER TABLE users DROP COLUMN profile_image;
    END IF;
END $$;

-- ایجاد ایندکس برای بهبود عملکرد
CREATE INDEX IF NOT EXISTS idx_notifications_timestamp ON notifications(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(notification_type);
CREATE INDEX IF NOT EXISTS idx_users_profile_image ON users(profile_image_url);

-- تابع بروزرسانی شده برای ایجاد اعلان خودکار
CREATE OR REPLACE FUNCTION create_transaction_notification()
RETURNS TRIGGER AS $$
DECLARE
    other_user_id UUID;
    other_user_info RECORD;
    current_user_info RECORD;
    notification_message TEXT;
BEGIN
    -- دریافت اطلاعات کاربر فعلی
    SELECT id, username, full_name, profile_image_url 
    INTO current_user_info
    FROM users 
    WHERE id = NEW.user_id;
    
    -- پیدا کردن کاربر مقابل
    SELECT id, username, full_name, profile_image_url 
    INTO other_user_info
    FROM users 
    WHERE id != NEW.user_id 
    LIMIT 1;
    
    -- ساخت پیام اعلان
    notification_message := current_user_info.full_name || ' تراکنش جدید ثبت کرد: ' || 
        CASE NEW.type
            WHEN 'SALE' THEN 'فروش بطری'
            WHEN 'PURCHASE' THEN 'خرید مواد اولیه'
            WHEN 'EXPENSE' THEN 'پرداختی'
            WHEN 'INCOME' THEN 'دریافتی'
        END || 
        ' به مبلغ ' || NEW.amount || ' تومان';
    
    -- درج اعلان با اطلاعات کامل
    INSERT INTO notifications (
        transaction_id, 
        from_user_id, 
        to_user_id, 
        message,
        notification_type,
        from_user_name,
        from_user_profile_image,
        timestamp
    ) VALUES (
        NEW.id,
        NEW.user_id,
        other_user_info.id,
        notification_message,
        'transaction',
        current_user_info.full_name,
        current_user_info.profile_image_url,
        EXTRACT(EPOCH FROM NOW()) * 1000
    );
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تابع جدید برای ایجاد اعلان تغییر عکس پروفایل
CREATE OR REPLACE FUNCTION create_profile_image_notification()
RETURNS TRIGGER AS $$
DECLARE
    other_user_info RECORD;
    notification_message TEXT;
BEGIN
    -- بررسی اینکه آیا عکس پروفایل تغییر کرده
    IF OLD.profile_image_url IS DISTINCT FROM NEW.profile_image_url THEN
        -- پیدا کردن کاربر مقابل
        SELECT id, username, full_name 
        INTO other_user_info
        FROM users 
        WHERE id != NEW.id 
        LIMIT 1;
        
        -- ساخت پیام اعلان
        notification_message := NEW.full_name || ' عکس پروفایل خود را تغییر داد';
        
        -- درج اعلان
        INSERT INTO notifications (
            from_user_id, 
            to_user_id, 
            message,
            notification_type,
            from_user_name,
            from_user_profile_image,
            timestamp
        ) VALUES (
            NEW.id,
            other_user_info.id,
            notification_message,
            'profile_image_changed',
            NEW.full_name,
            NEW.profile_image_url,
            EXTRACT(EPOCH FROM NOW()) * 1000
        );
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ایجاد تریگر برای تغییر عکس پروفایل
DROP TRIGGER IF EXISTS profile_image_change_trigger ON users;
CREATE TRIGGER profile_image_change_trigger
    AFTER UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION create_profile_image_notification();

-- فعال‌سازی Realtime برای جداول
ALTER PUBLICATION supabase_realtime ADD TABLE users;
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

COMMIT;
