package com.example.ma.utils;

/**
 * وضعیت کامل مالی
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u001e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u0000 52\u00020\u0001:\u00015Be\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\u0003\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\u0006\u0010\u000b\u001a\u00020\u0003\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\u0002\u0010\u0011J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\rH\u00c6\u0003J\t\u0010#\u001a\u00020\rH\u00c6\u0003J\t\u0010$\u001a\u00020\u0010H\u00c6\u0003J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0003H\u00c6\u0003J\t\u0010(\u001a\u00020\u0003H\u00c6\u0003J\t\u0010)\u001a\u00020\u0003H\u00c6\u0003J\t\u0010*\u001a\u00020\u0003H\u00c6\u0003J\t\u0010+\u001a\u00020\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u0003H\u00c6\u0003J\u0081\u0001\u0010-\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\r2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u00c6\u0001J\u0013\u0010.\u001a\u00020/2\b\u00100\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00101\u001a\u000202H\u00d6\u0001J\t\u00103\u001a\u000204H\u00d6\u0001R\u0011\u0010\u000e\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0013R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0015R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0015R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0015R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0015\u00a8\u00066"}, d2 = {"Lcom/example/ma/utils/FinancialStatus;", "", "totalSales", "", "totalExpenses", "netProfit", "miladShare", "aliShare", "miladWithdrawals", "aliWithdrawals", "miladFinalBalance", "aliFinalBalance", "miladAccounts", "Lcom/example/ma/utils/AccountBalances;", "aliAccounts", "lastCalculated", "", "(DDDDDDDDDLcom/example/ma/utils/AccountBalances;Lcom/example/ma/utils/AccountBalances;J)V", "getAliAccounts", "()Lcom/example/ma/utils/AccountBalances;", "getAliFinalBalance", "()D", "getAliShare", "getAliWithdrawals", "getLastCalculated", "()J", "getMiladAccounts", "getMiladFinalBalance", "getMiladShare", "getMiladWithdrawals", "getNetProfit", "getTotalExpenses", "getTotalSales", "component1", "component10", "component11", "component12", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "", "Companion", "app_debug"})
public final class FinancialStatus {
    private final double totalSales = 0.0;
    private final double totalExpenses = 0.0;
    private final double netProfit = 0.0;
    private final double miladShare = 0.0;
    private final double aliShare = 0.0;
    private final double miladWithdrawals = 0.0;
    private final double aliWithdrawals = 0.0;
    private final double miladFinalBalance = 0.0;
    private final double aliFinalBalance = 0.0;
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.utils.AccountBalances miladAccounts = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.utils.AccountBalances aliAccounts = null;
    private final long lastCalculated = 0L;
    @org.jetbrains.annotations.NotNull
    public static final com.example.ma.utils.FinancialStatus.Companion Companion = null;
    
    public FinancialStatus(double totalSales, double totalExpenses, double netProfit, double miladShare, double aliShare, double miladWithdrawals, double aliWithdrawals, double miladFinalBalance, double aliFinalBalance, @org.jetbrains.annotations.NotNull
    com.example.ma.utils.AccountBalances miladAccounts, @org.jetbrains.annotations.NotNull
    com.example.ma.utils.AccountBalances aliAccounts, long lastCalculated) {
        super();
    }
    
    public final double getTotalSales() {
        return 0.0;
    }
    
    public final double getTotalExpenses() {
        return 0.0;
    }
    
    public final double getNetProfit() {
        return 0.0;
    }
    
    public final double getMiladShare() {
        return 0.0;
    }
    
    public final double getAliShare() {
        return 0.0;
    }
    
    public final double getMiladWithdrawals() {
        return 0.0;
    }
    
    public final double getAliWithdrawals() {
        return 0.0;
    }
    
    public final double getMiladFinalBalance() {
        return 0.0;
    }
    
    public final double getAliFinalBalance() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.utils.AccountBalances getMiladAccounts() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.utils.AccountBalances getAliAccounts() {
        return null;
    }
    
    public final long getLastCalculated() {
        return 0L;
    }
    
    public final double component1() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.utils.AccountBalances component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.utils.AccountBalances component11() {
        return null;
    }
    
    public final long component12() {
        return 0L;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final double component9() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.utils.FinancialStatus copy(double totalSales, double totalExpenses, double netProfit, double miladShare, double aliShare, double miladWithdrawals, double aliWithdrawals, double miladFinalBalance, double aliFinalBalance, @org.jetbrains.annotations.NotNull
    com.example.ma.utils.AccountBalances miladAccounts, @org.jetbrains.annotations.NotNull
    com.example.ma.utils.AccountBalances aliAccounts, long lastCalculated) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/example/ma/utils/FinancialStatus$Companion;", "", "()V", "empty", "Lcom/example/ma/utils/FinancialStatus;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.utils.FinancialStatus empty() {
            return null;
        }
    }
}