R_DEF: Internal format may change without notice
local
color accent_color
color background_color
color background_secondary
color balance_color
color black
color card_background
color error_color
color expense_color
color gradient_end
color gradient_start
color income_color
color info_color
color on_surface
color on_surface_variant
color outline
color outline_variant
color overlay_dark
color overlay_light
color primary_color
color primary_dark_color
color primary_light
color primary_variant
color secondary_color
color secondary_variant
color success_color
color success_light
color surface_color
color surface_variant
color text_hint
color text_primary
color text_secondary
color text_white
color warning_color
color white
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen fab_margin
dimen nav_header_height
dimen nav_header_vertical_spacing
drawable bg_amount_display
drawable bg_status_approved
drawable button_primary
drawable card_background
drawable circle_background
drawable circle_background_white
drawable gradient_background
drawable gradient_primary
drawable ic_account_balance
drawable ic_add
drawable ic_arrow_back
drawable ic_balance
drawable ic_bar_chart
drawable ic_camera
drawable ic_category
drawable ic_chart
drawable ic_check
drawable ic_clear
drawable ic_close
drawable ic_dashboard
drawable ic_description
drawable ic_edit
drawable ic_email
drawable ic_expenses
drawable ic_filter
drawable ic_home
drawable ic_inventory
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_lock
drawable ic_lock_modern
drawable ic_logout
drawable ic_menu
drawable ic_money
drawable ic_notifications
drawable ic_palette
drawable ic_person
drawable ic_person_modern
drawable ic_phone
drawable ic_receipt
drawable ic_reports
drawable ic_search
drawable ic_settings
drawable ic_storage
drawable ic_transactions
drawable ic_trending_down
drawable ic_trending_up
drawable ic_visibility
drawable ic_visibility_off
drawable ic_withdrawal
drawable nav_item_background
drawable notification_badge_background
drawable rounded_background
drawable rounded_background_light
drawable rounded_background_primary
drawable rounded_background_secondary
drawable selector_radio_background
drawable spinner_background
id btnApprove
id btnBack
id btnClearFilter
id btnCopyScript
id btnCreateSampleData
id btnFilter
id btnLogin
id btnRegisterTransaction
id btnReject
id btnSelectImage
id btnSubmit
id btnTestConnection
id cardAddExpense
id cardAddWithdrawal
id cardBank
id cardBusinessExpense
id cardFinancial
id cardInventory
id cardManageInventory
id cardMenu
id cardNotifications
id cardPersonalWithdrawal
id cardProfileImage
id cardSharedExpense
id cardStats
id cardStatus
id cardTransaction
id cardTransactions
id cardViewTransactions
id chipReceived
id chipSent
id drawer_layout
id etAmount
id etBottleCount
id etDateFrom
id etDateTo
id etDescription
id etEmail
id etFullName
id etPassword
id etPhone
id etPrice
id etQuantity
id etSearch
id etUsername
id fixed_header
id ivCardProfileImage
id ivHeaderProfileImage
id ivNavProfileImage
id ivNotificationType
id ivProfileImage
id ivProfilePicture
id layoutAccount
id layoutActions
id layoutDescription
id layoutEmpty
id layoutProductCount
id layoutQuantity
id nav_dashboard
id nav_graph
id nav_home
id nav_logout
id nav_notifications
id nav_profile
id nav_reports
id nav_theme_settings
id nav_transactions
id nav_view
id rbCard
id rbCash
id rbDarkTheme
id rbDecrease
id rbIncrease
id rbLightTheme
id rbSystemTheme
id recyclerView
id rgChangeType
id rgThemeMode
id rgWithdrawalType
id rvNotifications
id spinnerCategory
id spinnerReceiver
id spinnerStatus
id spinnerType
id spinnerUser
id tilPassword
id toolbar
id tvAccount
id tvAccountLabel
id tvAliCardBalance
id tvAliCashBalance
id tvAliFinalBalance
id tvAliPersonalBalance
id tvAliShare
id tvAliWithdrawals
id tvAmount
id tvAmountInWords
id tvAvailableBalance
id tvCardBalance
id tvCardPhone
id tvCardUserName
id tvCashBalance
id tvCompanyBalance
id tvCurrentStock
id tvDate
id tvDescription
id tvDescriptionLabel
id tvError
id tvHeaderUserName
id tvHelpText
id tvLastCalculated
id tvMiladCardBalance
id tvMiladCashBalance
id tvMiladFinalBalance
id tvMiladPersonalBalance
id tvMiladShare
id tvMiladWithdrawals
id tvNavUserName
id tvNavUserRole
id tvNetProfit
id tvNewNotifications
id tvNotificationBadge
id tvPartnershipShare
id tvPendingApprovals
id tvPersonalCardBalance
id tvPersonalCashBalance
id tvPersonalExpenses
id tvPersonalProfit
id tvPersonalSales
id tvPersonalWithdrawals
id tvPriceInWords
id tvProductCount
id tvProfitShare
id tvQuantity
id tvQuantityLabel
id tvReceiverLabel
id tvSenderName
id tvSqlScript
id tvStatus
id tvTime
id tvTotalAmount
id tvTotalExpenses
id tvTotalSales
id tvTransactionType
id tvType
id tvUser
layout activity_database_setup
layout activity_expense
layout activity_financial
layout activity_inventory
layout activity_login
layout activity_main
layout activity_notification
layout activity_profile
layout activity_reports
layout activity_statistics
layout activity_transactions
layout activity_withdrawal
layout dialog_theme_selection
layout item_notification
layout item_notification_new
layout item_transaction
layout nav_header_main
menu activity_main_drawer
mipmap ic_launcher
mipmap ic_launcher_round
navigation nav_graph
string account_balance
string add
string ali_kakai_card
string amount
string app_name
string approve
string approved
string bottle_count_hint
string bottles_unit
string cancel
string card_destination
string card_payment
string cash_payment
string cash_receiver
string confirm
string connection_error
string currency_unit
string dashboard
string date
string delete
string description
string edit
string error
string expense
string filter
string financial_summary
string hide_password
string income
string invalid_credentials
string invalid_number
string inventory_stock
string loading
string login_button
string login_title
string logout
string main_menu
string mark_as_read
string menu_expenses
string menu_inventory
string menu_notifications
string menu_personal_withdrawal
string milad_nasiri_card
string my_share
string net_profit
string new_notification
string new_transaction
string no_data
string no_notifications
string notification_sent
string notifications
string partner_share
string password_hint
string pending
string please_enter_bottle_count
string please_enter_price
string please_select_payment_type
string please_select_receiver
string price_hint
string purchase
string register_transaction
string reject
string rejected
string reports
string sale
string save
string search
string show_password
string success
string this_month
string this_week
string today
string total_balance
string total_expenses
string total_income
string total_purchases
string total_sales
string transaction_approved
string transaction_approved_message
string transaction_list
string transaction_rejected
string transaction_rejected_message
string transaction_saved
string transaction_sent_for_approval
string transaction_type
string transactions
string unread_notifications
string username_hint
string welcome_message
string yesterday
style Base.Theme.MA
style NavigationMenuTextStyle
style NavigationSubheaderStyle
style Theme.MA
xml backup_rules
xml data_extraction_rules
