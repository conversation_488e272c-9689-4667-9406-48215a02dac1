package com.example.ma.ui.financial

import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.data.remote.SupabaseClient
import com.example.ma.utils.SimpleCurrencyTextWatcher
import com.example.ma.utils.CurrencyFormatter
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch

/**
 * صفحه ثبت هزینه (مشارکتی یا عملیاتی)
 */
class ExpenseActivity : AppCompatActivity() {

    private lateinit var etAmount: TextInputEditText
    private lateinit var etDescription: TextInputEditText
    private lateinit var spinnerCategory: AutoCompleteTextView
    private lateinit var btnSubmit: MaterialButton
    private lateinit var tvAmountInWords: android.widget.TextView

    private var expenseType: String = "business" // business یا shared_personal

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_expense)

        // دریافت نوع هزینه از Intent
        expenseType = intent.getStringExtra("expense_type") ?: "business"

        setupUI()
        setupCategorySpinner()
    }

    private fun setupUI() {
        etAmount = findViewById(R.id.etAmount)
        etDescription = findViewById(R.id.etDescription)
        spinnerCategory = findViewById(R.id.spinnerCategory)
        btnSubmit = findViewById(R.id.btnSubmit)
        tvAmountInWords = findViewById(R.id.tvAmountInWords)

        // تنظیم عنوان بر اساس نوع هزینه
        val toolbar = findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)
        val title = if (expenseType == "shared_personal") {
            "ثبت هزینه مشارکتی"
        } else {
            "ثبت هزینه عملیاتی"
        }
        toolbar.title = title

        // دکمه بازگشت
        toolbar.setNavigationOnClickListener {
            onBackPressed()
        }

        // دکمه ثبت
        btnSubmit.setOnClickListener {
            submitExpense()
        }

        // تنظیم متن راهنما
        val helpText = findViewById<android.widget.TextView>(R.id.tvHelpText)
        helpText.text = if (expenseType == "shared_personal") {
            "💡 هزینه‌های مشارکتی (غذا، تفریح) به صورت 50/50 بین دو شریک تقسیم می‌شود"
        } else {
            "💡 هزینه‌های عملیاتی از سود کل کسر شده و بر اساس پرداخت‌کننده تعدیل می‌شود"
        }

        // تنظیم TextWatcher ساده
        etAmount.addTextChangedListener(
            SimpleCurrencyTextWatcher(
                editText = etAmount,
                displayTextView = tvAmountInWords
            )
        )
    }

    private fun setupCategorySpinner() {
        val categories = if (expenseType == "shared_personal") {
            arrayOf("غذا و نوشیدنی", "تفریح", "حمل و نقل", "سایر")
        } else {
            arrayOf("مواد اولیه", "اجاره", "برق و آب", "حمل و نقل", "تعمیرات", "بازاریابی", "سایر")
        }

        val adapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, categories)
        spinnerCategory.setAdapter(adapter)
        
        // انتخاب پیش‌فرض
        spinnerCategory.setText(categories[0], false)
    }

    private fun submitExpense() {
        val amountText = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()
        val category = spinnerCategory.text.toString().trim()

        // اعتبارسنجی
        if (amountText.isEmpty()) {
            etAmount.error = "مبلغ را وارد کنید"
            return
        }

        val amount = try {
            // حذف کاما قبل از تبدیل به عدد
            val cleanAmountText = amountText.replace(",", "").replace("٬", "")
            cleanAmountText.toDouble()
        } catch (e: NumberFormatException) {
            etAmount.error = "مبلغ نامعتبر"
            return
        }

        if (amount <= 0) {
            etAmount.error = "مبلغ باید بیشتر از صفر باشد"
            return
        }

        if (description.isEmpty()) {
            etDescription.error = "توضیحات را وارد کنید"
            return
        }

        // ثبت هزینه
        lifecycleScope.launch {
            try {
                val currentUserId = getCurrentUserId()
                if (currentUserId != null) {
                    val success = SupabaseClient.createExpense(
                        userId = currentUserId,
                        amount = amount,
                        type = expenseType,
                        category = category,
                        description = description
                    )

                    if (success) {
                        Toast.makeText(this@ExpenseActivity, "هزینه با موفقیت ثبت شد", Toast.LENGTH_SHORT).show()
                        finish()
                    } else {
                        Toast.makeText(this@ExpenseActivity, "خطا در ثبت هزینه", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@ExpenseActivity, "خطا در احراز هویت", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@ExpenseActivity, "خطا در اتصال به سرور", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun getCurrentUserId(): String? {
        return try {
            val sharedPreferences = getSharedPreferences("auth_prefs", MODE_PRIVATE)
            // ابتدا تلاش برای دریافت username
            val username = sharedPreferences.getString("current_username", null)
            if (username != null) {
                println("🔍 ExpenseActivity: Current username = $username")
                return username
            }

            // اگر username نبود، از user_id استفاده کن
            val userId = sharedPreferences.getString("current_user_id", null)
            println("🔍 ExpenseActivity: Current user_id = $userId")
            return userId
        } catch (e: Exception) {
            println("❌ ExpenseActivity: خطا در دریافت user ID: ${e.message}")
            null
        }
    }
}
