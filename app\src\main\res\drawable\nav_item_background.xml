<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- حالت انتخاب شده -->
    <item android:state_checked="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_light" />
            <corners
                android:topLeftRadius="12dp"
                android:bottomLeftRadius="12dp"
                android:topRightRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>

    <!-- حالت فشرده شده -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/background_secondary" />
            <corners
                android:topLeftRadius="12dp"
                android:bottomLeftRadius="12dp"
                android:topRightRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>

    <!-- حالت عادی -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>

</selector>
