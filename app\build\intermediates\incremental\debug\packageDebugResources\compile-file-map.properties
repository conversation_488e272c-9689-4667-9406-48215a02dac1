#Sat Jul 12 18:16:19 PDT 2025
com.example.ma.app-main-6\:/menu/activity_main_drawer.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\menu\\activity_main_drawer.xml
com.example.ma.app-main-6\:/drawable/ic_person_modern.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person_modern.xml
com.example.ma.app-main-6\:/drawable/ic_email.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_email.xml
com.example.ma.app-main-6\:/xml/backup_rules.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\xml\\backup_rules.xml
com.example.ma.app-main-6\:/drawable/nav_item_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\nav_item_background.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_financial.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_financial.xml
com.example.ma.app-main-6\:/drawable/ic_notifications.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_notifications.xml
com.example.ma.app-main-6\:/drawable/ic_storage.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_storage.xml
com.example.ma.app-packageDebugResources-3\:/layout/item_transaction.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_transaction.xml
com.example.ma.app-main-6\:/drawable/rounded_background_secondary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_background_secondary.xml
com.example.ma.app-main-6\:/drawable/ic_check.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_check.xml
com.example.ma.app-main-6\:/drawable/ic_account_balance.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_account_balance.xml
com.example.ma.app-main-6\:/xml/data_extraction_rules.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\xml\\data_extraction_rules.xml
com.example.ma.app-packageDebugResources-3\:/layout/nav_header_main.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\nav_header_main.xml
com.example.ma.app-main-6\:/drawable/ic_category.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_category.xml
com.example.ma.app-main-6\:/drawable/circle_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle_background.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_profile.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_profile.xml
com.example.ma.app-main-6\:/drawable/ic_search.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_search.xml
com.example.ma.app-main-6\:/drawable/ic_phone.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_phone.xml
com.example.ma.app-main-6\:/drawable/ic_dashboard.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_dashboard.xml
com.example.ma.app-main-6\:/drawable/rounded_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_background.xml
com.example.ma.app-main-6\:/drawable/ic_add.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_add.xml
com.example.ma.app-main-6\:/drawable/ic_balance.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_balance.xml
com.example.ma.app-main-6\:/drawable/ic_launcher_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_background.xml
com.example.ma.app-main-6\:/drawable/ic_chart.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_chart.xml
com.example.ma.app-main-6\:/drawable/rounded_background_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_background_primary.xml
com.example.ma.app-main-6\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.ma.app-main-6\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.ma.app-main-6\:/drawable/ic_filter.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_filter.xml
com.example.ma.app-packageDebugResources-3\:/layout/item_notification.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_notification.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_notification.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_notification.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_login.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_login.xml
com.example.ma.app-main-6\:/drawable/ic_palette.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_palette.xml
com.example.ma.app-main-6\:/drawable/ic_reports.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_reports.xml
com.example.ma.app-main-6\:/drawable/gradient_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\gradient_background.xml
com.example.ma.app-main-6\:/drawable/bg_status_approved.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_status_approved.xml
com.example.ma.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.ma.app-main-6\:/drawable/ic_close.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_close.xml
com.example.ma.app-main-6\:/drawable/gradient_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\gradient_primary.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_main.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.example.ma.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.ma.app-main-6\:/drawable/ic_bar_chart.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_bar_chart.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_transactions.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_transactions.xml
com.example.ma.app-main-6\:/drawable/spinner_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\spinner_background.xml
com.example.ma.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.ma.app-main-6\:/drawable/ic_visibility_off.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_visibility_off.xml
com.example.ma.app-main-6\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_foreground.xml
com.example.ma.app-main-6\:/drawable/ic_lock.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_lock.xml
com.example.ma.app-main-6\:/drawable/bg_amount_display.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_amount_display.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_statistics.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_statistics.xml
com.example.ma.app-main-6\:/drawable/card_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\card_background.xml
com.example.ma.app-packageDebugResources-3\:/layout/dialog_theme_selection.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\dialog_theme_selection.xml
com.example.ma.app-main-6\:/drawable/ic_description.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_description.xml
com.example.ma.app-main-6\:/drawable/ic_money.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_money.xml
com.example.ma.app-main-6\:/drawable/ic_logout.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_logout.xml
com.example.ma.app-main-6\:/drawable/circle_background_white.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle_background_white.xml
com.example.ma.app-main-6\:/drawable/ic_trending_up.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_trending_up.xml
com.example.ma.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.ma.app-main-6\:/drawable/ic_receipt.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_receipt.xml
com.example.ma.app-main-6\:/drawable/ic_inventory.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_inventory.xml
com.example.ma.app-main-6\:/drawable/ic_menu.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_menu.xml
com.example.ma.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.ma.app-main-6\:/drawable/ic_home.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_home.xml
com.example.ma.app-main-6\:/drawable/selector_radio_background.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\selector_radio_background.xml
com.example.ma.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.ma.app-main-6\:/drawable/ic_transactions.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_transactions.xml
com.example.ma.app-main-6\:/navigation/nav_graph.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\navigation\\nav_graph.xml
com.example.ma.app-main-6\:/drawable/ic_arrow_back.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_arrow_back.xml
com.example.ma.app-main-6\:/drawable/ic_clear.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_clear.xml
com.example.ma.app-main-6\:/drawable/ic_settings.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_settings.xml
com.example.ma.app-main-6\:/drawable/ic_person.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person.xml
com.example.ma.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.ma.app-main-6\:/drawable/rounded_background_light.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_background_light.xml
com.example.ma.app-main-6\:/drawable/ic_withdrawal.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_withdrawal.xml
com.example.ma.app-main-6\:/drawable/ic_lock_modern.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_lock_modern.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_withdrawal.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_withdrawal.xml
com.example.ma.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.ma.app-packageDebugResources-3\:/layout/activity_expense.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_expense.xml
com.example.ma.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.ma.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.ma.app-main-6\:/drawable/ic_trending_down.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_trending_down.xml
com.example.ma.app-main-6\:/drawable/button_primary.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_primary.xml
com.example.ma.app-main-6\:/drawable/ic_camera.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_camera.xml
com.example.ma.app-main-6\:/drawable/ic_edit.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_edit.xml
com.example.ma.app-main-6\:/drawable/ic_visibility.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_visibility.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_inventory.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_inventory.xml
com.example.ma.app-packageDebugResources-3\:/layout/activity_database_setup.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_database_setup.xml
com.example.ma.app-main-6\:/drawable/ic_expenses.xml=C\:\\Users\\alikhan\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_expenses.xml
