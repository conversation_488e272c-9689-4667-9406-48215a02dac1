<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_notification_new" modulePackage="com.example.ma" filePath="app\src\main\res\layout\item_notification_new.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_notification_new_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="295" endOffset="51"/></Target><Target id="@+id/ivProfilePicture" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="25" startOffset="12" endLine="31" endOffset="61"/></Target><Target id="@+id/tvSenderName" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="44" endOffset="56"/></Target><Target id="@+id/cardStatus" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="47" startOffset="12" endLine="66" endOffset="63"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="55" startOffset="16" endLine="64" endOffset="60"/></Target><Target id="@+id/tvTransactionType" view="TextView"><Expressions/><location startLine="95" startOffset="16" endLine="105" endOffset="60"/></Target><Target id="@+id/tvAmount" view="TextView"><Expressions/><location startLine="124" startOffset="16" endLine="134" endOffset="60"/></Target><Target id="@+id/layoutQuantity" view="LinearLayout"><Expressions/><location startLine="139" startOffset="12" endLine="168" endOffset="26"/></Target><Target id="@+id/tvQuantityLabel" view="TextView"><Expressions/><location startLine="147" startOffset="16" endLine="154" endOffset="53"/></Target><Target id="@+id/tvQuantity" view="TextView"><Expressions/><location startLine="156" startOffset="16" endLine="166" endOffset="60"/></Target><Target id="@+id/layoutAccount" view="LinearLayout"><Expressions/><location startLine="171" startOffset="12" endLine="200" endOffset="26"/></Target><Target id="@+id/tvAccountLabel" view="TextView"><Expressions/><location startLine="179" startOffset="16" endLine="186" endOffset="53"/></Target><Target id="@+id/tvAccount" view="TextView"><Expressions/><location startLine="188" startOffset="16" endLine="198" endOffset="60"/></Target><Target id="@+id/layoutDescription" view="LinearLayout"><Expressions/><location startLine="203" startOffset="12" endLine="230" endOffset="26"/></Target><Target id="@+id/tvDescription" view="TextView"><Expressions/><location startLine="219" startOffset="16" endLine="228" endOffset="53"/></Target><Target id="@+id/tvDate" view="TextView"><Expressions/><location startLine="246" startOffset="16" endLine="255" endOffset="53"/></Target><Target id="@+id/layoutActions" view="LinearLayout"><Expressions/><location startLine="262" startOffset="8" endLine="291" endOffset="22"/></Target><Target id="@+id/btnReject" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="270" startOffset="12" endLine="279" endOffset="65"/></Target><Target id="@+id/btnApprove" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="281" startOffset="12" endLine="289" endOffset="65"/></Target></Targets></Layout>