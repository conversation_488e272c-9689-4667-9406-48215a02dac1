<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_notification_new" modulePackage="com.example.ma" filePath="app\src\main\res\layout\item_notification_new.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_notification_new_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="296" endOffset="51"/></Target><Target id="@+id/ivProfilePicture" view="ImageView"><Expressions/><location startLine="25" startOffset="12" endLine="32" endOffset="48"/></Target><Target id="@+id/tvSenderName" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="45" endOffset="56"/></Target><Target id="@+id/cardStatus" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="48" startOffset="12" endLine="67" endOffset="63"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="56" startOffset="16" endLine="65" endOffset="60"/></Target><Target id="@+id/tvTransactionType" view="TextView"><Expressions/><location startLine="96" startOffset="16" endLine="106" endOffset="60"/></Target><Target id="@+id/tvAmount" view="TextView"><Expressions/><location startLine="125" startOffset="16" endLine="135" endOffset="60"/></Target><Target id="@+id/layoutQuantity" view="LinearLayout"><Expressions/><location startLine="140" startOffset="12" endLine="169" endOffset="26"/></Target><Target id="@+id/tvQuantityLabel" view="TextView"><Expressions/><location startLine="148" startOffset="16" endLine="155" endOffset="53"/></Target><Target id="@+id/tvQuantity" view="TextView"><Expressions/><location startLine="157" startOffset="16" endLine="167" endOffset="60"/></Target><Target id="@+id/layoutAccount" view="LinearLayout"><Expressions/><location startLine="172" startOffset="12" endLine="201" endOffset="26"/></Target><Target id="@+id/tvAccountLabel" view="TextView"><Expressions/><location startLine="180" startOffset="16" endLine="187" endOffset="53"/></Target><Target id="@+id/tvAccount" view="TextView"><Expressions/><location startLine="189" startOffset="16" endLine="199" endOffset="60"/></Target><Target id="@+id/layoutDescription" view="LinearLayout"><Expressions/><location startLine="204" startOffset="12" endLine="231" endOffset="26"/></Target><Target id="@+id/tvDescription" view="TextView"><Expressions/><location startLine="220" startOffset="16" endLine="229" endOffset="53"/></Target><Target id="@+id/tvDate" view="TextView"><Expressions/><location startLine="247" startOffset="16" endLine="256" endOffset="53"/></Target><Target id="@+id/layoutActions" view="LinearLayout"><Expressions/><location startLine="263" startOffset="8" endLine="292" endOffset="22"/></Target><Target id="@+id/btnReject" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="271" startOffset="12" endLine="280" endOffset="65"/></Target><Target id="@+id/btnApprove" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="282" startOffset="12" endLine="290" endOffset="65"/></Target></Targets></Layout>