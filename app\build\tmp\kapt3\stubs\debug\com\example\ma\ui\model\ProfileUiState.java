package com.example.ma.ui.model;

/**
 * UI State برای صفحه پروفایل
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Ba\u0012\u001a\b\u0002\u0010\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00040\u0003\u0012\u001a\b\u0002\u0010\u0006\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00070\u00040\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\u0003\u00a2\u0006\u0002\u0010\fJ\u001b\u0010\u0012\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00040\u0003H\u00c6\u0003J\u001b\u0010\u0013\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00070\u00040\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\tH\u00c6\u0003J\t\u0010\u0015\u001a\u00020\tH\u00c6\u0003J\u000f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\t0\u0003H\u00c6\u0003Je\u0010\u0017\u001a\u00020\u00002\u001a\b\u0002\u0010\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00040\u00032\u001a\b\u0002\u0010\u0006\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00070\u00040\u00032\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\u0003H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\t2\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0005H\u00d6\u0001R#\u0010\u0006\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00070\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u000fR\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000fR\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR#\u0010\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000e\u00a8\u0006\u001d"}, d2 = {"Lcom/example/ma/ui/model/ProfileUiState;", "", "userInfo", "Lcom/example/ma/ui/model/UiState;", "", "", "balances", "", "isUpdatingProfile", "", "isUploadingImage", "lastUpdateResult", "(Lcom/example/ma/ui/model/UiState;Lcom/example/ma/ui/model/UiState;ZZLcom/example/ma/ui/model/UiState;)V", "getBalances", "()Lcom/example/ma/ui/model/UiState;", "()Z", "getLastUpdateResult", "getUserInfo", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ProfileUiState {
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Object>> userInfo = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> balances = null;
    private final boolean isUpdatingProfile = false;
    private final boolean isUploadingImage = false;
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.ui.model.UiState<java.lang.Boolean> lastUpdateResult = null;
    
    public ProfileUiState(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> userInfo, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, java.lang.Double>> balances, boolean isUpdatingProfile, boolean isUploadingImage, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<java.lang.Boolean> lastUpdateResult) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Object>> getUserInfo() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> getBalances() {
        return null;
    }
    
    public final boolean isUpdatingProfile() {
        return false;
    }
    
    public final boolean isUploadingImage() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.lang.Boolean> getLastUpdateResult() {
        return null;
    }
    
    public ProfileUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Object>> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.lang.Boolean> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.ProfileUiState copy(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> userInfo, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, java.lang.Double>> balances, boolean isUpdatingProfile, boolean isUploadingImage, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<java.lang.Boolean> lastUpdateResult) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}