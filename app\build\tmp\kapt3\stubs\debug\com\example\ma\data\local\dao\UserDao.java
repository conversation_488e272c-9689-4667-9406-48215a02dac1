package com.example.ma.data.local.dao;

/**
 * DAO برای عملیات کاربران
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u0011\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0019\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u0019\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\fJ\u0011\u0010\r\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00100\u000fH\'J\u0014\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00100\u000fH\'J\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u001b\u0010\u0014\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\fJ\u001b\u0010\u0015\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0016\u001a\u00020\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\fJ\u0011\u0010\u0017\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00070\u0010H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0019\u0010\u0019\u001a\u00020\u00132\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ%\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00130\u00102\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0010H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001cJ\u001c\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00100\u000f2\u0006\u0010\u001e\u001a\u00020\u000bH\'J+\u0010\u001f\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010 \u001a\u00020!2\b\b\u0002\u0010\"\u001a\u00020\u0013H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#JK\u0010$\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\b\u0010%\u001a\u0004\u0018\u00010\u000b2\b\u0010&\u001a\u0004\u0018\u00010\u000b2\b\u0010\'\u001a\u0004\u0018\u00010\u000b2\b\u0010(\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\"\u001a\u00020\u0013H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010)J+\u0010*\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010+\u001a\u00020\u000b2\b\b\u0002\u0010\"\u001a\u00020\u0013H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010,J+\u0010-\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010.\u001a\u00020/2\b\b\u0002\u0010\"\u001a\u00020\u0013H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00100J\u0019\u00101\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\b\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u00062"}, d2 = {"Lcom/example/ma/data/local/dao/UserDao;", "", "clearAllUsers", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteUser", "user", "Lcom/example/ma/data/local/entity/UserEntity;", "(Lcom/example/ma/data/local/entity/UserEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteUserById", "userId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveUserCount", "getActiveUsers", "Lkotlinx/coroutines/flow/Flow;", "", "getAllUsers", "getLastSyncTime", "", "getUserById", "getUserByUsername", "username", "getUserCount", "getUsersNeedingSync", "insertUser", "insertUsers", "users", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchUsers", "query", "setUserActiveStatus", "isActive", "", "timestamp", "(Ljava/lang/String;ZJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePersonalInfo", "displayName", "fullName", "email", "phone", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProfileImageUrl", "imageUrl", "(Ljava/lang/String;Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSyncStatus", "status", "Lcom/example/ma/data/local/entity/SyncStatus;", "(Ljava/lang/String;Lcom/example/ma/data/local/entity/SyncStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUser", "app_debug"})
@androidx.room.Dao
public abstract interface UserDao {
    
    /**
     * دریافت همه کاربران
     */
    @androidx.room.Query(value = "SELECT * FROM users ORDER BY username")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.UserEntity>> getAllUsers();
    
    /**
     * دریافت کاربر بر اساس ID
     */
    @androidx.room.Query(value = "SELECT * FROM users WHERE id = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserById(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.ma.data.local.entity.UserEntity> $completion);
    
    /**
     * دریافت کاربر بر اساس username
     */
    @androidx.room.Query(value = "SELECT * FROM users WHERE username = :username")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserByUsername(@org.jetbrains.annotations.NotNull
    java.lang.String username, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.ma.data.local.entity.UserEntity> $completion);
    
    /**
     * دریافت کاربران فعال
     */
    @androidx.room.Query(value = "SELECT * FROM users WHERE is_active = 1 ORDER BY username")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.UserEntity>> getActiveUsers();
    
    /**
     * دریافت کاربران نیازمند همگام‌سازی
     */
    @androidx.room.Query(value = "SELECT * FROM users WHERE sync_status != \'SYNCED\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUsersNeedingSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.example.ma.data.local.entity.UserEntity>> $completion);
    
    /**
     * درج کاربر جدید
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertUser(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.UserEntity user, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * درج چندین کاربر
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertUsers(@org.jetbrains.annotations.NotNull
    java.util.List<com.example.ma.data.local.entity.UserEntity> users, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion);
    
    /**
     * بروزرسانی کاربر
     */
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateUser(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.UserEntity user, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * حذف کاربر
     */
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteUser(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.UserEntity user, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * حذف کاربر بر اساس ID
     */
    @androidx.room.Query(value = "DELETE FROM users WHERE id = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteUserById(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * بروزرسانی وضعیت همگام‌سازی
     */
    @androidx.room.Query(value = "UPDATE users SET sync_status = :status, last_sync = :timestamp WHERE id = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateSyncStatus(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.SyncStatus status, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * بروزرسانی URL عکس پروفایل
     */
    @androidx.room.Query(value = "UPDATE users SET profile_image_url = :imageUrl, updated_at = :timestamp WHERE id = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateProfileImageUrl(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    java.lang.String imageUrl, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * بروزرسانی اطلاعات شخصی
     */
    @androidx.room.Query(value = "UPDATE users SET display_name = :displayName, full_name = :fullName, email = :email, phone = :phone, updated_at = :timestamp WHERE id = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updatePersonalInfo(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.Nullable
    java.lang.String displayName, @org.jetbrains.annotations.Nullable
    java.lang.String fullName, @org.jetbrains.annotations.Nullable
    java.lang.String email, @org.jetbrains.annotations.Nullable
    java.lang.String phone, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * تنظیم وضعیت فعال/غیرفعال
     */
    @androidx.room.Query(value = "UPDATE users SET is_active = :isActive, updated_at = :timestamp WHERE id = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object setUserActiveStatus(@org.jetbrains.annotations.NotNull
    java.lang.String userId, boolean isActive, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * پاک کردن همه کاربران
     */
    @androidx.room.Query(value = "DELETE FROM users")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearAllUsers(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * شمارش کاربران
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM users")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * شمارش کاربران فعال
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM users WHERE is_active = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getActiveUserCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * دریافت آخرین زمان همگام‌سازی
     */
    @androidx.room.Query(value = "SELECT MAX(last_sync) FROM users")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getLastSyncTime(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * جستجو در کاربران
     */
    @androidx.room.Query(value = "SELECT * FROM users WHERE username LIKE :query OR display_name LIKE :query OR full_name LIKE :query ORDER BY username")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.data.local.entity.UserEntity>> searchUsers(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    /**
     * DAO برای عملیات کاربران
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}