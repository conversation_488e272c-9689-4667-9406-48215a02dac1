package com.example.ma.ui.financial

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import com.example.ma.utils.CurrencyFormatter
import com.google.android.material.card.MaterialCardView
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter برای نمایش لیست تراکنش‌ها
 */
class TransactionsAdapter(
    private val onItemClick: (Map<String, Any>) -> Unit
) : RecyclerView.Adapter<TransactionsAdapter.TransactionViewHolder>() {

    private var transactions = listOf<Map<String, Any>>()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())

    fun updateTransactions(newTransactions: List<Map<String, Any>>) {
        transactions = newTransactions
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_transaction, parent, false)
        return TransactionViewHolder(view)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        holder.bind(transactions[position])
    }

    override fun getItemCount(): Int = transactions.size

    inner class TransactionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: MaterialCardView = itemView.findViewById(R.id.cardTransaction)
        private val tvType: TextView = itemView.findViewById(R.id.tvType)
        private val tvAmount: TextView = itemView.findViewById(R.id.tvAmount)
        private val tvDescription: TextView = itemView.findViewById(R.id.tvDescription)
        private val tvUser: TextView = itemView.findViewById(R.id.tvUser)
        private val tvDate: TextView = itemView.findViewById(R.id.tvDate)
        private val tvStatus: TextView = itemView.findViewById(R.id.tvStatus)

        fun bind(transaction: Map<String, Any>) {
            val type = transaction["type"] as? String ?: ""
            val amount = (transaction["amount"] as? Number)?.toDouble() ?: 0.0
            val description = transaction["description"] as? String ?: ""
            val userId = transaction["user_id"] as? String ?: ""
            val status = transaction["status"] as? String ?: ""
            val createdAt = transaction["created_at"] as? String ?: ""

            // نوع تراکنش
            tvType.text = getTypeDisplayName(type)
            
            // مبلغ
            tvAmount.text = formatCurrency(amount)
            
            // توضیحات
            tvDescription.text = description
            
            // کاربر
            tvUser.text = getUserDisplayName(userId)
            
            // تاریخ
            tvDate.text = formatDate(createdAt)
            
            // وضعیت
            tvStatus.text = getStatusDisplayName(status)
            setStatusColor(tvStatus, status)

            // کلیک
            cardView.setOnClickListener {
                onItemClick(transaction)
            }
        }

        private fun getTypeDisplayName(type: String): String {
            return when (type) {
                "sale" -> "📈 فروش"
                "business" -> "💼 هزینه عملیاتی"
                "shared_personal" -> "🤝 هزینه مشارکتی"
                "withdrawal" -> "👤 برداشت"
                else -> "❓ نامشخص"
            }
        }

        private fun getUserDisplayName(userId: String): String {
            return when (userId) {
                "miladnasiri" -> "میلاد نصیری"
                "alikakai" -> "علی کاکایی"
                else -> "نامشخص"
            }
        }

        private fun getStatusDisplayName(status: String): String {
            return when (status) {
                "pending" -> "در انتظار"
                "approved" -> "تایید شده"
                "rejected" -> "رد شده"
                else -> "نامشخص"
            }
        }

        private fun setStatusColor(textView: TextView, status: String) {
            val context = textView.context
            val colorRes = when (status) {
                "pending" -> R.color.warning_color
                "approved" -> R.color.success_color
                "rejected" -> R.color.error_color
                else -> R.color.text_secondary
            }
            textView.setTextColor(context.getColor(colorRes))
        }

        private fun formatCurrency(amount: Double): String {
            return CurrencyFormatter.format(amount)
        }

        private fun formatDate(dateString: String): String {
            return try {
                val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
                val date = inputFormat.parse(dateString)
                dateFormat.format(date ?: Date())
            } catch (e: Exception) {
                dateString.take(16) // فقط تاریخ و ساعت
            }
        }
    }
}
