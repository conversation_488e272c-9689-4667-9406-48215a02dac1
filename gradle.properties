# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8 --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-<PERSON>NAMED
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
# Network settings
org.gradle.daemon=true
org.gradle.configureondemand=true

# تنظیمات Proxy برای دور زدن تحریم‌ها
systemProp.http.proxyHost=***********
systemProp.http.proxyPort=18080
systemProp.https.proxyHost=***********
systemProp.https.proxyPort=18080
systemProp.http.nonProxyHosts=localhost|127.*|[::1]

# بهبود سرعت build
org.gradle.parallel=true
org.gradle.caching=true

# رفع warning compileSdk
android.suppressUnsupportedCompileSdk=34

# Proxy های backup (اگر بالایی کار نکرد، # رو از یکی از اینها بردارید)
# Backup 1:
# systemProp.http.proxyHost=************
# systemProp.http.proxyPort=80
# systemProp.https.proxyHost=************
# systemProp.https.proxyPort=80

# Backup 2:
# systemProp.http.proxyHost=************
# systemProp.http.proxyPort=3128
# systemProp.https.proxyHost=************
# systemProp.https.proxyPort=3128

# Backup 3:
# systemProp.http.proxyHost=*************
# systemProp.http.proxyPort=3129
# systemProp.https.proxyHost=*************
# systemProp.https.proxyPort=3129

# Supabase Configuration
# این مقادیر در production باید از environment variables خوانده شوند
SUPABASE_URL=https://secoqjdcrszjseedprqk.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNlY29xamRjcnN6anNlZWRwcnFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2NTQwMDksImV4cCI6MjA2NzIzMDAwOX0.UTKnCn7TDPLdy9ohdoh5fp2h6he3lnpEu-0MZv1P6fQ

# کلیدهای اضافی Supabase (برای مرجع)
# Service Role Key (فقط برای backend): eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNlY29xamRjcnN6anNlZWRwcnFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTY1NDAwOSwiZXhwIjoyMDY3MjMwMDA5fQ.UhflGKQ8xq3BP5BqhF7y6JOvfQ9u4qKVz9xAMjHk9Vc
# JWT Secret: ezZ8Fa7Q3pQ5FxXbRsV2ZsM6UC2pOWwFeALwZX/mVmMTI3qhoX5fTpkUB82/W8D88KRscLm+ywA8ge+K8nfoeQ==

# App Configuration
APP_VERSION_NAME=1.0.0
APP_VERSION_CODE=1