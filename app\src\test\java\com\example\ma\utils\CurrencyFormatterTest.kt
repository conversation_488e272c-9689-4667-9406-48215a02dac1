package com.example.ma.utils

import org.junit.Test
import org.junit.Assert.*

/**
 * تست‌های واحد برای CurrencyFormatter
 */
class CurrencyFormatterTest {

    @Test
    fun formatToToman_shouldFormatCorrectly() {
        // Given
        val amount = 1234567.0

        // When
        val result = CurrencyFormatter.formatToToman(amount)

        // Then
        assertEquals("1,234,567 تومان", result)
    }

    @Test
    fun formatWithoutUnit_shouldFormatWithCommas() {
        // Given
        val amount = 1000000.0

        // When
        val result = CurrencyFormatter.formatWithoutUnit(amount)

        // Then
        assertEquals("1,000,000", result)
    }

    @Test
    fun numberToWords_shouldConvertCorrectly() {
        // Test cases for number to words conversion
        val testCases = mapOf(
            0L to "صفر",
            1L to "یک",
            10L to "ده",
            11L to "یازده",
            20L to "بیست",
            21L to "بیست و یک",
            100L to "یکصد",
            101L to "یکصد و یک",
            1000L to "یک هزار",
            1001L to "یک هزار و یک",
            1234L to "یک هزار و دویست و سی و چهار",
            10000L to "ده هزار",
            100000L to "یکصد هزار",
            1000000L to "یک میلیون"
        )

        testCases.forEach { (input, expected) ->
            val result = CurrencyFormatter.numberToWords(input)
            assertEquals("Failed for input: $input", expected, result)
        }
    }

    @Test
    fun toWordsOnly_shouldReturnWordsWithUnit() {
        // Given
        val amount = 1234L

        // When
        val result = CurrencyFormatter.toWordsOnly(amount)

        // Then
        assertEquals("یک هزار و دویست و سی و چهار تومان", result)
    }

    @Test
    fun formatWithWords_shouldCombineNumberAndWords() {
        // Given
        val amount = 50000L

        // When
        val result = CurrencyFormatter.formatWithWords(amount)

        // Then
        assertEquals("50,000 (پنجاه هزار) تومان", result)
    }

    @Test
    fun formatWithSign_shouldShowSignCorrectly() {
        // Test positive number
        val positive = CurrencyFormatter.formatWithSign(1000.0, true)
        assertEquals("+1,000 تومان", positive)

        // Test negative number
        val negative = CurrencyFormatter.formatWithSign(-1000.0, true)
        assertEquals("-1,000 تومان", negative)

        // Test zero
        val zero = CurrencyFormatter.formatWithSign(0.0, true)
        assertEquals("0 تومان", zero)
    }

    @Test
    fun toPersianNumbers_shouldConvertDigits() {
        // Given
        val input = "1234567890"

        // When
        val result = CurrencyFormatter.toPersianNumbers(input)

        // Then
        assertEquals("۱۲۳۴۵۶۷۸۹۰", result)
    }

    @Test
    fun formatToPersianToman_shouldUsePersianDigits() {
        // Given
        val amount = 1234.0

        // When
        val result = CurrencyFormatter.formatToPersianToman(amount)

        // Then
        assertEquals("۱,۲۳۴ تومان", result)
    }

    @Test
    fun formatToRial_shouldConvertToRial() {
        // Given
        val amount = 1000.0 // 1000 تومان

        // When
        val result = CurrencyFormatter.formatToRial(amount)

        // Then
        assertEquals("10,000 ریال", result)
    }

    @Test
    fun edgeCases_shouldHandleCorrectly() {
        // Test very large numbers
        val largeNumber = 999999999L
        val largeResult = CurrencyFormatter.numberToWords(largeNumber)
        assertNotNull(largeResult)
        assertTrue(largeResult.isNotEmpty())

        // Test decimal formatting
        val decimal = 1234.56
        val decimalResult = CurrencyFormatter.formatToToman(decimal)
        assertEquals("1,235 تومان", decimalResult) // Should round

        // Test zero
        val zeroResult = CurrencyFormatter.numberToWords(0L)
        assertEquals("صفر", zeroResult)
    }

    @Test
    fun compatibilityMethods_shouldWork() {
        // Test format methods
        assertEquals("1,000 تومان", CurrencyFormatter.format(1000.0))
        assertEquals("1,000 تومان", CurrencyFormatter.format(1000L))
        assertEquals("1,000 تومان", CurrencyFormatter.format(1000))

        // Test formatWithoutUnit methods
        assertEquals("1,000", CurrencyFormatter.formatWithoutUnit(1000.0))
        assertEquals("1,000", CurrencyFormatter.formatWithoutUnit(1000L))
        assertEquals("1,000", CurrencyFormatter.formatWithoutUnit(1000))
    }
}
