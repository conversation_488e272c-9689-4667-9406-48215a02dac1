package com.example.ma.config

import com.example.ma.BuildConfig

/**
 * تنظیمات اپلیکیشن
 */
object AppConfig {
    
    /**
     * URL سرور Supabase
     */
    val SUPABASE_URL: String = BuildConfig.SUPABASE_URL
    
    /**
     * کلید Anonymous Supabase
     */
    val SUPABASE_ANON_KEY: String = BuildConfig.SUPABASE_ANON_KEY
    
    /**
     * نسخه اپلیکیشن
     */
    val APP_VERSION_NAME: String = BuildConfig.APP_VERSION_NAME
    
    /**
     * کد نسخه اپلیکیشن
     */
    val APP_VERSION_CODE: Int = BuildConfig.APP_VERSION_CODE
    
    /**
     * حالت دیباگ
     */
    val DEBUG_MODE: Boolean = BuildConfig.DEBUG_MODE
    
    /**
     * تنظیمات شبکه
     */
    object Network {
        const val CONNECT_TIMEOUT = 30L // seconds
        const val READ_TIMEOUT = 30L // seconds
        const val WRITE_TIMEOUT = 30L // seconds
        const val CALL_TIMEOUT = 60L // seconds
        
        const val MAX_RETRIES = 3
        const val RETRY_DELAY_MS = 1000L
        const val RETRY_BACKOFF_FACTOR = 2.0
    }
    
    /**
     * تنظیمات Real-time
     */
    object Realtime {
        const val HEARTBEAT_INTERVAL_MS = 30000L // 30 seconds
        const val RECONNECT_DELAY_MS = 5000L // 5 seconds
        const val MAX_RECONNECT_ATTEMPTS = 5
        
        val WEBSOCKET_URL: String = SUPABASE_URL.replace("https://", "wss://") + "/realtime/v1/websocket"
    }
    
    /**
     * تنظیمات Cache
     */
    object Cache {
        const val PROFILE_IMAGE_CACHE_SIZE = 50 * 1024 * 1024 // 50 MB
        const val PROFILE_IMAGE_CACHE_DURATION_HOURS = 24
        
        const val API_CACHE_SIZE = 10 * 1024 * 1024 // 10 MB
        const val API_CACHE_DURATION_MINUTES = 30
    }
    
    /**
     * تنظیمات UI
     */
    object UI {
        const val ANIMATION_DURATION_MS = 300L
        const val DEBOUNCE_DELAY_MS = 300L
        const val THROTTLE_INTERVAL_MS = 1000L
        
        const val PAGINATION_PAGE_SIZE = 20
        const val LOAD_MORE_THRESHOLD = 5
    }
    
    /**
     * تنظیمات فایل
     */
    object File {
        const val MAX_IMAGE_SIZE_MB = 5
        const val IMAGE_QUALITY = 80
        const val IMAGE_MAX_WIDTH = 1024
        const val IMAGE_MAX_HEIGHT = 1024
        
        const val PROFILE_IMAGES_DIR = "profile_images"
        const val TEMP_DIR = "temp"
    }
    
    /**
     * تنظیمات امنیتی
     */
    object Security {
        const val SESSION_TIMEOUT_MINUTES = 60
        const val MAX_LOGIN_ATTEMPTS = 5
        const val LOGIN_LOCKOUT_MINUTES = 15
        
        const val PASSWORD_MIN_LENGTH = 6
        const val USERNAME_MIN_LENGTH = 3
    }
    
    /**
     * تنظیمات کسب‌وکار
     */
    object Business {
        const val DEFAULT_BOTTLE_PRICE = 50000.0 // تومان
        const val MIN_TRANSACTION_AMOUNT = 1000.0 // تومان
        const val MAX_TRANSACTION_AMOUNT = 10000000.0 // تومان
        
        const val CURRENCY_SYMBOL = "تومان"
        const val CURRENCY_CODE = "IRR"
        
        val PAYMENT_TYPES = listOf("نقدی", "کارت به کارت", "چک")
        val EXPENSE_CATEGORIES = listOf("مواد اولیه", "حمل و نقل", "اجاره", "تجهیزات", "سایر")
        val WITHDRAWAL_TYPES = listOf("نقدی", "کارت به کارت")
    }
    
    /**
     * بررسی صحت تنظیمات
     */
    fun validateConfig(): Boolean {
        return SUPABASE_URL.isNotEmpty() && 
               SUPABASE_ANON_KEY.isNotEmpty() &&
               SUPABASE_URL.startsWith("https://") &&
               SUPABASE_ANON_KEY.length > 50 // JWT tokens are typically longer
    }
    
    /**
     * دریافت اطلاعات تنظیمات برای لاگ
     */
    fun getConfigInfo(): Map<String, Any> {
        return mapOf(
            "app_version" to APP_VERSION_NAME,
            "app_version_code" to APP_VERSION_CODE,
            "debug_mode" to DEBUG_MODE,
            "supabase_url" to SUPABASE_URL,
            "supabase_key_length" to SUPABASE_ANON_KEY.length,
            "config_valid" to validateConfig()
        )
    }
}
