package com.example.ma.data.model;

/**
 * کلاس‌های خطا برای مدیریت حرفه‌ای خطاها
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00060\u0001j\u0002`\u0002:\u000b\u0004\u0005\u0006\u0007\b\t\n\u000b\f\r\u000eB\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0003\u0082\u0001\u000b\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u00a8\u0006\u001a"}, d2 = {"Lcom/example/ma/data/model/ApiError;", "Ljava/lang/Exception;", "Lkotlin/Exception;", "()V", "AuthenticationError", "ConflictError", "DataProcessingError", "NetworkError", "NotFoundError", "PermissionError", "RateLimitError", "ServerError", "TimeoutError", "UnknownError", "ValidationError", "Lcom/example/ma/data/model/ApiError$AuthenticationError;", "Lcom/example/ma/data/model/ApiError$ConflictError;", "Lcom/example/ma/data/model/ApiError$DataProcessingError;", "Lcom/example/ma/data/model/ApiError$NetworkError;", "Lcom/example/ma/data/model/ApiError$NotFoundError;", "Lcom/example/ma/data/model/ApiError$PermissionError;", "Lcom/example/ma/data/model/ApiError$RateLimitError;", "Lcom/example/ma/data/model/ApiError$ServerError;", "Lcom/example/ma/data/model/ApiError$TimeoutError;", "Lcom/example/ma/data/model/ApiError$UnknownError;", "Lcom/example/ma/data/model/ApiError$ValidationError;", "app_debug"})
public abstract class ApiError extends java.lang.Exception {
    
    private ApiError() {
        super();
    }
    
    /**
     * خطای احراز هویت - نام کاربری یا رمز عبور اشتباه
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\u00020\u0004X\u0096D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/ma/data/model/ApiError$AuthenticationError;", "Lcom/example/ma/data/model/ApiError;", "()V", "message", "", "getMessage", "()Ljava/lang/String;", "app_debug"})
    public static final class AuthenticationError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private static final java.lang.String message = "\u0646\u0627\u0645 \u06a9\u0627\u0631\u0628\u0631\u06cc \u06cc\u0627 \u0631\u0645\u0632 \u0639\u0628\u0648\u0631 \u0627\u0634\u062a\u0628\u0627\u0647 \u0627\u0633\u062a";
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.data.model.ApiError.AuthenticationError INSTANCE = null;
        
        private AuthenticationError() {
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
    }
    
    /**
     * خطای تداخل - منبع قبلاً وجود دارد
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\n\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0003H\u00d6\u0001R\u0014\u0010\u0005\u001a\u00020\u0003X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/example/ma/data/model/ApiError$ConflictError;", "Lcom/example/ma/data/model/ApiError;", "resource", "", "(Ljava/lang/String;)V", "message", "getMessage", "()Ljava/lang/String;", "getResource", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class ConflictError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String resource = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String message = null;
        
        public ConflictError(@org.jetbrains.annotations.NotNull
        java.lang.String resource) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getResource() {
            return null;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError.ConflictError copy(@org.jetbrains.annotations.NotNull
        java.lang.String resource) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * خطای پردازش داده - مشکل در پردازش JSON یا داده‌ها
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\n\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0014\u0010\u0007\u001a\u00020\u0003X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0006\u00a8\u0006\u0012"}, d2 = {"Lcom/example/ma/data/model/ApiError$DataProcessingError;", "Lcom/example/ma/data/model/ApiError;", "details", "", "(Ljava/lang/String;)V", "getDetails", "()Ljava/lang/String;", "message", "getMessage", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class DataProcessingError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String details = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String message = null;
        
        public DataProcessingError(@org.jetbrains.annotations.NotNull
        java.lang.String details) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getDetails() {
            return null;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError.DataProcessingError copy(@org.jetbrains.annotations.NotNull
        java.lang.String details) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * خطای شبکه - مشکل در اتصال اینترنت
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\u00020\u0004X\u0096D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/ma/data/model/ApiError$NetworkError;", "Lcom/example/ma/data/model/ApiError;", "()V", "message", "", "getMessage", "()Ljava/lang/String;", "app_debug"})
    public static final class NetworkError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private static final java.lang.String message = "\u062e\u0637\u0627 \u062f\u0631 \u0627\u062a\u0635\u0627\u0644 \u0628\u0647 \u0627\u06cc\u0646\u062a\u0631\u0646\u062a";
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.data.model.ApiError.NetworkError INSTANCE = null;
        
        private NetworkError() {
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
    }
    
    /**
     * خطای یافت نشدن - منبع مورد نظر یافت نشد
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\n\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0003H\u00d6\u0001R\u0014\u0010\u0005\u001a\u00020\u0003X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/example/ma/data/model/ApiError$NotFoundError;", "Lcom/example/ma/data/model/ApiError;", "resource", "", "(Ljava/lang/String;)V", "message", "getMessage", "()Ljava/lang/String;", "getResource", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class NotFoundError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String resource = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String message = null;
        
        public NotFoundError(@org.jetbrains.annotations.NotNull
        java.lang.String resource) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getResource() {
            return null;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError.NotFoundError copy(@org.jetbrains.annotations.NotNull
        java.lang.String resource) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * خطای دسترسی - عدم مجوز برای انجام عملیات
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\u00020\u0004X\u0096D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/ma/data/model/ApiError$PermissionError;", "Lcom/example/ma/data/model/ApiError;", "()V", "message", "", "getMessage", "()Ljava/lang/String;", "app_debug"})
    public static final class PermissionError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private static final java.lang.String message = "\u0634\u0645\u0627 \u0645\u062c\u0648\u0632 \u0627\u0646\u062c\u0627\u0645 \u0627\u06cc\u0646 \u0639\u0645\u0644\u06cc\u0627\u062a \u0631\u0627 \u0646\u062f\u0627\u0631\u06cc\u062f";
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.data.model.ApiError.PermissionError INSTANCE = null;
        
        private PermissionError() {
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
    }
    
    /**
     * خطای محدودیت نرخ - تعداد درخواست‌ها بیش از حد مجاز
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\u00020\u0004X\u0096D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/ma/data/model/ApiError$RateLimitError;", "Lcom/example/ma/data/model/ApiError;", "()V", "message", "", "getMessage", "()Ljava/lang/String;", "app_debug"})
    public static final class RateLimitError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private static final java.lang.String message = "\u062a\u0639\u062f\u0627\u062f \u062f\u0631\u062e\u0648\u0627\u0633\u062a\u200c\u0647\u0627 \u0628\u06cc\u0634 \u0627\u0632 \u062d\u062f \u0645\u062c\u0627\u0632 \u0627\u0633\u062a. \u0644\u0637\u0641\u0627\u064b \u06a9\u0645\u06cc \u0635\u0628\u0631 \u06a9\u0646\u06cc\u062f";
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.data.model.ApiError.RateLimitError INSTANCE = null;
        
        private RateLimitError() {
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
    }
    
    /**
     * خطای سرور - مشکل در سمت سرور
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B\u0019\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u001f\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0014\u0010\t\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000b\u00a8\u0006\u0016"}, d2 = {"Lcom/example/ma/data/model/ApiError$ServerError;", "Lcom/example/ma/data/model/ApiError;", "code", "", "serverMessage", "", "(ILjava/lang/String;)V", "getCode", "()I", "message", "getMessage", "()Ljava/lang/String;", "getServerMessage", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "toString", "app_debug"})
    public static final class ServerError extends com.example.ma.data.model.ApiError {
        private final int code = 0;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String serverMessage = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String message = null;
        
        public ServerError(int code, @org.jetbrains.annotations.Nullable
        java.lang.String serverMessage) {
        }
        
        public final int getCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getServerMessage() {
            return null;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError.ServerError copy(int code, @org.jetbrains.annotations.Nullable
        java.lang.String serverMessage) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * خطای timeout - زمان انتظار تمام شد
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\u00020\u0004X\u0096D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/ma/data/model/ApiError$TimeoutError;", "Lcom/example/ma/data/model/ApiError;", "()V", "message", "", "getMessage", "()Ljava/lang/String;", "app_debug"})
    public static final class TimeoutError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private static final java.lang.String message = "\u0632\u0645\u0627\u0646 \u0627\u0646\u062a\u0638\u0627\u0631 \u062a\u0645\u0627\u0645 \u0634\u062f. \u0644\u0637\u0641\u0627\u064b \u062f\u0648\u0628\u0627\u0631\u0647 \u062a\u0644\u0627\u0634 \u06a9\u0646\u06cc\u062f";
        @org.jetbrains.annotations.NotNull
        public static final com.example.ma.data.model.ApiError.TimeoutError INSTANCE = null;
        
        private TimeoutError() {
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
    }
    
    /**
     * خطای نامشخص - سایر خطاها
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0011\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0004J\u000b\u0010\u000b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0015\u0010\f\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0006H\u00d6\u0001R\u0014\u0010\u0005\u001a\u00020\u0006X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0014"}, d2 = {"Lcom/example/ma/data/model/ApiError$UnknownError;", "Lcom/example/ma/data/model/ApiError;", "originalException", "", "(Ljava/lang/Throwable;)V", "message", "", "getMessage", "()Ljava/lang/String;", "getOriginalException", "()Ljava/lang/Throwable;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class UnknownError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.Nullable
        private final java.lang.Throwable originalException = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String message = null;
        
        public UnknownError(@org.jetbrains.annotations.Nullable
        java.lang.Throwable originalException) {
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.Throwable getOriginalException() {
            return null;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
        
        public UnknownError() {
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.Throwable component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError.UnknownError copy(@org.jetbrains.annotations.Nullable
        java.lang.Throwable originalException) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * خطای اعتبارسنجی - داده‌های ورودی نامعتبر
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0014\u0010\b\u001a\u00020\u0003X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0007\u00a8\u0006\u0015"}, d2 = {"Lcom/example/ma/data/model/ApiError$ValidationError;", "Lcom/example/ma/data/model/ApiError;", "field", "", "reason", "(Ljava/lang/String;Ljava/lang/String;)V", "getField", "()Ljava/lang/String;", "message", "getMessage", "getReason", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class ValidationError extends com.example.ma.data.model.ApiError {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String field = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String reason = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String message = null;
        
        public ValidationError(@org.jetbrains.annotations.NotNull
        java.lang.String field, @org.jetbrains.annotations.NotNull
        java.lang.String reason) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getField() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getReason() {
            return null;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError.ValidationError copy(@org.jetbrains.annotations.NotNull
        java.lang.String field, @org.jetbrains.annotations.NotNull
        java.lang.String reason) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
}