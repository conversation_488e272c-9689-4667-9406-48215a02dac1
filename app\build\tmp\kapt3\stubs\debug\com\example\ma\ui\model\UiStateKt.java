package com.example.ma.ui.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u001c\u0010\u0000\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u00a8\u0006\u0004"}, d2 = {"toUiState", "Lcom/example/ma/ui/model/UiState;", "T", "Lcom/example/ma/data/model/ApiResult;", "app_debug"})
public final class UiStateKt {
    
    /**
     * Extension functions برای تبدیل ApiResult به UiState
     */
    @org.jetbrains.annotations.NotNull
    public static final <T extends java.lang.Object>com.example.ma.ui.model.UiState<T> toUiState(@org.jetbrains.annotations.NotNull
    com.example.ma.data.model.ApiResult<? extends T> $this$toUiState) {
        return null;
    }
}