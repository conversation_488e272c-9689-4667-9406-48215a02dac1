<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:background="@color/primary_color"
            android:elevation="4dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="ثبت هزینه"
            app:titleTextColor="@color/text_white" />

        <!-- متن راهنما -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/info_color">

            <TextView
                android:id="@+id/tvHelpText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="💡 راهنمای ثبت هزینه"
                android:textSize="14sp"
                android:textColor="@color/text_white"
                android:padding="16dp"
                android:fontFamily="sans-serif"
                tools:text="💡 هزینه‌های مشارکتی (غذا، تفریح) به صورت 50/50 بین دو شریک تقسیم می‌شود" />

        </com.google.android.material.card.MaterialCardView>

        <!-- فرم ثبت هزینه -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- عنوان -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝 جزئیات هزینه"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="20dp"
                    android:fontFamily="sans-serif-medium" />

                <!-- دسته‌بندی -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="دسته‌بندی"
                    app:startIconDrawable="@drawable/ic_category"
                    app:startIconTint="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                    <AutoCompleteTextView
                        android:id="@+id/spinnerCategory"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif"
                        tools:text="مواد اولیه" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- مبلغ -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="مبلغ (تومان)"
                    app:startIconDrawable="@drawable/ic_money"
                    app:startIconTint="@color/primary_color"
                    app:suffixText="تومان"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif"
                        tools:text="500000" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- نمایش مبلغ به حروف -->
                <TextView
                    android:id="@+id/tvAmountInWords"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:padding="12dp"
                    android:background="@drawable/bg_amount_display"
                    android:text=""
                    android:textSize="14sp"
                    android:textColor="@color/primary_color"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:fontFamily="sans-serif-medium"
                    android:visibility="gone"
                    tools:text="پانصد هزار تومان"
                    tools:visibility="visible" />

                <!-- توضیحات -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:hint="توضیحات"
                    app:startIconDrawable="@drawable/ic_description"
                    app:startIconTint="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etDescription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="3"
                        android:maxLines="5"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif"
                        tools:text="خرید مواد اولیه برای تولید" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- دکمه ثبت -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSubmit"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="ثبت هزینه"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:backgroundTint="@color/primary_color"
                    app:cornerRadius="12dp"
                    app:icon="@drawable/ic_check"
                    app:iconGravity="textStart"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- نکات مهم -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📌 نکات مهم"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• هزینه ثبت شده نیاز به تایید شریک دارد\n• پس از تایید، در محاسبات اعمال می‌شود\n• امکان ویرایش فقط قبل از تایید وجود دارد"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:lineSpacingExtra="4dp"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
