@echo off
echo تست کردن proxy ها...

echo.
echo 1. تست proxy اول: 160.153.0.49:80
curl --proxy 160.153.0.49:80 --connect-timeout 5 -s -o nul -w "%%{http_code}" http://www.google.com
echo.

echo 2. تست proxy دوم: 209.127.191.252:5326
curl --proxy 209.127.191.252:5326 --connect-timeout 5 -s -o nul -w "%%{http_code}" http://www.google.com
echo.

echo 3. تست proxy سوم: 8.137.38.48:18080
curl --proxy 8.137.38.48:18080 --connect-timeout 5 -s -o nul -w "%%{http_code}" http://www.google.com
echo.

echo 4. تست proxy چهارم: 216.74.118.134:6289
curl --proxy 216.74.118.134:6289 --connect-timeout 5 -s -o nul -w "%%{http_code}" http://www.google.com
echo.

echo 5. تست proxy پنجم: 8.213.215.187:3129
curl --proxy 8.213.215.187:3129 --connect-timeout 5 -s -o nul -w "%%{http_code}" http://www.google.com
echo.

echo 6. تست proxy ششم: 173.211.69.223:6816
curl --proxy 173.211.69.223:6816 --connect-timeout 5 -s -o nul -w "%%{http_code}" http://www.google.com
echo.

echo 7. تست proxy هفتم: 52.78.241.34:3128
curl --proxy 52.78.241.34:3128 --connect-timeout 5 -s -o nul -w "%%{http_code}" http://www.google.com
echo.

echo 8. تست proxy هشتم: 8.211.200.183:1000
curl --proxy 8.211.200.183:1000 --connect-timeout 5 -s -o nul -w "%%{http_code}" http://www.google.com
echo.

echo تست تمام شد!
echo کد 200 یعنی proxy کار می‌کنه
pause
