/ Header Record For PersistentHashMapValueStorage@ ?$PROJECT_DIR$\app\src\main\java\com\example\ma\MAApplication.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktC B$PROJECT_DIR$\app\src\main\java\com\example\ma\config\AppConfig.ktI H$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\AppDatabase.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\dao\NotificationDao.ktP O$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\dao\TransactionDao.ktI H$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\dao\UserDao.ktW V$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\entity\NotificationEntity.ktV U$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\entity\TransactionEntity.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\data\local\entity\UserEntity.ktF E$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\ApiError.ktG F$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\ApiResult.ktN M$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\FinancialSummary.ktJ I$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\Notification.ktI H$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\Transaction.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\TransactionType.ktB A$PROJECT_DIR$\app\src\main\java\com\example\ma\data\model\User.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktU T$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseRealtimeClient.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\AuthRepository.ktY X$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\NotificationRepository.ktX W$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\TransactionRepository.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\auth\LoginActivity.ktF E$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\auth\LoginResult.ktI H$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\auth\LoginViewModel.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\dialogs\ThemeSelectionDialog.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\FinancialActivity.ktT S$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsAdapter.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\inventory\InventoryActivity.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\main\MainViewModel.ktC B$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\model\UiState.ktX W$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationAdapter.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\profile\ProfileActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\setup\DatabaseSetupActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\statistics\StatisticsActivity.ktK J$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CalculationManager.ktI H$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CoroutineManager.ktJ I$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyFormatter.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyInputTextWatcher.ktL K$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyTextWatcher.ktF E$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\DatabaseSetup.ktG F$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\ProfileManager.ktE D$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\ThemeManager.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\dialogs\ThemeSelectionDialog.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyInputTextWatcher.ktL K$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyTextWatcher.ktE D$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\ThemeManager.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CurrencyInputTextWatcher.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\UnifiedCurrencyTextWatcher.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktP O$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\SafeCurrencyTextWatcher.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\UnifiedCurrencyTextWatcher.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\SimpleCurrencyTextWatcher.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\SimpleCurrencyTextWatcher.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\SimpleCurrencyTextWatcher.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\main\MainViewModel.ktG F$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\ProfileManager.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationActivity.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\main\MainViewModel.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\AuthRepository.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktX W$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\TransactionRepository.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\data\repository\AuthRepository.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktT S$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsAdapter.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\main\MainViewModel.ktX W$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationAdapter.ktK J$PROJECT_DIR$\app\src\main\java\com\example\ma\utils\CalculationManager.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\auth\LoginActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\dialogs\ThemeSelectionDialog.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\FinancialActivity.ktT S$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsAdapter.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\inventory\InventoryActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationAdapter.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\profile\ProfileActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\setup\DatabaseSetupActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\statistics\StatisticsActivity.kt? >$PROJECT_DIR$\app\src\main\java\com\example\ma\MainActivity.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\auth\LoginActivity.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\dialogs\ThemeSelectionDialog.ktO N$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\ExpenseActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\FinancialActivity.ktT S$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\TransactionsAdapter.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\financial\WithdrawalActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\inventory\InventoryActivity.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\main\MainViewModel.ktX W$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationAdapterNew.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\profile\ProfileActivity.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\setup\DatabaseSetupActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\statistics\StatisticsActivity.ktM L$PROJECT_DIR$\app\src\main\java\com\example\ma\data\remote\SupabaseClient.ktH G$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\main\MainViewModel.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\ma\ui\notifications\NotificationAdapterNew.kt