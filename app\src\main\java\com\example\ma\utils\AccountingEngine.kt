package com.example.ma.utils

import com.example.ma.data.remote.SupabaseClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * موتور حسابداری کامل برای محاسبه real-time
 */
object AccountingEngine {

    /**
     * محاسبه کامل وضعیت مالی هر شریک
     */
    suspend fun calculateCompleteFinancialStatus(): FinancialStatus = withContext(Dispatchers.IO) {
        try {
            println("🧮 AccountingEngine: شروع محاسبات کامل")
            
            // 1. دریافت کل فروش‌های تایید شده
            val approvedSales = getApprovedSales()
            val totalSales = approvedSales.sumOf { it.amount }
            
            // 2. دریافت کل هزینه‌های تایید شده
            val approvedExpenses = getApprovedExpenses()
            val totalExpenses = approvedExpenses.sumOf { it.amount }
            
            // 3. محاسبه سود خالص
            val netProfit = totalSales - totalExpenses
            
            // 4. محاسبه سهم مساوی (50/50)
            val equalShare = netProfit / 2
            
            // 5. محاسبه هزینه‌های پرداختی هر شریک
            val miladExpenses = approvedExpenses.filter { it.userId == "Miladnasiri" }.sumOf { it.amount }
            val aliExpenses = approvedExpenses.filter { it.userId == "Alikakai" }.sumOf { it.amount }
            val contributionDiff = aliExpenses - miladExpenses
            
            // 6. تعدیل سهم‌ها بر اساس هزینه‌های پرداختی
            val miladAdjustedShare = equalShare - contributionDiff
            val aliAdjustedShare = equalShare + contributionDiff
            
            // 7. محاسبه برداشت‌های شخصی
            val approvedWithdrawals = getApprovedWithdrawals()
            val miladWithdrawals = approvedWithdrawals.filter { it.userId == "Miladnasiri" }.sumOf { it.amount }
            val aliWithdrawals = approvedWithdrawals.filter { it.userId == "Alikakai" }.sumOf { it.amount }
            
            // 8. محاسبه موجودی نهایی
            val miladFinalBalance = miladAdjustedShare - miladWithdrawals
            val aliFinalBalance = aliAdjustedShare - aliWithdrawals
            
            // 9. محاسبه موجودی حساب‌های مختلف
            val miladAccounts = calculateAccountBalances("Miladnasiri", approvedSales)
            val aliAccounts = calculateAccountBalances("Alikakai", approvedSales)
            
            println("✅ AccountingEngine: محاسبات کامل شد")
            
            FinancialStatus(
                totalSales = totalSales,
                totalExpenses = totalExpenses,
                netProfit = netProfit,
                miladShare = miladAdjustedShare,
                aliShare = aliAdjustedShare,
                miladWithdrawals = miladWithdrawals,
                aliWithdrawals = aliWithdrawals,
                miladFinalBalance = miladFinalBalance,
                aliFinalBalance = aliFinalBalance,
                miladAccounts = miladAccounts,
                aliAccounts = aliAccounts,
                lastCalculated = System.currentTimeMillis()
            )
            
        } catch (e: Exception) {
            println("❌ AccountingEngine: خطا در محاسبات = ${e.message}")
            e.printStackTrace()
            FinancialStatus.empty()
        }
    }
    
    /**
     * محاسبه موجودی حساب‌های مختلف (نقدی، کارت، شخصی)
     */
    private suspend fun calculateAccountBalances(userId: String, sales: List<SaleData>): AccountBalances {
        val userSales = sales.filter { it.userId == userId }
        
        val cashBalance = userSales.filter { it.paymentType == "cash" }.sumOf { it.amount }
        val cardBalance = userSales.filter { it.paymentType == "card" }.sumOf { it.amount }
        val personalBalance = userSales.filter { it.paymentType == "personal" }.sumOf { it.amount }
        
        return AccountBalances(
            cash = cashBalance,
            card = cardBalance,
            personal = personalBalance,
            total = cashBalance + cardBalance + personalBalance
        )
    }
    
    /**
     * دریافت فروش‌های تایید شده
     */
    private suspend fun getApprovedSales(): List<SaleData> {
        return try {
            val salesData = SupabaseClient.getApprovedSales()
            salesData?.map { sale ->
                SaleData(
                    id = sale["id"] as? String ?: "",
                    userId = sale["user_id"] as? String ?: "",
                    amount = (sale["amount"] as? Number)?.toDouble() ?: 0.0,
                    quantity = (sale["quantity"] as? Number)?.toInt() ?: 0,
                    paymentType = sale["payment_type"] as? String ?: "cash",
                    description = sale["description"] as? String ?: "",
                    status = sale["status"] as? String ?: "",
                    createdAt = sale["created_at"] as? String ?: ""
                )
            } ?: emptyList()
        } catch (e: Exception) {
            println("❌ getApprovedSales: ${e.message}")
            emptyList()
        }
    }
    
    /**
     * دریافت هزینه‌های تایید شده
     */
    private suspend fun getApprovedExpenses(): List<ExpenseData> {
        return try {
            val expensesData = SupabaseClient.getApprovedExpenses()
            expensesData?.map { expense ->
                ExpenseData(
                    id = expense["id"] as? String ?: "",
                    userId = expense["user_id"] as? String ?: "",
                    amount = (expense["amount"] as? Number)?.toDouble() ?: 0.0,
                    type = expense["type"] as? String ?: "",
                    category = expense["category"] as? String ?: "",
                    description = expense["description"] as? String ?: "",
                    status = expense["status"] as? String ?: "",
                    createdAt = expense["created_at"] as? String ?: ""
                )
            } ?: emptyList()
        } catch (e: Exception) {
            println("❌ getApprovedExpenses: ${e.message}")
            emptyList()
        }
    }

    /**
     * دریافت برداشت‌های تایید شده
     */
    private suspend fun getApprovedWithdrawals(): List<WithdrawalData> {
        return try {
            val withdrawalsData = SupabaseClient.getApprovedWithdrawals()
            withdrawalsData?.map { withdrawal ->
                WithdrawalData(
                    id = withdrawal["id"] as? String ?: "",
                    userId = withdrawal["user_id"] as? String ?: "",
                    amount = (withdrawal["amount"] as? Number)?.toDouble() ?: 0.0,
                    withdrawalType = withdrawal["withdrawal_type"] as? String ?: "",
                    description = withdrawal["description"] as? String ?: "",
                    status = withdrawal["status"] as? String ?: "",
                    createdAt = withdrawal["created_at"] as? String ?: ""
                )
            } ?: emptyList()
        } catch (e: Exception) {
            println("❌ getApprovedWithdrawals: ${e.message}")
            emptyList()
        }
    }
    
    /**
     * بروزرسانی جدول account_balances
     */
    suspend fun updateAccountBalances(status: FinancialStatus): Boolean {
        return try {
            // بروزرسانی میلاد
            val miladSuccess = SupabaseClient.updateAccountBalance(
                userId = "Miladnasiri",
                profitShare = status.miladFinalBalance,
                totalSales = status.totalSales,
                totalExpensesPaid = status.miladShare + status.miladWithdrawals - status.miladFinalBalance,
                totalWithdrawals = status.miladWithdrawals
            )
            
            // بروزرسانی علی
            val aliSuccess = SupabaseClient.updateAccountBalance(
                userId = "Alikakai",
                profitShare = status.aliFinalBalance,
                totalSales = status.totalSales,
                totalExpensesPaid = status.aliShare + status.aliWithdrawals - status.aliFinalBalance,
                totalWithdrawals = status.aliWithdrawals
            )
            
            miladSuccess && aliSuccess
        } catch (e: Exception) {
            println("❌ AccountingEngine: خطا در بروزرسانی account_balances = ${e.message}")
            false
        }
    }
}

/**
 * وضعیت کامل مالی
 */
data class FinancialStatus(
    val totalSales: Double,
    val totalExpenses: Double,
    val netProfit: Double,
    val miladShare: Double,
    val aliShare: Double,
    val miladWithdrawals: Double,
    val aliWithdrawals: Double,
    val miladFinalBalance: Double,
    val aliFinalBalance: Double,
    val miladAccounts: AccountBalances,
    val aliAccounts: AccountBalances,
    val lastCalculated: Long
) {
    companion object {
        fun empty() = FinancialStatus(
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
            AccountBalances.empty(), AccountBalances.empty(), 0L
        )
    }
}

/**
 * موجودی حساب‌های مختلف
 */
data class AccountBalances(
    val cash: Double,
    val card: Double,
    val personal: Double,
    val total: Double
) {
    companion object {
        fun empty() = AccountBalances(0.0, 0.0, 0.0, 0.0)
    }
}

/**
 * داده فروش
 */
data class SaleData(
    val id: String,
    val userId: String,
    val amount: Double,
    val quantity: Int,
    val paymentType: String,
    val description: String,
    val status: String,
    val createdAt: String
)

/**
 * داده هزینه
 */
data class ExpenseData(
    val id: String,
    val userId: String,
    val amount: Double,
    val type: String,
    val category: String,
    val description: String,
    val status: String,
    val createdAt: String
)

/**
 * داده برداشت
 */
data class WithdrawalData(
    val id: String,
    val userId: String,
    val amount: Double,
    val withdrawalType: String,
    val description: String,
    val status: String,
    val createdAt: String
)
