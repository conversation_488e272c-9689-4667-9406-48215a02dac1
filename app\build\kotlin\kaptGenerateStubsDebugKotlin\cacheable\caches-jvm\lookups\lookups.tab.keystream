  MainActivity com.example.ma  AuthRepository android.app.Activity  Bitmap android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  CoroutineManager android.app.Activity  DrawerLayout android.app.Activity  	ImageView android.app.Activity  Int android.app.Activity  Intent android.app.Activity  
MainViewModel android.app.Activity  MaterialButton android.app.Activity  ProfileManager android.app.Activity  RadioButton android.app.Activity  SharedPreferences android.app.Activity  Spinner android.app.Activity  String android.app.Activity  TextInputEditText android.app.Activity  TextView android.app.Activity  User android.app.Activity  View android.app.Activity  Context android.content  Intent android.content  SharedPreferences android.content  AuthRepository android.content.Context  Bitmap android.content.Context  Boolean android.content.Context  Bundle android.content.Context  CoroutineManager android.content.Context  DrawerLayout android.content.Context  	ImageView android.content.Context  Int android.content.Context  Intent android.content.Context  
MainViewModel android.content.Context  MaterialButton android.content.Context  ProfileManager android.content.Context  RadioButton android.content.Context  SharedPreferences android.content.Context  Spinner android.content.Context  String android.content.Context  TextInputEditText android.content.Context  TextView android.content.Context  User android.content.Context  View android.content.Context  AuthRepository android.content.ContextWrapper  Bitmap android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  CoroutineManager android.content.ContextWrapper  DrawerLayout android.content.ContextWrapper  	ImageView android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  
MainViewModel android.content.ContextWrapper  MaterialButton android.content.ContextWrapper  ProfileManager android.content.ContextWrapper  RadioButton android.content.ContextWrapper  SharedPreferences android.content.ContextWrapper  Spinner android.content.ContextWrapper  String android.content.ContextWrapper  TextInputEditText android.content.ContextWrapper  TextView android.content.ContextWrapper  User android.content.ContextWrapper  View android.content.ContextWrapper  Bitmap android.graphics  
BitmapFactory android.graphics  Bundle 
android.os  Editable android.text  TextWatcher android.text  View android.view  AuthRepository  android.view.ContextThemeWrapper  Bitmap  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  CoroutineManager  android.view.ContextThemeWrapper  DrawerLayout  android.view.ContextThemeWrapper  	ImageView  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  
MainViewModel  android.view.ContextThemeWrapper  MaterialButton  android.view.ContextThemeWrapper  ProfileManager  android.view.ContextThemeWrapper  RadioButton  android.view.ContextThemeWrapper  SharedPreferences  android.view.ContextThemeWrapper  Spinner  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  TextInputEditText  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  User  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  	ImageView android.widget  RadioButton android.widget  Spinner android.widget  TextView android.widget  AuthRepository #androidx.activity.ComponentActivity  Bitmap #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  CoroutineManager #androidx.activity.ComponentActivity  DrawerLayout #androidx.activity.ComponentActivity  	ImageView #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  
MainViewModel #androidx.activity.ComponentActivity  MaterialButton #androidx.activity.ComponentActivity  ProfileManager #androidx.activity.ComponentActivity  RadioButton #androidx.activity.ComponentActivity  SharedPreferences #androidx.activity.ComponentActivity  Spinner #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  TextInputEditText #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  User #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  ActionBarDrawerToggle androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  AuthRepository (androidx.appcompat.app.AppCompatActivity  Bitmap (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  CoroutineManager (androidx.appcompat.app.AppCompatActivity  DrawerLayout (androidx.appcompat.app.AppCompatActivity  	ImageView (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  
MainViewModel (androidx.appcompat.app.AppCompatActivity  MaterialButton (androidx.appcompat.app.AppCompatActivity  ProfileManager (androidx.appcompat.app.AppCompatActivity  RadioButton (androidx.appcompat.app.AppCompatActivity  SharedPreferences (androidx.appcompat.app.AppCompatActivity  Spinner (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  TextInputEditText (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  User (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  AuthRepository #androidx.core.app.ComponentActivity  Bitmap #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CoroutineManager #androidx.core.app.ComponentActivity  DrawerLayout #androidx.core.app.ComponentActivity  	ImageView #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  
MainViewModel #androidx.core.app.ComponentActivity  MaterialButton #androidx.core.app.ComponentActivity  ProfileManager #androidx.core.app.ComponentActivity  RadioButton #androidx.core.app.ComponentActivity  SharedPreferences #androidx.core.app.ComponentActivity  Spinner #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  TextInputEditText #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  User #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  
GravityCompat androidx.core.view  DrawerLayout androidx.drawerlayout.widget  AuthRepository &androidx.fragment.app.FragmentActivity  Bitmap &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  CoroutineManager &androidx.fragment.app.FragmentActivity  DrawerLayout &androidx.fragment.app.FragmentActivity  	ImageView &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  
MainViewModel &androidx.fragment.app.FragmentActivity  MaterialButton &androidx.fragment.app.FragmentActivity  ProfileManager &androidx.fragment.app.FragmentActivity  RadioButton &androidx.fragment.app.FragmentActivity  SharedPreferences &androidx.fragment.app.FragmentActivity  Spinner &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  TextInputEditText &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  User &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  ViewModelProvider androidx.lifecycle  lifecycleScope androidx.lifecycle  Boolean com.example.ma  	ImageView com.example.ma  Int com.example.ma  RadioButton com.example.ma  Spinner com.example.ma  String com.example.ma  TextView com.example.ma  AuthRepository com.example.ma.MainActivity  Bitmap com.example.ma.MainActivity  Boolean com.example.ma.MainActivity  Bundle com.example.ma.MainActivity  CoroutineManager com.example.ma.MainActivity  DrawerLayout com.example.ma.MainActivity  	ImageView com.example.ma.MainActivity  Int com.example.ma.MainActivity  Intent com.example.ma.MainActivity  
MainViewModel com.example.ma.MainActivity  MaterialButton com.example.ma.MainActivity  ProfileManager com.example.ma.MainActivity  RadioButton com.example.ma.MainActivity  SharedPreferences com.example.ma.MainActivity  Spinner com.example.ma.MainActivity  String com.example.ma.MainActivity  TextInputEditText com.example.ma.MainActivity  TextView com.example.ma.MainActivity  User com.example.ma.MainActivity  View com.example.ma.MainActivity  AuthRepository %com.example.ma.MainActivity.Companion  Bitmap %com.example.ma.MainActivity.Companion  Boolean %com.example.ma.MainActivity.Companion  Bundle %com.example.ma.MainActivity.Companion  CoroutineManager %com.example.ma.MainActivity.Companion  DrawerLayout %com.example.ma.MainActivity.Companion  	ImageView %com.example.ma.MainActivity.Companion  Int %com.example.ma.MainActivity.Companion  Intent %com.example.ma.MainActivity.Companion  
MainViewModel %com.example.ma.MainActivity.Companion  MaterialButton %com.example.ma.MainActivity.Companion  ProfileManager %com.example.ma.MainActivity.Companion  RadioButton %com.example.ma.MainActivity.Companion  SharedPreferences %com.example.ma.MainActivity.Companion  Spinner %com.example.ma.MainActivity.Companion  String %com.example.ma.MainActivity.Companion  TextInputEditText %com.example.ma.MainActivity.Companion  TextView %com.example.ma.MainActivity.Companion  User %com.example.ma.MainActivity.Companion  View %com.example.ma.MainActivity.Companion  TransactionType com.example.ma.data.model  User com.example.ma.data.model  SupabaseClient com.example.ma.data.remote  AuthRepository com.example.ma.data.repository  
LoginActivity com.example.ma.ui.auth  ThemeSelectionDialog com.example.ma.ui.dialogs  
MainViewModel com.example.ma.ui.main  ProfileActivity com.example.ma.ui.profile  DatabaseSetupActivity com.example.ma.ui.setup  CoroutineManager com.example.ma.utils  CurrencyFormatter com.example.ma.utils  ProfileManager com.example.ma.utils  SimpleCurrencyTextWatcher com.example.ma.utils  ThemeManager com.example.ma.utils  createCoroutineManager com.example.ma.utils  MaterialToolbar "com.google.android.material.appbar  MaterialButton "com.google.android.material.button  NavigationView &com.google.android.material.navigation  TextInputEditText %com.google.android.material.textfield  File java.io  Boolean kotlin  Int kotlin  Nothing kotlin  String kotlin  Dispatchers kotlinx.coroutines  isActive kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  FinancialStats com.example.ma.ui.main  Application android.app  AndroidViewModel androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  viewModelScope androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  Double #androidx.lifecycle.AndroidViewModel  FinancialStats #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  LiveData #androidx.lifecycle.AndroidViewModel  MutableLiveData #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  Transaction #androidx.lifecycle.AndroidViewModel  TransactionRepository #androidx.lifecycle.AndroidViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  FinancialStats androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Transaction androidx.lifecycle.ViewModel  TransactionRepository androidx.lifecycle.ViewModel  Transaction com.example.ma.data.model  TransactionRepository com.example.ma.data.repository  Boolean com.example.ma.ui.main  Double com.example.ma.ui.main  Int com.example.ma.ui.main  List com.example.ma.ui.main  MutableLiveData com.example.ma.ui.main  String com.example.ma.ui.main  TransactionRepository com.example.ma.ui.main  Double %com.example.ma.ui.main.FinancialStats  Int %com.example.ma.ui.main.FinancialStats  Application $com.example.ma.ui.main.MainViewModel  Boolean $com.example.ma.ui.main.MainViewModel  Double $com.example.ma.ui.main.MainViewModel  FinancialStats $com.example.ma.ui.main.MainViewModel  Int $com.example.ma.ui.main.MainViewModel  List $com.example.ma.ui.main.MainViewModel  LiveData $com.example.ma.ui.main.MainViewModel  MutableLiveData $com.example.ma.ui.main.MainViewModel  String $com.example.ma.ui.main.MainViewModel  Transaction $com.example.ma.ui.main.MainViewModel  TransactionRepository $com.example.ma.ui.main.MainViewModel  
_errorMessage $com.example.ma.ui.main.MainViewModel  
_isLoading $com.example.ma.ui.main.MainViewModel  
_transactions $com.example.ma.ui.main.MainViewModel  MutableLiveData 	java.lang  TransactionRepository 	java.lang  Date 	java.util  Double kotlin  MutableLiveData kotlin  TransactionRepository kotlin  MutableLiveData kotlin.annotation  TransactionRepository kotlin.annotation  List kotlin.collections  MutableLiveData kotlin.collections  TransactionRepository kotlin.collections  MutableLiveData kotlin.comparisons  TransactionRepository kotlin.comparisons  MutableLiveData 	kotlin.io  TransactionRepository 	kotlin.io  MutableLiveData 
kotlin.jvm  TransactionRepository 
kotlin.jvm  MutableLiveData 
kotlin.ranges  TransactionRepository 
kotlin.ranges  MutableLiveData kotlin.sequences  TransactionRepository kotlin.sequences  MutableLiveData kotlin.text  TransactionRepository kotlin.text  MODE_PRIVATE android.content.Context  getSharedPreferences android.content.Context  ConnectivityManager android.net  NetworkCapabilities android.net  Build 
android.os  	AppConfig com.example.ma.config  Network com.example.ma.config.AppConfig  SUPABASE_ANON_KEY com.example.ma.config.AppConfig  SUPABASE_URL com.example.ma.config.AppConfig  CALL_TIMEOUT 'com.example.ma.config.AppConfig.Network  CONNECT_TIMEOUT 'com.example.ma.config.AppConfig.Network  READ_TIMEOUT 'com.example.ma.config.AppConfig.Network  
WRITE_TIMEOUT 'com.example.ma.config.AppConfig.Network  ApiError com.example.ma.data.model  	ApiResult com.example.ma.data.model  safeApiCall com.example.ma.data.model  
toApiError com.example.ma.data.model  Any com.example.ma.data.remote  	AppConfig com.example.ma.data.remote  Array com.example.ma.data.remote  Boolean com.example.ma.data.remote  	ByteArray com.example.ma.data.remote  Double com.example.ma.data.remote  Gson com.example.ma.data.remote  Int com.example.ma.data.remote  List com.example.ma.data.remote  Map com.example.ma.data.remote  OkHttpClient com.example.ma.data.remote  Request com.example.ma.data.remote  Response com.example.ma.data.remote  String com.example.ma.data.remote  TimeUnit com.example.ma.data.remote  Unit com.example.ma.data.remote  Any )com.example.ma.data.remote.SupabaseClient  	ApiResult )com.example.ma.data.remote.SupabaseClient  	AppConfig )com.example.ma.data.remote.SupabaseClient  Array )com.example.ma.data.remote.SupabaseClient  Boolean )com.example.ma.data.remote.SupabaseClient  	ByteArray )com.example.ma.data.remote.SupabaseClient  Context )com.example.ma.data.remote.SupabaseClient  Double )com.example.ma.data.remote.SupabaseClient  Gson )com.example.ma.data.remote.SupabaseClient  Int )com.example.ma.data.remote.SupabaseClient  List )com.example.ma.data.remote.SupabaseClient  Map )com.example.ma.data.remote.SupabaseClient  OkHttpClient )com.example.ma.data.remote.SupabaseClient  Request )com.example.ma.data.remote.SupabaseClient  Response )com.example.ma.data.remote.SupabaseClient  String )com.example.ma.data.remote.SupabaseClient  TimeUnit )com.example.ma.data.remote.SupabaseClient  	TypeToken )com.example.ma.data.remote.SupabaseClient  Unit )com.example.ma.data.remote.SupabaseClient  getTYPE Icom.example.ma.data.remote.SupabaseClient.arrayMapType.<no name provided>  getType Icom.example.ma.data.remote.SupabaseClient.arrayMapType.<no name provided>  setType Icom.example.ma.data.remote.SupabaseClient.arrayMapType.<no name provided>  getTYPE Dcom.example.ma.data.remote.SupabaseClient.mapType.<no name provided>  getType Dcom.example.ma.data.remote.SupabaseClient.mapType.<no name provided>  setType Dcom.example.ma.data.remote.SupabaseClient.mapType.<no name provided>  Boolean com.example.ma.data.repository  Context com.example.ma.data.repository  String com.example.ma.data.repository  Boolean -com.example.ma.data.repository.AuthRepository  Context -com.example.ma.data.repository.AuthRepository  SharedPreferences -com.example.ma.data.repository.AuthRepository  String -com.example.ma.data.repository.AuthRepository  User -com.example.ma.data.repository.AuthRepository  context -com.example.ma.data.repository.AuthRepository  Boolean 7com.example.ma.data.repository.AuthRepository.Companion  Context 7com.example.ma.data.repository.AuthRepository.Companion  SharedPreferences 7com.example.ma.data.repository.AuthRepository.Companion  String 7com.example.ma.data.repository.AuthRepository.Companion  User 7com.example.ma.data.repository.AuthRepository.Companion  Gson com.google.gson  	TypeToken com.google.gson.reflect  	AppConfig 	java.lang  Context 	java.lang  Gson 	java.lang  OkHttpClient 	java.lang  TimeUnit 	java.lang  Type java.lang.reflect  ConnectException java.net  SocketTimeoutException java.net  UnknownHostException java.net  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  Any kotlin  	AppConfig kotlin  Array kotlin  	ByteArray kotlin  Context kotlin  Gson kotlin  Long kotlin  OkHttpClient kotlin  TimeUnit kotlin  Unit kotlin  	AppConfig kotlin.annotation  Context kotlin.annotation  Gson kotlin.annotation  OkHttpClient kotlin.annotation  TimeUnit kotlin.annotation  	AppConfig kotlin.collections  Context kotlin.collections  Gson kotlin.collections  Map kotlin.collections  OkHttpClient kotlin.collections  TimeUnit kotlin.collections  	AppConfig kotlin.comparisons  Context kotlin.comparisons  Gson kotlin.comparisons  OkHttpClient kotlin.comparisons  TimeUnit kotlin.comparisons  	AppConfig 	kotlin.io  Context 	kotlin.io  Gson 	kotlin.io  OkHttpClient 	kotlin.io  TimeUnit 	kotlin.io  	AppConfig 
kotlin.jvm  Context 
kotlin.jvm  Gson 
kotlin.jvm  OkHttpClient 
kotlin.jvm  TimeUnit 
kotlin.jvm  	AppConfig 
kotlin.ranges  Context 
kotlin.ranges  Gson 
kotlin.ranges  OkHttpClient 
kotlin.ranges  TimeUnit 
kotlin.ranges  	AppConfig kotlin.sequences  Context kotlin.sequences  Gson kotlin.sequences  OkHttpClient kotlin.sequences  TimeUnit kotlin.sequences  	AppConfig kotlin.text  Context kotlin.text  Gson kotlin.text  OkHttpClient kotlin.text  TimeUnit kotlin.text  CoroutineScope kotlinx.coroutines  GlobalScope kotlinx.coroutines  	AppConfig okhttp3  Gson okhttp3  	MediaType okhttp3  OkHttpClient okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  TimeUnit okhttp3  	Companion okhttp3.MediaType  toMediaType okhttp3.MediaType.Companion  Builder okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  callTimeout okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  retryOnConnectionFailure okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  	Companion okhttp3.RequestBody  
toRequestBody okhttp3.RequestBody.Companion  Transaction )com.example.ma.data.remote.SupabaseClient  List com.example.ma.data.repository  Boolean 4com.example.ma.data.repository.TransactionRepository  List 4com.example.ma.data.repository.TransactionRepository  String 4com.example.ma.data.repository.TransactionRepository  Transaction 4com.example.ma.data.repository.TransactionRepository  UUID 	java.util  resume kotlin.coroutines  suspendCancellableCoroutine kotlinx.coroutines  TransactionsActivity com.example.ma.ui.financial  NotificationAdapter com.example.ma.ui.notifications  TransactionsAdapter com.example.ma.ui.financial  OnNotificationActionListener 3com.example.ma.ui.notifications.NotificationAdapter  NotificationActivity com.example.ma.ui.notifications  NotificationViewHolder 3com.example.ma.ui.notifications.NotificationAdapter  CalculationManager com.example.ma.utils  TransactionViewHolder /com.example.ma.ui.financial.TransactionsAdapter  DatePickerDialog android.app  Any android.app.Activity  AutoCompleteTextView android.app.Activity  Locale android.app.Activity  Map android.app.Activity  NotificationAdapter android.app.Activity  RecyclerView android.app.Activity  SimpleDateFormat android.app.Activity  SupabaseRealtimeClient android.app.Activity  TransactionsAdapter android.app.Activity  invoke android.app.Activity  Any android.content.Context  AutoCompleteTextView android.content.Context  Locale android.content.Context  Map android.content.Context  NotificationAdapter android.content.Context  RecyclerView android.content.Context  SimpleDateFormat android.content.Context  SupabaseRealtimeClient android.content.Context  TransactionsAdapter android.content.Context  invoke android.content.Context  Any android.content.ContextWrapper  AutoCompleteTextView android.content.ContextWrapper  Locale android.content.ContextWrapper  Map android.content.ContextWrapper  NotificationAdapter android.content.ContextWrapper  RecyclerView android.content.ContextWrapper  SimpleDateFormat android.content.ContextWrapper  SupabaseRealtimeClient android.content.ContextWrapper  TransactionsAdapter android.content.ContextWrapper  invoke android.content.ContextWrapper  LayoutInflater android.view  	ViewGroup android.view  Any  android.view.ContextThemeWrapper  AutoCompleteTextView  android.view.ContextThemeWrapper  Locale  android.view.ContextThemeWrapper  Map  android.view.ContextThemeWrapper  NotificationAdapter  android.view.ContextThemeWrapper  RecyclerView  android.view.ContextThemeWrapper  SimpleDateFormat  android.view.ContextThemeWrapper  SupabaseRealtimeClient  android.view.ContextThemeWrapper  TransactionsAdapter  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  findViewById android.view.View  ArrayAdapter android.widget  AutoCompleteTextView android.widget  Toast android.widget  Any #androidx.activity.ComponentActivity  AutoCompleteTextView #androidx.activity.ComponentActivity  Locale #androidx.activity.ComponentActivity  Map #androidx.activity.ComponentActivity  NotificationAdapter #androidx.activity.ComponentActivity  RecyclerView #androidx.activity.ComponentActivity  SimpleDateFormat #androidx.activity.ComponentActivity  SupabaseRealtimeClient #androidx.activity.ComponentActivity  TransactionsAdapter #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  Any (androidx.appcompat.app.AppCompatActivity  AutoCompleteTextView (androidx.appcompat.app.AppCompatActivity  Locale (androidx.appcompat.app.AppCompatActivity  Map (androidx.appcompat.app.AppCompatActivity  NotificationAdapter (androidx.appcompat.app.AppCompatActivity  RecyclerView (androidx.appcompat.app.AppCompatActivity  SimpleDateFormat (androidx.appcompat.app.AppCompatActivity  SupabaseRealtimeClient (androidx.appcompat.app.AppCompatActivity  TransactionsAdapter (androidx.appcompat.app.AppCompatActivity  invoke (androidx.appcompat.app.AppCompatActivity  Any #androidx.core.app.ComponentActivity  AutoCompleteTextView #androidx.core.app.ComponentActivity  Locale #androidx.core.app.ComponentActivity  Map #androidx.core.app.ComponentActivity  NotificationAdapter #androidx.core.app.ComponentActivity  RecyclerView #androidx.core.app.ComponentActivity  SimpleDateFormat #androidx.core.app.ComponentActivity  SupabaseRealtimeClient #androidx.core.app.ComponentActivity  TransactionsAdapter #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  Any &androidx.fragment.app.FragmentActivity  AutoCompleteTextView &androidx.fragment.app.FragmentActivity  Locale &androidx.fragment.app.FragmentActivity  Map &androidx.fragment.app.FragmentActivity  NotificationAdapter &androidx.fragment.app.FragmentActivity  RecyclerView &androidx.fragment.app.FragmentActivity  SimpleDateFormat &androidx.fragment.app.FragmentActivity  SupabaseRealtimeClient &androidx.fragment.app.FragmentActivity  TransactionsAdapter &androidx.fragment.app.FragmentActivity  invoke &androidx.fragment.app.FragmentActivity  LinearLayoutManager androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  Any 1androidx.recyclerview.widget.RecyclerView.Adapter  Context 1androidx.recyclerview.widget.RecyclerView.Adapter  Double 1androidx.recyclerview.widget.RecyclerView.Adapter  	ImageView 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  Locale 1androidx.recyclerview.widget.RecyclerView.Adapter  Map 1androidx.recyclerview.widget.RecyclerView.Adapter  MaterialCardView 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  SimpleDateFormat 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  listOf 1androidx.recyclerview.widget.RecyclerView.Adapter  
mutableListOf 1androidx.recyclerview.widget.RecyclerView.Adapter  Any 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Double 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	ImageView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Map 4androidx.recyclerview.widget.RecyclerView.ViewHolder  MaterialCardView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  String 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R com.example.ma  id com.example.ma.R  
btnApprove com.example.ma.R.id  	btnReject com.example.ma.R.id  
cardStatus com.example.ma.R.id  cardTransaction com.example.ma.R.id  ivNotificationType com.example.ma.R.id  
layoutActions com.example.ma.R.id  layoutDescription com.example.ma.R.id  layoutProductCount com.example.ma.R.id  tvAmount com.example.ma.R.id  tvDate com.example.ma.R.id  
tvDescription com.example.ma.R.id  tvProductCount com.example.ma.R.id  tvSenderName com.example.ma.R.id  tvStatus com.example.ma.R.id  tvTime com.example.ma.R.id  tvTransactionType com.example.ma.R.id  tvType com.example.ma.R.id  tvUser com.example.ma.R.id  SupabaseRealtimeClient com.example.ma.data.remote  invoke ;com.example.ma.data.remote.SupabaseRealtimeClient.Companion  Any com.example.ma.ui.financial  Double com.example.ma.ui.financial  Int com.example.ma.ui.financial  List com.example.ma.ui.financial  Locale com.example.ma.ui.financial  Map com.example.ma.ui.financial  R com.example.ma.ui.financial  SimpleDateFormat com.example.ma.ui.financial  String com.example.ma.ui.financial  Unit com.example.ma.ui.financial  listOf com.example.ma.ui.financial  Any 0com.example.ma.ui.financial.TransactionsActivity  AutoCompleteTextView 0com.example.ma.ui.financial.TransactionsActivity  Bundle 0com.example.ma.ui.financial.TransactionsActivity  Locale 0com.example.ma.ui.financial.TransactionsActivity  Map 0com.example.ma.ui.financial.TransactionsActivity  MaterialButton 0com.example.ma.ui.financial.TransactionsActivity  RecyclerView 0com.example.ma.ui.financial.TransactionsActivity  SimpleDateFormat 0com.example.ma.ui.financial.TransactionsActivity  String 0com.example.ma.ui.financial.TransactionsActivity  TextInputEditText 0com.example.ma.ui.financial.TransactionsActivity  TransactionsAdapter 0com.example.ma.ui.financial.TransactionsActivity  Any /com.example.ma.ui.financial.TransactionsAdapter  Double /com.example.ma.ui.financial.TransactionsAdapter  Int /com.example.ma.ui.financial.TransactionsAdapter  List /com.example.ma.ui.financial.TransactionsAdapter  Locale /com.example.ma.ui.financial.TransactionsAdapter  Map /com.example.ma.ui.financial.TransactionsAdapter  MaterialCardView /com.example.ma.ui.financial.TransactionsAdapter  R /com.example.ma.ui.financial.TransactionsAdapter  RecyclerView /com.example.ma.ui.financial.TransactionsAdapter  SimpleDateFormat /com.example.ma.ui.financial.TransactionsAdapter  String /com.example.ma.ui.financial.TransactionsAdapter  TextView /com.example.ma.ui.financial.TransactionsAdapter  Unit /com.example.ma.ui.financial.TransactionsAdapter  View /com.example.ma.ui.financial.TransactionsAdapter  	ViewGroup /com.example.ma.ui.financial.TransactionsAdapter  	getLISTOf /com.example.ma.ui.financial.TransactionsAdapter  	getListOf /com.example.ma.ui.financial.TransactionsAdapter  listOf /com.example.ma.ui.financial.TransactionsAdapter  Any Ecom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder  Double Ecom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder  Map Ecom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder  MaterialCardView Ecom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder  R Ecom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder  String Ecom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder  TextView Ecom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder  View Ecom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder  Any com.example.ma.ui.notifications  Boolean com.example.ma.ui.notifications  Double com.example.ma.ui.notifications  Int com.example.ma.ui.notifications  List com.example.ma.ui.notifications  Map com.example.ma.ui.notifications  R com.example.ma.ui.notifications  String com.example.ma.ui.notifications  SupabaseRealtimeClient com.example.ma.ui.notifications  
mutableListOf com.example.ma.ui.notifications  Boolean 4com.example.ma.ui.notifications.NotificationActivity  Bundle 4com.example.ma.ui.notifications.NotificationActivity  Int 4com.example.ma.ui.notifications.NotificationActivity  Map 4com.example.ma.ui.notifications.NotificationActivity  NotificationAdapter 4com.example.ma.ui.notifications.NotificationActivity  RecyclerView 4com.example.ma.ui.notifications.NotificationActivity  String 4com.example.ma.ui.notifications.NotificationActivity  SupabaseRealtimeClient 4com.example.ma.ui.notifications.NotificationActivity  invoke 4com.example.ma.ui.notifications.NotificationActivity  Any 3com.example.ma.ui.notifications.NotificationAdapter  Context 3com.example.ma.ui.notifications.NotificationAdapter  Double 3com.example.ma.ui.notifications.NotificationAdapter  	ImageView 3com.example.ma.ui.notifications.NotificationAdapter  Int 3com.example.ma.ui.notifications.NotificationAdapter  List 3com.example.ma.ui.notifications.NotificationAdapter  Map 3com.example.ma.ui.notifications.NotificationAdapter  R 3com.example.ma.ui.notifications.NotificationAdapter  RecyclerView 3com.example.ma.ui.notifications.NotificationAdapter  String 3com.example.ma.ui.notifications.NotificationAdapter  TextView 3com.example.ma.ui.notifications.NotificationAdapter  View 3com.example.ma.ui.notifications.NotificationAdapter  	ViewGroup 3com.example.ma.ui.notifications.NotificationAdapter  getMUTABLEListOf 3com.example.ma.ui.notifications.NotificationAdapter  getMutableListOf 3com.example.ma.ui.notifications.NotificationAdapter  
mutableListOf 3com.example.ma.ui.notifications.NotificationAdapter  	ImageView Jcom.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder  R Jcom.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder  TextView Jcom.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder  View Jcom.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder  Int Pcom.example.ma.ui.notifications.NotificationAdapter.OnNotificationActionListener  String Pcom.example.ma.ui.notifications.NotificationAdapter.OnNotificationActionListener  Any com.example.ma.utils  Boolean com.example.ma.utils  
DatabaseSetup com.example.ma.utils  Double com.example.ma.utils  Map com.example.ma.utils  String com.example.ma.utils  Any 'com.example.ma.utils.CalculationManager  Boolean 'com.example.ma.utils.CalculationManager  Double 'com.example.ma.utils.CalculationManager  Map 'com.example.ma.utils.CalculationManager  String 'com.example.ma.utils.CalculationManager  MaterialCardView  com.google.android.material.card  Locale 	java.lang  R 	java.lang  SimpleDateFormat 	java.lang  SupabaseRealtimeClient 	java.lang  listOf 	java.lang  
mutableListOf 	java.lang  SimpleDateFormat 	java.text  Locale 	java.util  R 	java.util  SimpleDateFormat 	java.util  listOf 	java.util  
mutableListOf 	java.util  ENGLISH java.util.Locale  Locale kotlin  R kotlin  SimpleDateFormat kotlin  SupabaseRealtimeClient kotlin  listOf kotlin  
mutableListOf kotlin  Locale kotlin.annotation  R kotlin.annotation  SimpleDateFormat kotlin.annotation  SupabaseRealtimeClient kotlin.annotation  listOf kotlin.annotation  
mutableListOf kotlin.annotation  Locale kotlin.collections  MutableList kotlin.collections  R kotlin.collections  SimpleDateFormat kotlin.collections  SupabaseRealtimeClient kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  Locale kotlin.comparisons  R kotlin.comparisons  SimpleDateFormat kotlin.comparisons  SupabaseRealtimeClient kotlin.comparisons  listOf kotlin.comparisons  
mutableListOf kotlin.comparisons  Locale 	kotlin.io  R 	kotlin.io  SimpleDateFormat 	kotlin.io  SupabaseRealtimeClient 	kotlin.io  listOf 	kotlin.io  
mutableListOf 	kotlin.io  Locale 
kotlin.jvm  R 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  SupabaseRealtimeClient 
kotlin.jvm  listOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  Locale 
kotlin.ranges  R 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  SupabaseRealtimeClient 
kotlin.ranges  listOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  Locale kotlin.sequences  R kotlin.sequences  SimpleDateFormat kotlin.sequences  SupabaseRealtimeClient kotlin.sequences  listOf kotlin.sequences  
mutableListOf kotlin.sequences  Locale kotlin.text  R kotlin.text  SimpleDateFormat kotlin.text  SupabaseRealtimeClient kotlin.text  listOf kotlin.text  
mutableListOf kotlin.text  OnNotificationActionListener 6com.example.ma.ui.notifications.NotificationAdapterNew  NotificationAdapterNew com.example.ma.ui.notifications  NotificationViewHolder 6com.example.ma.ui.notifications.NotificationAdapterNew  NotificationAdapterNew android.app.Activity  NotificationAdapterNew android.content.Context  NotificationAdapterNew android.content.ContextWrapper  NotificationAdapterNew  android.view.ContextThemeWrapper  NotificationAdapterNew #androidx.activity.ComponentActivity  NotificationAdapterNew (androidx.appcompat.app.AppCompatActivity  NotificationAdapterNew #androidx.core.app.ComponentActivity  NotificationAdapterNew &androidx.fragment.app.FragmentActivity  ivProfilePicture com.example.ma.R.id  
layoutAccount com.example.ma.R.id  layoutQuantity com.example.ma.R.id  	tvAccount com.example.ma.R.id  tvAccountLabel com.example.ma.R.id  
tvQuantity com.example.ma.R.id  tvQuantityLabel com.example.ma.R.id  NotificationAdapterNew 4com.example.ma.ui.notifications.NotificationActivity  Any 6com.example.ma.ui.notifications.NotificationAdapterNew  Context 6com.example.ma.ui.notifications.NotificationAdapterNew  	ImageView 6com.example.ma.ui.notifications.NotificationAdapterNew  Int 6com.example.ma.ui.notifications.NotificationAdapterNew  List 6com.example.ma.ui.notifications.NotificationAdapterNew  Map 6com.example.ma.ui.notifications.NotificationAdapterNew  R 6com.example.ma.ui.notifications.NotificationAdapterNew  RecyclerView 6com.example.ma.ui.notifications.NotificationAdapterNew  String 6com.example.ma.ui.notifications.NotificationAdapterNew  TextView 6com.example.ma.ui.notifications.NotificationAdapterNew  View 6com.example.ma.ui.notifications.NotificationAdapterNew  	ViewGroup 6com.example.ma.ui.notifications.NotificationAdapterNew  getMUTABLEListOf 6com.example.ma.ui.notifications.NotificationAdapterNew  getMutableListOf 6com.example.ma.ui.notifications.NotificationAdapterNew  
mutableListOf 6com.example.ma.ui.notifications.NotificationAdapterNew  	ImageView Mcom.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder  R Mcom.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder  TextView Mcom.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder  View Mcom.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder  String Scom.example.ma.ui.notifications.NotificationAdapterNew.OnNotificationActionListener  empty .com.example.ma.utils.AccountBalances.Companion  AccountingEngine com.example.ma.utils  WithdrawalData com.example.ma.utils  FinancialStatus com.example.ma.utils  ExpenseData com.example.ma.utils  SaleData com.example.ma.utils  ReportsActivity com.example.ma.ui.reports  AccountBalances com.example.ma.utils  Boolean com.example.ma.ui.reports  String com.example.ma.ui.reports  Boolean )com.example.ma.ui.reports.ReportsActivity  Bundle )com.example.ma.ui.reports.ReportsActivity  String )com.example.ma.ui.reports.ReportsActivity  TextView )com.example.ma.ui.reports.ReportsActivity  Int com.example.ma.utils  List com.example.ma.utils  Long com.example.ma.utils  invoke com.example.ma.utils  AccountBalances $com.example.ma.utils.AccountBalances  Double $com.example.ma.utils.AccountBalances  empty $com.example.ma.utils.AccountBalances  invoke $com.example.ma.utils.AccountBalances  AccountBalances .com.example.ma.utils.AccountBalances.Companion  Double .com.example.ma.utils.AccountBalances.Companion  invoke .com.example.ma.utils.AccountBalances.Companion  AccountBalances %com.example.ma.utils.AccountingEngine  Boolean %com.example.ma.utils.AccountingEngine  ExpenseData %com.example.ma.utils.AccountingEngine  FinancialStatus %com.example.ma.utils.AccountingEngine  List %com.example.ma.utils.AccountingEngine  SaleData %com.example.ma.utils.AccountingEngine  String %com.example.ma.utils.AccountingEngine  WithdrawalData %com.example.ma.utils.AccountingEngine  Double  com.example.ma.utils.ExpenseData  String  com.example.ma.utils.ExpenseData  AccountBalances $com.example.ma.utils.FinancialStatus  Double $com.example.ma.utils.FinancialStatus  FinancialStatus $com.example.ma.utils.FinancialStatus  Long $com.example.ma.utils.FinancialStatus  invoke $com.example.ma.utils.FinancialStatus  AccountBalances .com.example.ma.utils.FinancialStatus.Companion  Double .com.example.ma.utils.FinancialStatus.Companion  FinancialStatus .com.example.ma.utils.FinancialStatus.Companion  Long .com.example.ma.utils.FinancialStatus.Companion  invoke .com.example.ma.utils.FinancialStatus.Companion  Double com.example.ma.utils.SaleData  Int com.example.ma.utils.SaleData  String com.example.ma.utils.SaleData  Double #com.example.ma.utils.WithdrawalData  String #com.example.ma.utils.WithdrawalData  AccountBalances 	java.lang  FinancialStatus 	java.lang  AccountBalances kotlin  FinancialStatus kotlin  AccountBalances kotlin.annotation  FinancialStatus kotlin.annotation  AccountBalances kotlin.collections  FinancialStatus kotlin.collections  AccountBalances kotlin.comparisons  FinancialStatus kotlin.comparisons  AccountBalances 	kotlin.io  FinancialStatus 	kotlin.io  AccountBalances 
kotlin.jvm  FinancialStatus 
kotlin.jvm  AccountBalances 
kotlin.ranges  FinancialStatus 
kotlin.ranges  AccountBalances kotlin.sequences  FinancialStatus kotlin.sequences  AccountBalances kotlin.text  FinancialStatus kotlin.text  tvDescriptionLabel com.example.ma.R.id                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             