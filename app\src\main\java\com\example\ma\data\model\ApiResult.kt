package com.example.ma.data.model

/**
 * Wrapper class برای نتایج API که شامل حالت‌های مختلف می‌شود
 */
sealed class ApiResult<out T> {
    
    /**
     * حالت موفقیت - داده‌ها دریافت شده
     */
    data class Success<T>(val data: T) : ApiResult<T>()
    
    /**
     * حالت خطا - خطایی رخ داده
     */
    data class Error<T>(val error: ApiError) : ApiResult<T>()
    
    /**
     * حالت بارگذاری - در حال پردازش
     */
    data class Loading<T>(val loadingState: Boolean = true) : ApiResult<T>()

    /**
     * بررسی موفقیت‌آمیز بودن نتیجه
     */
    val isSuccess: Boolean
        get() = this is Success

    /**
     * بررسی خطا داشتن نتیجه
     */
    val isError: Boolean
        get() = this is Error

    /**
     * بررسی در حال بارگذاری بودن
     */
    val isLoading: Boolean
        get() = this is Loading
    
    /**
     * دریافت داده در صورت موفقیت، در غیر این صورت null
     */
    fun getDataOrNull(): T? {
        return when (this) {
            is Success -> data
            else -> null
        }
    }
    
    /**
     * دریافت خطا در صورت وجود، در غیر این صورت null
     */
    fun getErrorOrNull(): ApiError? {
        return when (this) {
            is Error -> error
            else -> null
        }
    }
    
    /**
     * اجرای عملیات در صورت موفقیت
     */
    inline fun onSuccess(action: (T) -> Unit): ApiResult<T> {
        if (this is Success) {
            action(data)
        }
        return this
    }
    
    /**
     * اجرای عملیات در صورت خطا
     */
    inline fun onError(action: (ApiError) -> Unit): ApiResult<T> {
        if (this is Error) {
            action(error)
        }
        return this
    }
    
    /**
     * اجرای عملیات در حالت بارگذاری
     */
    inline fun onLoading(action: (Boolean) -> Unit): ApiResult<T> {
        if (this is Loading) {
            action(isLoading)
        }
        return this
    }
    
    /**
     * تبدیل نتیجه به نوع دیگر
     */
    inline fun <R> map(transform: (T) -> R): ApiResult<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Error -> Error(error)
            is Loading -> Loading(isLoading)
        }
    }
    
    /**
     * تبدیل نتیجه به نوع دیگر با امکان خطا
     */
    inline fun <R> flatMap(transform: (T) -> ApiResult<R>): ApiResult<R> {
        return when (this) {
            is Success -> transform(data)
            is Error -> Error(error)
            is Loading -> Loading(isLoading)
        }
    }
}

/**
 * Extension functions برای راحتی کار
 */

/**
 * ایجاد نتیجه موفق
 */
fun <T> T.toSuccess(): ApiResult<T> = ApiResult.Success(this)

/**
 * ایجاد نتیجه خطا
 */
fun <T> ApiError.toError(): ApiResult<T> = ApiResult.Error(this)

/**
 * ایجاد نتیجه بارگذاری
 */
fun <T> loading(): ApiResult<T> = ApiResult.Loading()

/**
 * تبدیل Exception به ApiResult
 */
fun <T> Throwable.toApiResult(): ApiResult<T> {
    val apiError = when (this) {
        is ApiError -> this
        is java.net.UnknownHostException -> ApiError.NetworkError
        is java.net.SocketTimeoutException -> ApiError.TimeoutError
        is java.net.ConnectException -> ApiError.NetworkError
        else -> ApiError.UnknownError(this)
    }
    return ApiResult.Error(apiError)
}

/**
 * اجرای safe API call
 */
suspend fun <T> safeApiCall(apiCall: suspend () -> T): ApiResult<T> {
    return try {
        ApiResult.Success(apiCall())
    } catch (e: Exception) {
        e.toApiResult()
    }
}
