package com.example.ma.data.local.entity;

/**
 * Entity برای جدول تراکنش‌ها
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b/\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0087\b\u0018\u00002\u00020\u0001B\u00a7\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\r\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0011\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0011\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0017\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0019J\t\u00104\u001a\u00020\u0003H\u00c6\u0003J\t\u00105\u001a\u00020\u0011H\u00c6\u0003J\t\u00106\u001a\u00020\u0011H\u00c6\u0003J\u000b\u00107\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u00108\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001dJ\t\u00109\u001a\u00020\u0011H\u00c6\u0003J\t\u0010:\u001a\u00020\u0017H\u00c6\u0003J\u000b\u0010;\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010<\u001a\u00020\u0003H\u00c6\u0003J\t\u0010=\u001a\u00020\u0006H\u00c6\u0003J\t\u0010>\u001a\u00020\bH\u00c6\u0003J\u0010\u0010?\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010)J\u000b\u0010@\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010A\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010B\u001a\u00020\u0003H\u00c6\u0003J\t\u0010C\u001a\u00020\u000fH\u00c6\u0003J\u00ba\u0001\u0010D\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00112\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00112\b\b\u0002\u0010\u0015\u001a\u00020\u00112\b\b\u0002\u0010\u0016\u001a\u00020\u00172\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010EJ\u0013\u0010F\u001a\u00020G2\b\u0010H\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010I\u001a\u00020\nH\u00d6\u0001J\t\u0010J\u001a\u00020\u0003H\u00d6\u0001R\u0016\u0010\u0007\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u001a\u0010\u0014\u001a\u0004\u0018\u00010\u00118\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\u001e\u001a\u0004\b\u001c\u0010\u001dR\u0018\u0010\u0013\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0018\u0010\f\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010 R\u0016\u0010\u0010\u001a\u00020\u00118\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0016\u0010\r\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010 R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010 R\u0016\u0010\u0015\u001a\u00020\u00118\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010#R\u0018\u0010\u000b\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010 R\u001a\u0010\t\u001a\u0004\u0018\u00010\n8\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010*\u001a\u0004\b(\u0010)R\u0018\u0010\u0018\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010 R\u0016\u0010\u000e\u001a\u00020\u000f8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0016\u0010\u0016\u001a\u00020\u00178\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0016\u0010\u0005\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0016\u0010\u0012\u001a\u00020\u00118\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010#R\u0016\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010 \u00a8\u0006K"}, d2 = {"Lcom/example/ma/data/local/entity/TransactionEntity;", "", "id", "", "userId", "transactionType", "Lcom/example/ma/data/local/entity/TransactionType;", "amount", "", "quantity", "", "paymentType", "category", "description", "status", "Lcom/example/ma/data/local/entity/TransactionStatus;", "createdAt", "", "updatedAt", "approvedBy", "approvedAt", "lastSync", "syncStatus", "Lcom/example/ma/data/local/entity/SyncStatus;", "remoteId", "(Ljava/lang/String;Ljava/lang/String;Lcom/example/ma/data/local/entity/TransactionType;DLjava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/example/ma/data/local/entity/TransactionStatus;JJLjava/lang/String;Ljava/lang/Long;JLcom/example/ma/data/local/entity/SyncStatus;Ljava/lang/String;)V", "getAmount", "()D", "getApprovedAt", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getApprovedBy", "()Ljava/lang/String;", "getCategory", "getCreatedAt", "()J", "getDescription", "getId", "getLastSync", "getPaymentType", "getQuantity", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getRemoteId", "getStatus", "()Lcom/example/ma/data/local/entity/TransactionStatus;", "getSyncStatus", "()Lcom/example/ma/data/local/entity/SyncStatus;", "getTransactionType", "()Lcom/example/ma/data/local/entity/TransactionType;", "getUpdatedAt", "getUserId", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/String;Lcom/example/ma/data/local/entity/TransactionType;DLjava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/example/ma/data/local/entity/TransactionStatus;JJLjava/lang/String;Ljava/lang/Long;JLcom/example/ma/data/local/entity/SyncStatus;Ljava/lang/String;)Lcom/example/ma/data/local/entity/TransactionEntity;", "equals", "", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "transactions", foreignKeys = {@androidx.room.ForeignKey(entity = com.example.ma.data.local.entity.UserEntity.class, parentColumns = {"id"}, childColumns = {"user_id"}, onDelete = 5)}, indices = {@androidx.room.Index(value = {"user_id"}), @androidx.room.Index(value = {"transaction_type"}), @androidx.room.Index(value = {"status"}), @androidx.room.Index(value = {"created_at"})})
public final class TransactionEntity {
    @androidx.room.PrimaryKey
    @androidx.room.ColumnInfo(name = "id")
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @androidx.room.ColumnInfo(name = "user_id")
    @org.jetbrains.annotations.NotNull
    private final java.lang.String userId = null;
    @androidx.room.ColumnInfo(name = "transaction_type")
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.data.local.entity.TransactionType transactionType = null;
    @androidx.room.ColumnInfo(name = "amount")
    private final double amount = 0.0;
    @androidx.room.ColumnInfo(name = "quantity")
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer quantity = null;
    @androidx.room.ColumnInfo(name = "payment_type")
    @org.jetbrains.annotations.Nullable
    private final java.lang.String paymentType = null;
    @androidx.room.ColumnInfo(name = "category")
    @org.jetbrains.annotations.Nullable
    private final java.lang.String category = null;
    @androidx.room.ColumnInfo(name = "description")
    @org.jetbrains.annotations.NotNull
    private final java.lang.String description = null;
    @androidx.room.ColumnInfo(name = "status")
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.data.local.entity.TransactionStatus status = null;
    @androidx.room.ColumnInfo(name = "created_at")
    private final long createdAt = 0L;
    @androidx.room.ColumnInfo(name = "updated_at")
    private final long updatedAt = 0L;
    @androidx.room.ColumnInfo(name = "approved_by")
    @org.jetbrains.annotations.Nullable
    private final java.lang.String approvedBy = null;
    @androidx.room.ColumnInfo(name = "approved_at")
    @org.jetbrains.annotations.Nullable
    private final java.lang.Long approvedAt = null;
    @androidx.room.ColumnInfo(name = "last_sync")
    private final long lastSync = 0L;
    @androidx.room.ColumnInfo(name = "sync_status")
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.data.local.entity.SyncStatus syncStatus = null;
    @androidx.room.ColumnInfo(name = "remote_id")
    @org.jetbrains.annotations.Nullable
    private final java.lang.String remoteId = null;
    
    public TransactionEntity(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionType transactionType, double amount, @org.jetbrains.annotations.Nullable
    java.lang.Integer quantity, @org.jetbrains.annotations.Nullable
    java.lang.String paymentType, @org.jetbrains.annotations.Nullable
    java.lang.String category, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionStatus status, long createdAt, long updatedAt, @org.jetbrains.annotations.Nullable
    java.lang.String approvedBy, @org.jetbrains.annotations.Nullable
    java.lang.Long approvedAt, long lastSync, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.SyncStatus syncStatus, @org.jetbrains.annotations.Nullable
    java.lang.String remoteId) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getUserId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionType getTransactionType() {
        return null;
    }
    
    public final double getAmount() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getQuantity() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getPaymentType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionStatus getStatus() {
        return null;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    public final long getUpdatedAt() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getApprovedBy() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long getApprovedAt() {
        return null;
    }
    
    public final long getLastSync() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.SyncStatus getSyncStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getRemoteId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    public final long component10() {
        return 0L;
    }
    
    public final long component11() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long component13() {
        return null;
    }
    
    public final long component14() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.SyncStatus component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionType component3() {
        return null;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionStatus component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionEntity copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionType transactionType, double amount, @org.jetbrains.annotations.Nullable
    java.lang.Integer quantity, @org.jetbrains.annotations.Nullable
    java.lang.String paymentType, @org.jetbrains.annotations.Nullable
    java.lang.String category, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionStatus status, long createdAt, long updatedAt, @org.jetbrains.annotations.Nullable
    java.lang.String approvedBy, @org.jetbrains.annotations.Nullable
    java.lang.Long approvedAt, long lastSync, @org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.SyncStatus syncStatus, @org.jetbrains.annotations.Nullable
    java.lang.String remoteId) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}