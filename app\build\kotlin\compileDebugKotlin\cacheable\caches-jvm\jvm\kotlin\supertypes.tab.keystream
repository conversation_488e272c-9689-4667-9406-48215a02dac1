com.example.ma.MAApplication<EMAIL>+com.example.ma.data.local.entity.SyncStatus"com.example.ma.data.model.ApiError/com.example.ma.data.model.ApiError.NetworkError6com.example.ma.data.model.ApiError.AuthenticationError.com.example.ma.data.model.ApiError.ServerError2com.example.ma.data.model.ApiError.ValidationError2com.example.ma.data.model.ApiError.PermissionError0com.example.ma.data.model.ApiError.NotFoundError0com.example.ma.data.model.ApiError.ConflictError1com.example.ma.data.model.ApiError.RateLimitError/com.example.ma.data.model.ApiError.UnknownError6com.example.ma.data.model.ApiError.DataProcessingError/com.example.ma.data.model.ApiError.TimeoutError+com.example.ma.data.model.ApiResult.Success)com.example.ma.data.model.ApiResult.Error+com.example.ma.data.model.ApiResult.Loading)com.example.ma.data.model.TransactionTypeAcom.example.ma.data.remote.SupabaseRealtimeClient.ConnectionState$com.example.ma.ui.auth.LoginActivity*com.example.ma.ui.auth.LoginResult.Success(com.example.ma.ui.auth.LoginResult.Error*com.example.ma.ui.auth.LoginResult.Loading%com.example.ma.ui.auth.LoginViewModel.com.example.ma.ui.dialogs.ThemeSelectionDialog+com.example.ma.ui.financial.ExpenseActivity-com.example.ma.ui.financial.FinancialActivity0com.example.ma.ui.financial.TransactionsActivity/com.example.ma.ui.financial.TransactionsAdapterEcom.example.ma.ui.financial.TransactionsAdapter.TransactionViewHolder.com.example.ma.ui.financial.WithdrawalActivity-com.example.ma.ui.inventory.InventoryActivity$com.example.ma.ui.main.MainViewModel$com.example.ma.ui.model.UiState.Idle'com.example.ma.ui.model.UiState.Loading'com.example.ma.ui.model.UiState.Success%com.example.ma.ui.model.UiState.Error4com.example.ma.ui.notifications.NotificationActivity3com.example.ma.ui.notifications.NotificationAdapterJcom.example.ma.ui.notifications.NotificationAdapter.NotificationViewHolder)com.example.ma.ui.profile.ProfileActivity-com.example.ma.ui.setup.DatabaseSetupActivity/com.example.ma.ui.statistics.StatisticsActivity%com.example.ma.utils.CoroutineManager-com.example.ma.utils.CurrencyInputTextWatcher(com.example.ma.utils.CurrencyTextWatcher/com.example.ma.utils.UnifiedCurrencyTextWatcher,com.example.ma.utils.SafeCurrencyTextWatcher.com.example.ma.utils.SimpleCurrencyTextWatcher6com.example.ma.databinding.ActivityNotificationBinding6com.example.ma.ui.notifications.NotificationAdapterNewMcom.example.ma.ui.notifications.NotificationAdapterNew.NotificationViewHolder5com.example.ma.databinding.ItemNotificationNewBinding)com.example.ma.ui.reports.ReportsActivity1com.example.ma.databinding.ActivityReportsBinding.com.example.ma.databinding.ActivityMainBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           