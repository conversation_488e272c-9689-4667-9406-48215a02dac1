package com.example.ma.ui.profile

import android.app.Activity
import android.Manifest
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Base64
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.canhub.cropper.CropImageContract
import com.canhub.cropper.CropImageContractOptions
import com.canhub.cropper.CropImageOptions
import com.canhub.cropper.CropImageView
import com.example.ma.R
import com.example.ma.data.model.User
import com.example.ma.data.remote.SupabaseClient

import com.example.ma.utils.CurrencyFormatter
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class ProfileActivity : AppCompatActivity() {

    private lateinit var sharedPreferences: SharedPreferences
    
    // UI Elements
    private lateinit var ivProfileImage: ImageView
    private lateinit var ivCardProfileImage: ImageView
    private lateinit var tvCardUserName: TextView

    private lateinit var tvCardPhone: TextView
    private lateinit var tvCardBalance: TextView
    private lateinit var tvCashBalance: TextView
    private lateinit var tvPartnershipShare: TextView
    private lateinit var etFullName: TextInputEditText
    private lateinit var etEmail: TextInputEditText
    private lateinit var etPhone: TextInputEditText
    private lateinit var btnSelectImage: MaterialButton
    
    // Permission Launcher
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val cameraGranted = permissions[Manifest.permission.CAMERA] ?: false

        val storageGranted = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            permissions[Manifest.permission.READ_MEDIA_IMAGES] ?: false
        } else {
            permissions[Manifest.permission.READ_EXTERNAL_STORAGE] ?: false
        }

        if (cameraGranted && storageGranted) {
            // همه اجازه‌ها داده شده، شروع crop
            startImageCropper()
        } else {
            // اجازه داده نشده
            showPermissionDeniedDialog()
        }
    }

    // Image Cropper
    private val cropImageLauncher = registerForActivityResult(CropImageContract()) { result ->
        try {
            when {
                result.isSuccessful -> {
                    result.uriContent?.let { uri ->
                        println("عکس با موفقیت crop شد: $uri")
                        handleCroppedImage(uri)
                    } ?: run {
                        Toast.makeText(this, "خطا: فایل عکس دریافت نشد", Toast.LENGTH_SHORT).show()
                    }
                }
                result.error != null -> {
                    val errorMessage = result.error?.message ?: "خطای نامشخص"
                    println("خطا در crop: $errorMessage")

                    // بررسی اینکه آیا کاربر لغو کرده یا خطای واقعی است
                    if (isCancellationError(errorMessage)) {
                        println("کاربر عملیات crop را لغو کرد")
                        Toast.makeText(this, "انتخاب عکس لغو شد", Toast.LENGTH_SHORT).show()
                    } else {
                        println("خطای واقعی در crop: $errorMessage")
                        Toast.makeText(this, "خطا در تنظیم عکس: $errorMessage", Toast.LENGTH_LONG).show()
                    }
                }
                else -> {
                    println("عملیات crop توسط کاربر لغو شد")
                    Toast.makeText(this, "انتخاب عکس لغو شد", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "خطا در پردازش نتیجه: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_profile)
        
        sharedPreferences = getSharedPreferences("user_profile", MODE_PRIVATE)
        
        setupUI()
        loadUserData()
        setupClickListeners()
    }

    private fun setupUI() {
        ivProfileImage = findViewById(R.id.ivProfileImage)
        ivCardProfileImage = findViewById(R.id.ivCardProfileImage)
        tvCardUserName = findViewById(R.id.tvCardUserName)

        tvCardPhone = findViewById(R.id.tvCardPhone)
        tvCardBalance = findViewById(R.id.tvCardBalance)
        tvCashBalance = findViewById(R.id.tvCashBalance)
        tvPartnershipShare = findViewById(R.id.tvPartnershipShare)
        etFullName = findViewById(R.id.etFullName)
        etEmail = findViewById(R.id.etEmail)
        etPhone = findViewById(R.id.etPhone)
        btnSelectImage = findViewById(R.id.btnSelectImage)
        
        // تنظیم toolbar
        findViewById<ImageView>(R.id.btnBack)?.setOnClickListener {
            finish()
        }
    }

    private fun setupClickListeners() {
        btnSelectImage.setOnClickListener {
            openImagePicker()
        }

        // تنظیم ذخیره خودکار برای فیلدها
        setupAutoSave()
    }

    private fun loadUserData() {
        // بارگذاری اطلاعات ذخیره شده
        val fullName = sharedPreferences.getString("full_name", "")
        val phone = sharedPreferences.getString("phone", "")

        etFullName.setText(fullName)
        etEmail.setText(sharedPreferences.getString("email", ""))
        etPhone.setText(phone)

        // نمایش در کارت
        tvCardUserName.text = if (fullName.isNullOrEmpty()) "" else fullName
        tvCardPhone.text = if (phone.isNullOrEmpty()) "00000 0000 0000 0000" else formatPhoneForCard(phone)

        // بارگذاری عکس پروفایل
        val imagePath = sharedPreferences.getString("profile_image", "")
        if (!imagePath.isNullOrEmpty()) {
            if (imagePath.startsWith("data:image")) {
                // عکس base64 از Supabase
                loadImageFromBase64(imagePath)
                updateImageButtonText(true)
            } else {
                // عکس محلی
                loadImageFromPath(imagePath)
                updateImageButtonText(true)
            }
        } else {
            // عکس موجود نیست، تلاش برای بازیابی از Supabase
            updateImageButtonText(false)
            tryLoadProfileImageFromSupabase()
        }

        // بررسی وضعیت تکمیل پروفایل
        checkAndLockProfile()

        // نمایش موجودی‌ها (محاسبه شده از سیستم حسابداری)
        updateBalances()
    }

    /**
     * تلاش برای بازیابی عکس پروفایل از Supabase
     */
    private fun tryLoadProfileImageFromSupabase() {
        lifecycleScope.launch {
            try {
                val currentUsername = getCurrentUsername()
                if (currentUsername != null) {
                    val profileImageFromDB = SupabaseClient.getUserProfileImage(currentUsername)
                    if (!profileImageFromDB.isNullOrEmpty()) {
                        // ذخیره عکس بازیابی شده
                        sharedPreferences.edit().putString("profile_image", profileImageFromDB).apply()

                        runOnUiThread {
                            // بارگذاری عکس
                            loadImageFromBase64(profileImageFromDB)
                            updateImageButtonText(true)
                            Toast.makeText(this@ProfileActivity, "عکس پروفایل از سرور بازیابی شد", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                println("خطا در بازیابی عکس پروفایل از Supabase: ${e.message}")
            }
        }
    }



    /**
     * تشخیص نوع خطا در crop
     */
    private fun isCancellationError(errorMessage: String): Boolean {
        val cancellationKeywords = listOf(
            "cancelled by the user",
            "canceled by the user",
            "cancelled",
            "canceled",
            "لغو شد",
            "لغو",
            "user cancelled",
            "user canceled"
        )
        return cancellationKeywords.any { keyword ->
            errorMessage.contains(keyword, ignoreCase = true)
        }
    }

    private fun openImagePicker() {
        try {
            println("شروع انتخاب عکس...")

            // بررسی اجازه‌ها
            if (checkPermissions()) {
                println("مجوزها موجود است، شروع crop...")
                startImageCropper()
            } else {
                println("مجوزها موجود نیست، درخواست مجوز...")
                requestPermissions()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "خطا در شروع انتخاب عکس: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun checkPermissions(): Boolean {
        val cameraPermission = ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)

        // بررسی permission بر اساس نسخه Android
        val storagePermission = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES)
        } else {
            ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
        }

        return cameraPermission == PackageManager.PERMISSION_GRANTED &&
               storagePermission == PackageManager.PERMISSION_GRANTED
    }

    private fun requestPermissions() {
        val permissions = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.READ_MEDIA_IMAGES
            )
        } else {
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.READ_EXTERNAL_STORAGE
            )
        }

        // بررسی اینکه آیا باید توضیح بدیم
        val shouldShowRationale = permissions.any { permission ->
            ActivityCompat.shouldShowRequestPermissionRationale(this, permission)
        }

        if (shouldShowRationale) {
            showPermissionExplanationDialog()
        } else {
            permissionLauncher.launch(permissions)
        }
    }

    private fun showPermissionExplanationDialog() {
        AlertDialog.Builder(this)
            .setTitle("اجازه دسترسی مورد نیاز")
            .setMessage("برای انتخاب و تنظیم عکس پروفایل، نیاز به دسترسی به دوربین و فایل‌ها داریم.")
            .setPositiveButton("اجازه می‌دهم") { _, _ ->
                val permissions = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                    arrayOf(
                        Manifest.permission.CAMERA,
                        Manifest.permission.READ_MEDIA_IMAGES
                    )
                } else {
                    arrayOf(
                        Manifest.permission.CAMERA,
                        Manifest.permission.READ_EXTERNAL_STORAGE
                    )
                }
                permissionLauncher.launch(permissions)
            }
            .setNegativeButton("انصراف") { dialog, _ ->
                dialog.dismiss()
                Toast.makeText(this, "بدون اجازه دسترسی، امکان تنظیم عکس وجود ندارد", Toast.LENGTH_LONG).show()
            }
            .show()
    }

    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle("اجازه دسترسی لازم است")
            .setMessage("برای استفاده از این قابلیت، لطفاً از تنظیمات اپلیکیشن، اجازه دسترسی به دوربین و فایل‌ها را فعال کنید.")
            .setPositiveButton("رفتن به تنظیمات") { _, _ ->
                val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.data = Uri.parse("package:$packageName")
                startActivity(intent)
            }
            .setNegativeButton("انصراف") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun startImageCropper() {
        try {
            // نمایش راهنمای کوتاه
            Toast.makeText(this, "عکس را انتخاب کرده و در دایره تنظیم کنید", Toast.LENGTH_SHORT).show()

            val cropOptions = CropImageOptions()

            // تنظیمات اساسی
            cropOptions.imageSourceIncludeGallery = true
            cropOptions.imageSourceIncludeCamera = true
            cropOptions.cropShape = CropImageView.CropShape.OVAL
            cropOptions.aspectRatioX = 1
            cropOptions.aspectRatioY = 1
            cropOptions.fixAspectRatio = true
            cropOptions.guidelines = CropImageView.Guidelines.ON
            cropOptions.autoZoomEnabled = true
            cropOptions.multiTouchEnabled = true
            cropOptions.allowRotation = true
            cropOptions.allowFlipping = true
            cropOptions.outputCompressFormat = Bitmap.CompressFormat.JPEG
            cropOptions.outputCompressQuality = 85
            cropOptions.activityTitle = "انتخاب و تنظیم عکس پروفایل"

            // تنظیمات بهینه برای UI های مختلف
            // بزرگترین سایز: 100dp = حدود 300px در density بالا
            cropOptions.outputRequestWidth = 600  // 2x برای کیفیت بالا
            cropOptions.outputRequestHeight = 600 // مربعی برای دایره
            cropOptions.outputRequestSizeOptions = CropImageView.RequestSizeOptions.RESIZE_INSIDE

            // تنظیمات UI crop
            cropOptions.maxZoom = 4
            cropOptions.initialCropWindowPaddingRatio = 0.1f
            cropOptions.borderLineThickness = 3f
            cropOptions.borderCornerThickness = 2f
            cropOptions.guidelinesThickness = 1f
            cropOptions.snapRadius = 3f
            cropOptions.touchRadius = 48f
            cropOptions.initialCropWindowRectangle = null
            cropOptions.allowCounterRotation = false
            cropOptions.allowFlipping = true
            cropOptions.noOutputImage = false

            val cropImageContractOptions = CropImageContractOptions(null, cropOptions)
            cropImageLauncher.launch(cropImageContractOptions)

        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "خطا در باز کردن انتخابگر عکس: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun handleCroppedImage(uri: Uri) {
        try {
            println("شروع پردازش عکس crop شده: $uri")

            // نمایش loading
            Toast.makeText(this, "در حال پردازش عکس...", Toast.LENGTH_SHORT).show()

            // بررسی وجود URI
            if (uri.toString().isEmpty()) {
                Toast.makeText(this, "خطا: آدرس فایل نامعتبر است", Toast.LENGTH_SHORT).show()
                return
            }

            val inputStream = contentResolver.openInputStream(uri)
            if (inputStream == null) {
                Toast.makeText(this, "خطا در خواندن فایل عکس - دسترسی مجاز نیست", Toast.LENGTH_SHORT).show()
                return
            }

            val croppedBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()

            if (croppedBitmap != null) {
                // بررسی سایز عکس
                println("عکس crop شده - عرض: ${croppedBitmap.width}, ارتفاع: ${croppedBitmap.height}")

                // بررسی حداقل سایز
                if (croppedBitmap.width < 50 || croppedBitmap.height < 50) {
                    Toast.makeText(this, "عکس انتخابی خیلی کوچک است", Toast.LENGTH_SHORT).show()
                    return
                }

                // ذخیره محلی
                val imagePath = saveImageToInternalStorage(croppedBitmap)
                if (imagePath.isNotEmpty()) {
                    sharedPreferences.edit().putString("profile_image", imagePath).apply()
                    println("عکس در مسیر ذخیره شد: $imagePath")

                    // نمایش عکس فوری با تنظیمات صحیح
                    runOnUiThread {
                        setProfileImageWithCorrectSettings(ivProfileImage, croppedBitmap)
                        setProfileImageWithCorrectSettings(ivCardProfileImage, croppedBitmap)

                        // تغییر متن دکمه به ویرایش
                        updateImageButtonText(true)

                        Toast.makeText(this@ProfileActivity, "عکس با موفقیت ذخیره شد", Toast.LENGTH_SHORT).show()
                    }

                    // آپلود به دیتابیس و ارسال اعلان در background
                    lifecycleScope.launch {
                        try {
                            val uploadSuccess = uploadImageToDatabase(croppedBitmap)

                            // ارسال اعلان به کاربر دیگه
                            if (uploadSuccess) {
                                sendProfileImageChangeNotification(croppedBitmap)
                            }

                            runOnUiThread {
                                if (uploadSuccess) {
                                    Toast.makeText(this@ProfileActivity, "عکس با سرور همگام‌سازی شد", Toast.LENGTH_SHORT).show()
                                } else {
                                    Toast.makeText(this@ProfileActivity, "خطا در همگام‌سازی با سرور", Toast.LENGTH_SHORT).show()
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            runOnUiThread {
                                Toast.makeText(this@ProfileActivity, "خطا در آپلود: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }

                    // اطلاع به MainActivity که عکس تغییر کرده
                    setResult(RESULT_OK)
                } else {
                    Toast.makeText(this, "خطا در ذخیره عکس در حافظه داخلی", Toast.LENGTH_SHORT).show()
                }
            } else {
                Toast.makeText(this, "خطا در تبدیل فایل به عکس - فرمت نامعتبر", Toast.LENGTH_SHORT).show()
            }

        } catch (e: SecurityException) {
            e.printStackTrace()
            Toast.makeText(this, "خطا: عدم دسترسی به فایل - مجوز لازم است", Toast.LENGTH_LONG).show()
        } catch (e: OutOfMemoryError) {
            e.printStackTrace()
            Toast.makeText(this, "خطا: عکس خیلی بزرگ است", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "خطا در پردازش عکس: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun saveImageToInternalStorage(bitmap: Bitmap): String {
        return try {
            // ایجاد پوشه اختصاصی برای عکس‌ها
            val imageDir = File(filesDir, "images")
            if (!imageDir.exists()) {
                imageDir.mkdirs()
            }

            // ایجاد نام فایل یکتا
            val timestamp = System.currentTimeMillis()
            val file = File(imageDir, "profile_image_$timestamp.jpg")

            // حذف عکس قبلی اگر وجود داشته باشد
            val oldImagePath = sharedPreferences.getString("profile_image", "")
            if (!oldImagePath.isNullOrEmpty()) {
                val oldFile = File(oldImagePath)
                if (oldFile.exists() && oldFile != file) {
                    oldFile.delete()
                    println("عکس قبلی حذف شد: $oldImagePath")
                }
            }

            val outputStream = FileOutputStream(file)
            val compressed = bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            outputStream.flush()
            outputStream.close()

            if (compressed) {
                println("عکس با موفقیت ذخیره شد: ${file.absolutePath}")
                println("سایز فایل: ${file.length()} bytes")
                file.absolutePath
            } else {
                println("خطا در فشرده‌سازی عکس")
                ""
            }
        } catch (e: IOException) {
            e.printStackTrace()
            println("خطا در نوشتن فایل: ${e.message}")
            ""
        } catch (e: Exception) {
            e.printStackTrace()
            println("خطای عمومی در ذخیره عکس: ${e.message}")
            ""
        }
    }

    /**
     * تنظیم صحیح عکس پروفایل در ImageView
     * این تابع اطمینان می‌دهد که عکس با تنظیمات مناسب نمایش داده شود
     */
    private fun setProfileImageWithCorrectSettings(imageView: ImageView, bitmap: Bitmap) {
        try {
            // تنظیمات اساسی برای نمایش عکس
            imageView.setImageBitmap(bitmap)
            imageView.setPadding(0, 0, 0, 0)
            imageView.clearColorFilter()
            imageView.scaleType = ImageView.ScaleType.CENTER_CROP

            // حذف tint اگر وجود داشته باشد
            imageView.imageTintList = null

            println("عکس پروفایل با تنظیمات صحیح اعمال شد - سایز: ${bitmap.width}x${bitmap.height}")
        } catch (e: Exception) {
            e.printStackTrace()
            println("خطا در تنظیم عکس پروفایل: ${e.message}")
        }
    }

    private fun loadImageFromPath(imagePath: String) {
        try {
            val file = java.io.File(imagePath)
            if (file.exists()) {
                val bitmap = BitmapFactory.decodeFile(imagePath)
                if (bitmap != null) {
                    setProfileImageWithCorrectSettings(ivProfileImage, bitmap)
                    setProfileImageWithCorrectSettings(ivCardProfileImage, bitmap)
                } else {
                    // فایل خراب است، حذف کن
                    file.delete()
                    sharedPreferences.edit().remove("profile_image").apply()
                    resetToDefaultImage()
                }
            } else {
                // فایل وجود نداره، پاک کن از SharedPreferences
                sharedPreferences.edit().remove("profile_image").apply()
                resetToDefaultImage()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            resetToDefaultImage()
        }
    }

    private fun resetToDefaultImage() {
        ivProfileImage.setImageResource(R.drawable.ic_person_modern)
        ivProfileImage.setPadding(20, 20, 20, 20)
        ivProfileImage.setColorFilter(getColor(R.color.primary_color))
        ivProfileImage.scaleType = ImageView.ScaleType.CENTER_INSIDE

        ivCardProfileImage.setImageResource(R.drawable.ic_person_modern)
        ivCardProfileImage.setPadding(10, 10, 10, 10)
        ivCardProfileImage.setColorFilter(getColor(R.color.primary_color))
        ivCardProfileImage.scaleType = ImageView.ScaleType.CENTER_INSIDE

        updateImageButtonText(false) // دکمه رو به حالت "انتخاب عکس" برگردون
    }

    private fun loadImageFromBase64(base64String: String) {
        try {
            // حذف prefix اگر وجود داره
            val cleanBase64 = base64String.replace("data:image/jpeg;base64,", "")
                .replace("data:image/png;base64,", "")

            val decodedBytes = android.util.Base64.decode(cleanBase64, android.util.Base64.DEFAULT)
            val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)

            if (bitmap != null) {
                setProfileImageWithCorrectSettings(ivProfileImage, bitmap)
                setProfileImageWithCorrectSettings(ivCardProfileImage, bitmap)
            } else {
                resetToDefaultImage()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            resetToDefaultImage()
        }
    }

    private fun resizeBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        val scaleWidth = maxWidth.toFloat() / width
        val scaleHeight = maxHeight.toFloat() / height
        val scale = minOf(scaleWidth, scaleHeight)
        
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    private fun formatPhoneForCard(phone: String): String {
        if (phone.length == 11 && phone.startsWith("09")) {
            // تبدیل 09123456789 به 6789 2345 0091 00000 (از چپ به راست)
            return "${phone.substring(7)} ${phone.substring(4, 7)} ${phone.substring(0, 4)} 00000"
        }
        return "00000 0000 0000 0000"
    }

    private fun setupAutoSave() {
        // ذخیره خودکار نام
        etFullName.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                saveFieldData("full_name", etFullName.text.toString().trim())
            }
        }

        // ذخیره خودکار ایمیل
        etEmail.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                saveFieldData("email", etEmail.text.toString().trim())
            }
        }

        // ذخیره خودکار تلفن
        etPhone.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                saveFieldData("phone", etPhone.text.toString().trim())
            }
        }
    }

    private fun checkAndLockProfile() {
        val isProfileCompleted = sharedPreferences.getBoolean("profile_completed", false)
        if (isProfileCompleted) {
            lockAllFields()
        }
    }

    private fun lockAllFields() {
        // غیرفعال کردن فیلدها
        etFullName.isEnabled = false
        etEmail.isEnabled = false
        etPhone.isEnabled = false

        // تغییر ظاهر فیلدها
        etFullName.alpha = 0.7f
        etEmail.alpha = 0.7f
        etPhone.alpha = 0.7f

        // تغییر رنگ پس‌زمینه
        etFullName.setBackgroundColor(getColor(R.color.background_secondary))
        etEmail.setBackgroundColor(getColor(R.color.background_secondary))
        etPhone.setBackgroundColor(getColor(R.color.background_secondary))
    }

    private fun saveFieldData(fieldName: String, value: String) {
        if (value.isNotEmpty()) {
            // ذخیره محلی
            sharedPreferences.edit().apply {
                putString(fieldName, value)
                putBoolean("profile_completed", true) // علامت‌گذاری تکمیل
                apply()
            }

            // بروزرسانی نمایش کارت
            when (fieldName) {
                "full_name" -> tvCardUserName.text = value
                "phone" -> tvCardPhone.text = formatPhoneForCard(value)
            }

            // آپدیت در دیتابیس
            lifecycleScope.launch {
                updateFieldInDatabase(fieldName, value)
            }

            // قفل کردن فیلدها بعد از اولین ذخیره
            lockAllFields()
        }
    }

    private suspend fun updateFieldInDatabase(fieldName: String, value: String) {
        try {
            val currentUsername = getCurrentUsername()
            if (currentUsername != null) {
                withContext(Dispatchers.IO) {
                    SupabaseClient.updateUserField(currentUsername, fieldName, value)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateImageButtonText(hasImage: Boolean) {
        if (hasImage) {
            btnSelectImage.text = "ویرایش و تنظیم عکس"
            btnSelectImage.setIconResource(R.drawable.ic_edit)
        } else {
            btnSelectImage.text = "انتخاب و تنظیم عکس"
            btnSelectImage.setIconResource(R.drawable.ic_camera)
        }
    }



    private suspend fun uploadImageToDatabase(bitmap: Bitmap): Boolean {
        return try {
            // تبدیل bitmap به base64
            val base64Image = bitmapToBase64(bitmap)

            // دریافت نام کاربری فعلی
            val currentUsername = getCurrentUsername()

            if (currentUsername != null) {
                // آپدیت در دیتابیس در background thread
                val success = withContext(Dispatchers.IO) {
                    SupabaseClient.updateUserProfileImage(currentUsername, base64Image)
                }
                println("Upload result: $success")
                success
            } else {
                println("Username is null")
                false
            }

        } catch (e: Exception) {
            e.printStackTrace()
            println("Upload exception: ${e.message}")
            false
        }
    }

    private fun bitmapToBase64(bitmap: Bitmap): String {
        return try {
            val byteArrayOutputStream = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, 70, byteArrayOutputStream)
            val byteArray = byteArrayOutputStream.toByteArray()
            byteArrayOutputStream.close()
            Base64.encodeToString(byteArray, Base64.NO_WRAP)
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    private fun getCurrentUsername(): String? {
        return try {
            val authPrefs = getSharedPreferences("auth_prefs", MODE_PRIVATE)
            val userId = authPrefs.getString("current_user_id", null)

            // پیدا کردن username بر اساس ID
            when (userId) {
                "ad28ba8f-0fa0-4420-8119-70fcacfd237e" -> "Alikakai"
                "930b5d13-0408-4c57-965b-235c5532b35a" -> "Miladnasiri"
                else -> {
                    // اگر ID پیدا نشد، سعی کن از نام کاربری محلی استفاده کنی
                    val profilePrefs = getSharedPreferences("user_profile", MODE_PRIVATE)
                    val fullName = profilePrefs.getString("full_name", "")
                    when {
                        fullName?.contains("علی") == true -> "Alikakai"
                        fullName?.contains("میلاد") == true -> "Miladnasiri"
                        else -> null
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    private suspend fun sendProfileImageChangeNotification(bitmap: Bitmap) {
        try {
            val currentUsername = getCurrentUsername()
            val currentUser = getCurrentUser()

            if (currentUsername != null && currentUser != null) {
                // پیدا کردن شریک دیگه
                val otherUser = if (currentUsername == "Alikakai") {
                    User.getAllUsers().find { it.username == "Miladnasiri" }
                } else {
                    User.getAllUsers().find { it.username == "Alikakai" }
                }

                if (otherUser != null) {
                    // تبدیل bitmap به base64
                    val base64Image = bitmapToBase64(bitmap)

                    // ارسال اعلان تغییر عکس پروفایل با اطلاعات کامل
                    val notificationData = mapOf(
                        "notification_type" to "profile_image_changed",
                        "from_user_id" to currentUser.id,
                        "from_user_name" to currentUser.displayName,
                        "from_user_profile_image" to "data:image/jpeg;base64,$base64Image",
                        "to_user_id" to otherUser.id,
                        "message" to "${currentUser.displayName} عکس پروفایل خود را تغییر داد",
                        "timestamp" to System.currentTimeMillis(),
                        "is_read" to false
                    )

                    val success = withContext(Dispatchers.IO) {
                        SupabaseClient.insertNotification(
                            fromUserId = currentUsername,
                            toUserId = if (currentUsername == "miladnasiri") "alikakai" else "miladnasiri",
                            type = "profile_update",
                            data = notificationData
                        )
                    }
                    println("Profile image change notification sent: $success")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            println("Error sending profile image change notification: ${e.message}")
        }
    }

    private fun getCurrentUser(): User? {
        val currentUsername = getCurrentUsername()
        return User.getAllUsers().find { it.username == currentUsername }
    }

    private fun updateBalances() {
        // TODO: این مقادیر باید از سیستم حسابداری محاسبه شوند
        // فعلاً مقادیر نمونه
        val cardBalance = 2500000.0
        val cashBalance = 1800000.0
        val partnershipShare = 3200000.0

        tvCardBalance.text = CurrencyFormatter.formatToPersianToman(cardBalance)
        tvCashBalance.text = CurrencyFormatter.formatToPersianToman(cashBalance)
        tvPartnershipShare.text = CurrencyFormatter.formatToPersianToman(partnershipShare)
    }
}
