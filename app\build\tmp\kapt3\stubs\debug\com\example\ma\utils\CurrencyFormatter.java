package com.example.ma.utils;

/**
 * کلاس کمکی برای فرمت کردن قیمت‌ها به صورت حرفه‌ای با تبدیل به حروف
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\u0010\b\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u000bJ\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\fJ\u000e\u0010\r\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u000e\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u000f\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u0010\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u0018\u0010\u0011\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u0012\u001a\u00020\u0013J\u000e\u0010\u0014\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u0014\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u000bJ\u000e\u0010\u0014\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\fJ\u000e\u0010\u0015\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u0015\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u000bJ\u000e\u0010\u0015\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\fJ\u000e\u0010\u0016\u001a\u00020\b2\u0006\u0010\u0017\u001a\u00020\fJ\u000e\u0010\u0018\u001a\u00020\b2\u0006\u0010\u0019\u001a\u00020\bJ\u000e\u0010\u001a\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u001a\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u000bJ\u000e\u0010\u001a\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001b"}, d2 = {"Lcom/example/ma/utils/CurrencyFormatter;", "", "()V", "decimalFormat", "Ljava/text/DecimalFormat;", "persianLocale", "Ljava/util/Locale;", "format", "", "amount", "", "", "", "formatNumber", "formatToPersianToman", "formatToRial", "formatToToman", "formatWithSign", "showPositiveSign", "", "formatWithWords", "formatWithoutUnit", "numberToWords", "number", "toPersianNumbers", "input", "toWordsOnly", "app_debug"})
public final class CurrencyFormatter {
    @org.jetbrains.annotations.NotNull
    private static final java.util.Locale persianLocale = null;
    @org.jetbrains.annotations.NotNull
    private static final java.text.DecimalFormat decimalFormat = null;
    @org.jetbrains.annotations.NotNull
    public static final com.example.ma.utils.CurrencyFormatter INSTANCE = null;
    
    private CurrencyFormatter() {
        super();
    }
    
    /**
     * فرمت کردن مبلغ به تومان
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatToToman(double amount) {
        return null;
    }
    
    /**
     * فرمت کردن مبلغ به ریال
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatToRial(double amount) {
        return null;
    }
    
    /**
     * فرمت کردن مبلغ بدون واحد
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatNumber(double amount) {
        return null;
    }
    
    /**
     * تبدیل عدد به حروف فارسی
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String numberToWords(long number) {
        return null;
    }
    
    /**
     * فرمت کامل با عدد، جداکننده و حروف
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatWithWords(long amount) {
        return null;
    }
    
    /**
     * فرمت کامل با عدد، جداکننده و حروف
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatWithWords(double amount) {
        return null;
    }
    
    /**
     * فرمت کامل با عدد، جداکننده و حروف
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatWithWords(int amount) {
        return null;
    }
    
    /**
     * فقط حروف بدون عدد
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String toWordsOnly(long amount) {
        return null;
    }
    
    /**
     * فقط حروف بدون عدد
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String toWordsOnly(double amount) {
        return null;
    }
    
    /**
     * فقط حروف بدون عدد
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String toWordsOnly(int amount) {
        return null;
    }
    
    /**
     * فرمت کردن مبلغ با علامت مثبت/منفی
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatWithSign(double amount, boolean showPositiveSign) {
        return null;
    }
    
    /**
     * تبدیل اعداد انگلیسی به فارسی
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String toPersianNumbers(@org.jetbrains.annotations.NotNull
    java.lang.String input) {
        return null;
    }
    
    /**
     * فرمت کامل با اعداد فارسی
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatToPersianToman(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String format(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String format(long amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String format(int amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatWithoutUnit(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatWithoutUnit(long amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatWithoutUnit(int amount) {
        return null;
    }
}