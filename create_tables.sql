-- ایجاد جداول برای سیستم اعلانات حسابداری شراکتی

-- حذف جداول قدیمی اگر وجود دارند
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;

-- جدول اعلانات
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    from_user_id VARCHAR(50) NOT NULL,
    to_user_id VARCHAR(50) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
    amount DECIMAL(15,2) NOT NULL,
    product_count INTEGER DEFAULT NULL,
    description TEXT DEFAULT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

-- جدول تراکنش‌های تایید شده
CREATE TABLE transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    notification_id UUID REFERENCES notifications(id),
    user_id VARCHAR(50) NOT NULL,
    partner_id VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('sale', 'purchase', 'withdrawal', 'expense', 'deposit')),
    amount DECIMAL(15,2) NOT NULL,
    product_count INTEGER DEFAULT NULL,
    description TEXT DEFAULT NULL,
    approved_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ایندکس‌ها برای بهبود عملکرد
CREATE INDEX idx_notifications_to_user ON notifications(to_user_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_type ON notifications(transaction_type);
CREATE INDEX idx_notifications_created ON notifications(created_at DESC);
CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_transactions_type ON transactions(type);

-- تریگر برای بروزرسانی updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- اضافه کردن داده‌های نمونه برای تست
INSERT INTO notifications (from_user_id, to_user_id, transaction_type, amount, description) VALUES
('miladnasiri', 'alikakai', 'sale', 2500000, 'فروش محصولات ماه جاری'),
('alikakai', 'miladnasiri', 'withdrawal', 1000000, 'برداشت شخصی'),
('miladnasiri', 'alikakai', 'expense', 500000, 'هزینه اجاره مغازه'),
('alikakai', 'miladnasiri', 'purchase', 1500000, 'خرید کالا از تامین کننده');

-- نمایش جداول ایجاد شده
SELECT 'notifications table created' as message;
SELECT 'transactions table created' as message;
SELECT 'Sample data inserted' as message;
