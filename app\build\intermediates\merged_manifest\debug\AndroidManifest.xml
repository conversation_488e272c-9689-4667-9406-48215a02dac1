<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.ma"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <queries>
        <intent>
            <action android:name="android.intent.action.GET_CONTENT" />

            <category android:name="android.intent.category.OPENABLE" />

            <data android:mimeType="*/*" />
        </intent>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
    </queries>

    <permission
        android:name="com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.example.ma.MAApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.MA" >
        <activity
            android:name="com.example.ma.ui.auth.LoginActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.ma.MainActivity"
            android:exported="false" />
        <activity
            android:name="com.example.ma.ui.profile.ProfileActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />
        <activity
            android:name="com.example.ma.ui.notifications.NotificationActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />
        <activity
            android:name="com.example.ma.ui.reports.ReportsActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <!-- صفحات مالی -->
        <activity
            android:name="com.example.ma.ui.financial.FinancialActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />
        <activity
            android:name="com.example.ma.ui.financial.ExpenseActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />
        <activity
            android:name="com.example.ma.ui.financial.WithdrawalActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />
        <activity
            android:name="com.example.ma.ui.financial.TransactionsActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />
        <activity
            android:name="com.example.ma.ui.statistics.StatisticsActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />
        <activity
            android:name="com.example.ma.ui.inventory.InventoryActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />
        <activity
            android:name="com.example.ma.ui.setup.DatabaseSetupActivity"
            android:exported="false"
            android:theme="@style/Theme.MA" />

        <!-- Image Cropper Activity -->
        <activity
            android:name="com.canhub.cropper.CropImageActivity"
            android:exported="true"
            android:theme="@style/Base.Theme.AppCompat" />

        <provider
            android:name="com.canhub.cropper.CropFileProvider"
            android:authorities="com.example.ma.cropper.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/library_file_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.ma.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>