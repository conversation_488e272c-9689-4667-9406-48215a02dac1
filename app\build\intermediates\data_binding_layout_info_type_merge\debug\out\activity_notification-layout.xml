<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_notification" modulePackage="com.example.ma" filePath="app\src\main\res\layout\activity_notification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_notification_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="261" endOffset="14"/></Target><Target id="@+id/btnBack" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="22" startOffset="8" endLine="40" endOffset="59"/></Target><Target id="@+id/tvNewNotifications" view="TextView"><Expressions/><location startLine="110" startOffset="24" endLine="118" endOffset="68"/></Target><Target id="@+id/tvPendingApprovals" view="TextView"><Expressions/><location startLine="149" startOffset="24" endLine="157" endOffset="68"/></Target><Target id="@+id/chipReceived" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="182" startOffset="16" endLine="193" endOffset="74"/></Target><Target id="@+id/chipSent" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="195" startOffset="16" endLine="206" endOffset="74"/></Target><Target id="@+id/rvNotifications" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="211" startOffset="12" endLine="218" endOffset="60"/></Target><Target id="@+id/layoutEmpty" view="LinearLayout"><Expressions/><location startLine="221" startOffset="12" endLine="255" endOffset="26"/></Target></Targets></Layout>