package com.example.ma.data.model;

/**
 * Wrapper class برای نتایج API که شامل حالت‌های مختلف می‌شود
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u00012\u00020\u0002:\u0003\u0017\u0018\u0019B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0003J2\u0010\t\u001a\b\u0012\u0004\u0012\u0002H\n0\u0000\"\u0004\b\u0001\u0010\n2\u0018\u0010\u000b\u001a\u0014\u0012\u0004\u0012\u00028\u0000\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\n0\u00000\fH\u0086\b\u00f8\u0001\u0000J\r\u0010\r\u001a\u0004\u0018\u00018\u0000\u00a2\u0006\u0002\u0010\u000eJ\b\u0010\u000f\u001a\u0004\u0018\u00010\u0010J,\u0010\u0011\u001a\b\u0012\u0004\u0012\u0002H\n0\u0000\"\u0004\b\u0001\u0010\n2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u0002H\n0\fH\u0086\b\u00f8\u0001\u0000J&\u0010\u0012\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00140\fH\u0086\b\u00f8\u0001\u0000J&\u0010\u0015\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00140\fH\u0086\b\u00f8\u0001\u0000J&\u0010\u0016\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00020\u00140\fH\u0086\b\u00f8\u0001\u0000R\u0011\u0010\u0004\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\u0004\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\u0007\u0010\u0006R\u0011\u0010\b\u001a\u00020\u00058F\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\u0006\u0082\u0001\u0003\u001a\u001b\u001c\u0082\u0002\u0007\n\u0005\b\u009920\u0001\u00a8\u0006\u001d"}, d2 = {"Lcom/example/ma/data/model/ApiResult;", "T", "", "()V", "isError", "", "()Z", "isLoading", "isSuccess", "flatMap", "R", "transform", "Lkotlin/Function1;", "getDataOrNull", "()Ljava/lang/Object;", "getErrorOrNull", "Lcom/example/ma/data/model/ApiError;", "map", "onError", "action", "", "onLoading", "onSuccess", "Error", "Loading", "Success", "Lcom/example/ma/data/model/ApiResult$Error;", "Lcom/example/ma/data/model/ApiResult$Loading;", "Lcom/example/ma/data/model/ApiResult$Success;", "app_debug"})
public abstract class ApiResult<T extends java.lang.Object> {
    
    private ApiResult() {
        super();
    }
    
    public final boolean isSuccess() {
        return false;
    }
    
    public final boolean isError() {
        return false;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    /**
     * دریافت داده در صورت موفقیت، در غیر این صورت null
     */
    @org.jetbrains.annotations.Nullable
    public final T getDataOrNull() {
        return null;
    }
    
    /**
     * دریافت خطا در صورت وجود، در غیر این صورت null
     */
    @org.jetbrains.annotations.Nullable
    public final com.example.ma.data.model.ApiError getErrorOrNull() {
        return null;
    }
    
    /**
     * اجرای عملیات در صورت موفقیت
     */
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.model.ApiResult<T> onSuccess(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super T, kotlin.Unit> action) {
        return null;
    }
    
    /**
     * اجرای عملیات در صورت خطا
     */
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.model.ApiResult<T> onError(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.example.ma.data.model.ApiError, kotlin.Unit> action) {
        return null;
    }
    
    /**
     * اجرای عملیات در حالت بارگذاری
     */
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.model.ApiResult<T> onLoading(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> action) {
        return null;
    }
    
    /**
     * تبدیل نتیجه به نوع دیگر
     */
    @org.jetbrains.annotations.NotNull
    public final <R extends java.lang.Object>com.example.ma.data.model.ApiResult<R> map(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super T, ? extends R> transform) {
        return null;
    }
    
    /**
     * تبدیل نتیجه به نوع دیگر با امکان خطا
     */
    @org.jetbrains.annotations.NotNull
    public final <R extends java.lang.Object>com.example.ma.data.model.ApiResult<R> flatMap(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super T, ? extends com.example.ma.data.model.ApiResult<? extends R>> transform) {
        return null;
    }
    
    /**
     * حالت خطا - خطایی رخ داده
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u0000*\u0004\b\u0001\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\t\u0010\b\u001a\u00020\u0004H\u00c6\u0003J\u0019\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00010\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\rH\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/example/ma/data/model/ApiResult$Error;", "T", "Lcom/example/ma/data/model/ApiResult;", "error", "Lcom/example/ma/data/model/ApiError;", "(Lcom/example/ma/data/model/ApiError;)V", "getError", "()Lcom/example/ma/data/model/ApiError;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class Error<T extends java.lang.Object> extends com.example.ma.data.model.ApiResult<T> {
        @org.jetbrains.annotations.NotNull
        private final com.example.ma.data.model.ApiError error = null;
        
        public Error(@org.jetbrains.annotations.NotNull
        com.example.ma.data.model.ApiError error) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError getError() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiError component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiResult.Error<T> copy(@org.jetbrains.annotations.NotNull
        com.example.ma.data.model.ApiError error) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * حالت بارگذاری - در حال پردازش
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u0000*\u0004\b\u0001\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B\u000f\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\t\u0010\b\u001a\u00020\u0004H\u00c6\u0003J\u0019\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00010\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u00042\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0011"}, d2 = {"Lcom/example/ma/data/model/ApiResult$Loading;", "T", "Lcom/example/ma/data/model/ApiResult;", "loadingState", "", "(Z)V", "getLoadingState", "()Z", "component1", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class Loading<T extends java.lang.Object> extends com.example.ma.data.model.ApiResult<T> {
        private final boolean loadingState = false;
        
        public Loading(boolean loadingState) {
        }
        
        public final boolean getLoadingState() {
            return false;
        }
        
        public Loading() {
        }
        
        public final boolean component1() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiResult.Loading<T> copy(boolean loadingState) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * حالت موفقیت - داده‌ها دریافت شده
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u0000*\u0004\b\u0001\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00028\u0001\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\b\u001a\u00028\u0001H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00010\u00002\b\b\u0002\u0010\u0003\u001a\u00028\u0001H\u00c6\u0001\u00a2\u0006\u0002\u0010\nJ\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001R\u0013\u0010\u0003\u001a\u00028\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0013"}, d2 = {"Lcom/example/ma/data/model/ApiResult$Success;", "T", "Lcom/example/ma/data/model/ApiResult;", "data", "(Ljava/lang/Object;)V", "getData", "()Ljava/lang/Object;", "Ljava/lang/Object;", "component1", "copy", "(Ljava/lang/Object;)Lcom/example/ma/data/model/ApiResult$Success;", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class Success<T extends java.lang.Object> extends com.example.ma.data.model.ApiResult<T> {
        private final T data = null;
        
        public Success(T data) {
        }
        
        public final T getData() {
            return null;
        }
        
        public final T component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.ma.data.model.ApiResult.Success<T> copy(T data) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
}