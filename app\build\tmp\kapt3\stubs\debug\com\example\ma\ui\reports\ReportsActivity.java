package com.example.ma.ui.reports;

/**
 * صفحه گزارشات مالی کامل
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0014\u001a\u00020\u0015H\u0002J\b\u0010\u0016\u001a\u00020\u0015H\u0002J\u0012\u0010\u0017\u001a\u00020\u00152\b\u0010\u0018\u001a\u0004\u0018\u00010\u0019H\u0014J\b\u0010\u001a\u001a\u00020\u001bH\u0016J\b\u0010\u001c\u001a\u00020\u0015H\u0002J\u0010\u0010\u001d\u001a\u00020\u00152\u0006\u0010\u001e\u001a\u00020\u001fH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/example/ma/ui/reports/ReportsActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "tvAliCardBalance", "Landroid/widget/TextView;", "tvAliCashBalance", "tvAliFinalBalance", "tvAliPersonalBalance", "tvAliShare", "tvAliWithdrawals", "tvLastCalculated", "tvMiladCardBalance", "tvMiladCashBalance", "tvMiladFinalBalance", "tvMiladPersonalBalance", "tvMiladShare", "tvMiladWithdrawals", "tvNetProfit", "tvTotalExpenses", "tvTotalSales", "initViews", "", "loadFinancialReports", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onSupportNavigateUp", "", "setupToolbar", "showErrorMessage", "message", "", "app_debug"})
public final class ReportsActivity extends androidx.appcompat.app.AppCompatActivity {
    private android.widget.TextView tvTotalSales;
    private android.widget.TextView tvTotalExpenses;
    private android.widget.TextView tvNetProfit;
    private android.widget.TextView tvMiladShare;
    private android.widget.TextView tvAliShare;
    private android.widget.TextView tvMiladWithdrawals;
    private android.widget.TextView tvAliWithdrawals;
    private android.widget.TextView tvMiladFinalBalance;
    private android.widget.TextView tvAliFinalBalance;
    private android.widget.TextView tvMiladCashBalance;
    private android.widget.TextView tvMiladCardBalance;
    private android.widget.TextView tvMiladPersonalBalance;
    private android.widget.TextView tvAliCashBalance;
    private android.widget.TextView tvAliCardBalance;
    private android.widget.TextView tvAliPersonalBalance;
    private android.widget.TextView tvLastCalculated;
    
    public ReportsActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews() {
    }
    
    private final void setupToolbar() {
    }
    
    private final void loadFinancialReports() {
    }
    
    private final void showErrorMessage(java.lang.String message) {
    }
    
    @java.lang.Override
    public boolean onSupportNavigateUp() {
        return false;
    }
}