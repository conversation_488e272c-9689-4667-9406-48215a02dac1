<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:background="@color/primary_color"
            android:elevation="4dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="مدیریت انبار"
            app:titleTextColor="@color/text_white" />

        <!-- موجودی فعلی -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/info_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📦"
                    android:textSize="32sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="موجودی فعلی انبار"
                    android:textSize="16sp"
                    android:textColor="@color/text_white"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/tvCurrentStock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0 عدد"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_white"
                    android:fontFamily="sans-serif-medium"
                    tools:text="20 عدد" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- فرم تغییر موجودی -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- عنوان -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝 تغییر موجودی"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="20dp"
                    android:fontFamily="sans-serif-medium" />

                <!-- نوع تغییر -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🔄 نوع عملیات:"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp"
                    android:fontFamily="sans-serif-medium" />

                <RadioGroup
                    android:id="@+id/rgChangeType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rbIncrease"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="➕ افزایش موجودی"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:padding="8dp"
                        android:fontFamily="sans-serif" />

                    <RadioButton
                        android:id="@+id/rbDecrease"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="➖ کاهش موجودی"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:padding="8dp"
                        android:fontFamily="sans-serif" />

                </RadioGroup>

                <!-- تعداد -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:hint="تعداد"
                    app:startIconDrawable="@drawable/ic_inventory"
                    app:startIconTint="@color/primary_color"
                    app:suffixText="عدد"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etQuantity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif"
                        tools:text="5" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- توضیحات -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:hint="توضیحات"
                    app:startIconDrawable="@drawable/ic_description"
                    app:startIconTint="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etDescription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="2"
                        android:maxLines="4"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif"
                        tools:text="ورود کالای جدید" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- دکمه ثبت -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSubmit"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="ثبت تغییر موجودی"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:backgroundTint="@color/warning_color"
                    app:cornerRadius="12dp"
                    app:icon="@drawable/ic_inventory"
                    app:iconGravity="textStart"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- راهنما -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📌 راهنمای استفاده"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• افزایش موجودی: برای ورود کالای جدید\n• کاهش موجودی: برای فروش یا خروج کالا\n• تغییرات نیاز به تایید شریک دارند\n• موجودی پس از تایید بروزرسانی می‌شود"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:lineSpacingExtra="4dp"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
