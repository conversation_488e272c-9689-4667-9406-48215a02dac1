package com.example.ma.utils

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Bitmap
import android.graphics.BitmapFactory

/**
 * کلاس مدیریت اطلاعات پروفایل کاربر
 */
class ProfileManager(private val context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("user_profile", Context.MODE_PRIVATE)
    
    /**
     * بررسی اینکه آیا پروفایل تکمیل شده یا نه
     */
    fun isProfileCompleted(): Boolean {
        return sharedPreferences.getBoolean("profile_completed", false)
    }
    
    /**
     * دریافت نام کامل کاربر
     */
    fun getFullName(): String {
        return sharedPreferences.getString("full_name", "") ?: ""
    }
    
    /**
     * دریافت ایمیل کاربر
     */
    fun getEmail(): String {
        return sharedPreferences.getString("email", "") ?: ""
    }
    
    /**
     * دریافت شماره تماس کاربر
     */
    fun getPhone(): String {
        return sharedPreferences.getString("phone", "") ?: ""
    }
    

    
    /**
     * دریافت عکس پروفایل کاربر
     */
    fun getProfileImage(): Bitmap? {
        val imagePath = sharedPreferences.getString("profile_image", "")
        return if (!imagePath.isNullOrEmpty()) {
            try {
                if (imagePath.startsWith("data:image")) {
                    // عکس base64 از Supabase
                    val cleanBase64 = imagePath.replace("data:image/jpeg;base64,", "")
                        .replace("data:image/png;base64,", "")
                    val decodedBytes = android.util.Base64.decode(cleanBase64, android.util.Base64.DEFAULT)
                    BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
                } else {
                    // عکس محلی
                    val file = java.io.File(imagePath)
                    if (file.exists()) {
                        BitmapFactory.decodeFile(imagePath)
                    } else {
                        // فایل وجود نداره، تلاش برای بازیابی از Supabase
                        sharedPreferences.edit().remove("profile_image").apply()
                        tryLoadFromSupabase()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        } else {
            // اگر هیچ عکسی ذخیره نشده، تلاش برای بازیابی از Supabase
            tryLoadFromSupabase()
        }
    }

    /**
     * تلاش برای بازیابی عکس از Supabase
     */
    private fun tryLoadFromSupabase(): Bitmap? {
        return try {
            // این متد باید در background thread اجرا شه
            // فعلاً null برمی‌گردونیم
            // در آینده با coroutine بازیابی می‌کنیم
            null
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * دریافت شناسه کاربر فعلی
     */
    private fun getCurrentUserId(): String? {
        val authPrefs = context.getSharedPreferences("auth_prefs", android.content.Context.MODE_PRIVATE)
        return authPrefs.getString("current_user_id", null)
    }
    
    /**
     * دریافت مسیر عکس پروفایل
     */
    fun getProfileImagePath(): String {
        return sharedPreferences.getString("profile_image", "") ?: ""
    }
    

    
    /**
     * دریافت نام کوتاه برای نمایش
     */
    fun getDisplayName(): String {
        val fullName = getFullName()
        return if (fullName.isNotEmpty()) {
            fullName
        } else {
            "کاربر گرامی"
        }
    }
    
    /**
     * ذخیره عکس پروفایل (محلی یا base64)
     */
    fun saveProfileImage(imagePath: String) {
        sharedPreferences.edit().putString("profile_image", imagePath).apply()
    }

    /**
     * بررسی اینکه آیا عکس پروفایل موجود است
     */
    fun hasProfileImage(): Boolean {
        val imagePath = sharedPreferences.getString("profile_image", "")
        return !imagePath.isNullOrEmpty()
    }

    /**
     * دریافت نوع عکس پروفایل (محلی یا آنلاین)
     */
    fun getProfileImageType(): String {
        val imagePath = sharedPreferences.getString("profile_image", "")
        return when {
            imagePath.isNullOrEmpty() -> "none"
            imagePath.startsWith("data:image") -> "base64"
            else -> "local"
        }
    }

    /**
     * بازیابی عکس پروفایل از Supabase (async)
     */
    suspend fun loadProfileImageFromSupabase(): Boolean {
        return try {
            val currentUsername = getCurrentUsername()
            if (currentUsername != null) {
                println("🔍 ProfileManager: تلاش برای بازیابی عکس پروفایل برای $currentUsername")
                val profileImageFromDB = com.example.ma.data.remote.SupabaseClient.getUserProfileImage(currentUsername)
                if (!profileImageFromDB.isNullOrEmpty()) {
                    sharedPreferences.edit().putString("profile_image", profileImageFromDB).apply()
                    println("✅ عکس پروفایل از Supabase بازیابی شد: ${profileImageFromDB.length} کاراکتر")
                    return true
                } else {
                    println("❌ عکس پروفایل در Supabase پیدا نشد برای $currentUsername")
                }
            } else {
                println("❌ Username فعلی پیدا نشد")
            }
            false
        } catch (e: Exception) {
            e.printStackTrace()
            println("❌ خطا در بازیابی عکس پروفایل: ${e.message}")
            false
        }
    }

    /**
     * دریافت نام کاربری فعلی
     */
    private fun getCurrentUsername(): String? {
        val authPrefs = context.getSharedPreferences("auth_prefs", android.content.Context.MODE_PRIVATE)
        return authPrefs.getString("current_username", null)
    }

    /**
     * پاک کردن تمام اطلاعات پروفایل
     */
    fun clearProfile() {
        sharedPreferences.edit().clear().apply()
    }
}
