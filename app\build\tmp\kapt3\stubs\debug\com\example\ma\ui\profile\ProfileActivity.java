package com.example.ma.ui.profile;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0017\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u001b\u001a\u00020\u00122\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\b\u0010\u001e\u001a\u00020\u001fH\u0002J\b\u0010 \u001a\u00020!H\u0002J\u0010\u0010\"\u001a\u00020\u00122\u0006\u0010#\u001a\u00020\u0012H\u0002J\n\u0010$\u001a\u0004\u0018\u00010%H\u0002J\n\u0010&\u001a\u0004\u0018\u00010\u0012H\u0002J\u0010\u0010\'\u001a\u00020\u001f2\u0006\u0010(\u001a\u00020)H\u0002J\u0010\u0010*\u001a\u00020!2\u0006\u0010+\u001a\u00020\u0012H\u0002J\u0010\u0010,\u001a\u00020\u001f2\u0006\u0010-\u001a\u00020\u0012H\u0002J\u0010\u0010.\u001a\u00020\u001f2\u0006\u0010/\u001a\u00020\u0012H\u0002J\b\u00100\u001a\u00020\u001fH\u0002J\b\u00101\u001a\u00020\u001fH\u0002J\u0012\u00102\u001a\u00020\u001f2\b\u00103\u001a\u0004\u0018\u000104H\u0014J\b\u00105\u001a\u00020\u001fH\u0002J\b\u00106\u001a\u00020\u001fH\u0002J\b\u00107\u001a\u00020\u001fH\u0002J \u00108\u001a\u00020\u001d2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u00109\u001a\u00020:2\u0006\u0010;\u001a\u00020:H\u0002J\u0018\u0010<\u001a\u00020\u001f2\u0006\u0010=\u001a\u00020\u00122\u0006\u0010>\u001a\u00020\u0012H\u0002J\u0010\u0010?\u001a\u00020\u00122\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\u0019\u0010@\u001a\u00020\u001f2\u0006\u0010\u001c\u001a\u00020\u001dH\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010AJ\u0018\u0010B\u001a\u00020\u001f2\u0006\u0010C\u001a\u00020\u000e2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\b\u0010D\u001a\u00020\u001fH\u0002J\b\u0010E\u001a\u00020\u001fH\u0002J\b\u0010F\u001a\u00020\u001fH\u0002J\b\u0010G\u001a\u00020\u001fH\u0002J\b\u0010H\u001a\u00020\u001fH\u0002J\b\u0010I\u001a\u00020\u001fH\u0002J\b\u0010J\u001a\u00020\u001fH\u0002J\b\u0010K\u001a\u00020\u001fH\u0002J!\u0010L\u001a\u00020\u001f2\u0006\u0010=\u001a\u00020\u00122\u0006\u0010>\u001a\u00020\u0012H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010MJ\u0010\u0010N\u001a\u00020\u001f2\u0006\u0010O\u001a\u00020!H\u0002J\u0019\u0010P\u001a\u00020!2\u0006\u0010\u001c\u001a\u00020\u001dH\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010AR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0005\u001a\u0010\u0012\f\u0012\n \b*\u0004\u0018\u00010\u00070\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00110\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006Q"}, d2 = {"Lcom/example/ma/ui/profile/ProfileActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "btnSelectImage", "Lcom/google/android/material/button/MaterialButton;", "cropImageLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Lcom/canhub/cropper/CropImageContractOptions;", "kotlin.jvm.PlatformType", "etEmail", "Lcom/google/android/material/textfield/TextInputEditText;", "etFullName", "etPhone", "ivCardProfileImage", "Landroid/widget/ImageView;", "ivProfileImage", "permissionLauncher", "", "", "sharedPreferences", "Landroid/content/SharedPreferences;", "tvCardBalance", "Landroid/widget/TextView;", "tvCardPhone", "tvCardUserName", "tvCashBalance", "tvPartnershipShare", "bitmapToBase64", "bitmap", "Landroid/graphics/Bitmap;", "checkAndLockProfile", "", "checkPermissions", "", "formatPhoneForCard", "phone", "getCurrentUser", "Lcom/example/ma/data/model/User;", "getCurrentUsername", "handleCroppedImage", "uri", "Landroid/net/Uri;", "isCancellationError", "errorMessage", "loadImageFromBase64", "base64String", "loadImageFromPath", "imagePath", "loadUserData", "lockAllFields", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "openImagePicker", "requestPermissions", "resetToDefaultImage", "resizeBitmap", "maxWidth", "", "maxHeight", "saveFieldData", "fieldName", "value", "saveImageToInternalStorage", "sendProfileImageChangeNotification", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setProfileImageWithCorrectSettings", "imageView", "setupAutoSave", "setupClickListeners", "setupUI", "showPermissionDeniedDialog", "showPermissionExplanationDialog", "startImageCropper", "tryLoadProfileImageFromSupabase", "updateBalances", "updateFieldInDatabase", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateImageButtonText", "hasImage", "uploadImageToDatabase", "app_debug"})
public final class ProfileActivity extends androidx.appcompat.app.AppCompatActivity {
    private android.content.SharedPreferences sharedPreferences;
    private android.widget.ImageView ivProfileImage;
    private android.widget.ImageView ivCardProfileImage;
    private android.widget.TextView tvCardUserName;
    private android.widget.TextView tvCardPhone;
    private android.widget.TextView tvCardBalance;
    private android.widget.TextView tvCashBalance;
    private android.widget.TextView tvPartnershipShare;
    private com.google.android.material.textfield.TextInputEditText etFullName;
    private com.google.android.material.textfield.TextInputEditText etEmail;
    private com.google.android.material.textfield.TextInputEditText etPhone;
    private com.google.android.material.button.MaterialButton btnSelectImage;
    @org.jetbrains.annotations.NotNull
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String[]> permissionLauncher = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.activity.result.ActivityResultLauncher<com.canhub.cropper.CropImageContractOptions> cropImageLauncher = null;
    
    public ProfileActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void loadUserData() {
    }
    
    /**
     * تلاش برای بازیابی عکس پروفایل از Supabase
     */
    private final void tryLoadProfileImageFromSupabase() {
    }
    
    /**
     * تشخیص نوع خطا در crop
     */
    private final boolean isCancellationError(java.lang.String errorMessage) {
        return false;
    }
    
    private final void openImagePicker() {
    }
    
    private final boolean checkPermissions() {
        return false;
    }
    
    private final void requestPermissions() {
    }
    
    private final void showPermissionExplanationDialog() {
    }
    
    private final void showPermissionDeniedDialog() {
    }
    
    private final void startImageCropper() {
    }
    
    private final void handleCroppedImage(android.net.Uri uri) {
    }
    
    private final java.lang.String saveImageToInternalStorage(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * تنظیم صحیح عکس پروفایل در ImageView
     * این تابع اطمینان می‌دهد که عکس با تنظیمات مناسب نمایش داده شود
     */
    private final void setProfileImageWithCorrectSettings(android.widget.ImageView imageView, android.graphics.Bitmap bitmap) {
    }
    
    private final void loadImageFromPath(java.lang.String imagePath) {
    }
    
    private final void resetToDefaultImage() {
    }
    
    private final void loadImageFromBase64(java.lang.String base64String) {
    }
    
    private final android.graphics.Bitmap resizeBitmap(android.graphics.Bitmap bitmap, int maxWidth, int maxHeight) {
        return null;
    }
    
    private final java.lang.String formatPhoneForCard(java.lang.String phone) {
        return null;
    }
    
    private final void setupAutoSave() {
    }
    
    private final void checkAndLockProfile() {
    }
    
    private final void lockAllFields() {
    }
    
    private final void saveFieldData(java.lang.String fieldName, java.lang.String value) {
    }
    
    private final java.lang.Object updateFieldInDatabase(java.lang.String fieldName, java.lang.String value, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final void updateImageButtonText(boolean hasImage) {
    }
    
    private final java.lang.Object uploadImageToDatabase(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    private final java.lang.String bitmapToBase64(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    private final java.lang.String getCurrentUsername() {
        return null;
    }
    
    private final java.lang.Object sendProfileImageChangeNotification(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final com.example.ma.data.model.User getCurrentUser() {
        return null;
    }
    
    private final void updateBalances() {
    }
}