# ✅ رفع خطای Duplicate Import

## مشکل:
```
خط 45-46: Conflicting import, imported name 'IOException' is ambiguous
```

## علت:
`IOException` دو بار import شده بود:
```kotlin
import java.io.IOException  // خط 36
import java.io.IOException  // خط 37 - تکراری!
```

## راه‌حل:
حذف import تکراری:
```kotlin
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException  // فقط یک بار
```

## نتیجه:
✅ خطای compilation حل شد
✅ پروژه آماده build است
✅ عکس پروفایل آماده تست است
