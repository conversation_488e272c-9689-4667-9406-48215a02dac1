package com.example.ma.data.local;

/**
 * Type Converters برای Room
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\bH\u0007J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\nH\u0007J\u0010\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\rH\u0007J\u0010\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0007J\u0010\u0010\u000f\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0004H\u0007J\u0010\u0010\u0010\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0004H\u0007J\u0010\u0010\u0011\u001a\u00020\r2\u0006\u0010\f\u001a\u00020\u0004H\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/example/ma/data/local/Converters;", "", "()V", "fromNotificationStatus", "", "status", "Lcom/example/ma/data/local/entity/NotificationStatus;", "fromSyncStatus", "Lcom/example/ma/data/local/entity/SyncStatus;", "fromTransactionStatus", "Lcom/example/ma/data/local/entity/TransactionStatus;", "fromTransactionType", "type", "Lcom/example/ma/data/local/entity/TransactionType;", "toNotificationStatus", "toSyncStatus", "toTransactionStatus", "toTransactionType", "app_debug"})
public final class Converters {
    
    public Converters() {
        super();
    }
    
    @androidx.room.TypeConverter
    @org.jetbrains.annotations.NotNull
    public final java.lang.String fromSyncStatus(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.SyncStatus status) {
        return null;
    }
    
    @androidx.room.TypeConverter
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.SyncStatus toSyncStatus(@org.jetbrains.annotations.NotNull
    java.lang.String status) {
        return null;
    }
    
    @androidx.room.TypeConverter
    @org.jetbrains.annotations.NotNull
    public final java.lang.String fromTransactionType(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionType type) {
        return null;
    }
    
    @androidx.room.TypeConverter
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionType toTransactionType(@org.jetbrains.annotations.NotNull
    java.lang.String type) {
        return null;
    }
    
    @androidx.room.TypeConverter
    @org.jetbrains.annotations.NotNull
    public final java.lang.String fromTransactionStatus(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.TransactionStatus status) {
        return null;
    }
    
    @androidx.room.TypeConverter
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.TransactionStatus toTransactionStatus(@org.jetbrains.annotations.NotNull
    java.lang.String status) {
        return null;
    }
    
    @androidx.room.TypeConverter
    @org.jetbrains.annotations.NotNull
    public final java.lang.String fromNotificationStatus(@org.jetbrains.annotations.NotNull
    com.example.ma.data.local.entity.NotificationStatus status) {
        return null;
    }
    
    @androidx.room.TypeConverter
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.data.local.entity.NotificationStatus toNotificationStatus(@org.jetbrains.annotations.NotNull
    java.lang.String status) {
        return null;
    }
}