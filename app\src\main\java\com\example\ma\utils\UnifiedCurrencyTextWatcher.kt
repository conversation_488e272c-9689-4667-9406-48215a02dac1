package com.example.ma.utils

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import android.widget.TextView

/**
 * TextWatcher یکپارچه برای فرمت کردن ورودی و نمایش به حروف
 * این کلاس همه کارهای مربوط به currency formatting را انجام می‌دهد
 */
class UnifiedCurrencyTextWatcher(
    private val editText: EditText,
    private val displayTextView: TextView? = null,
    private val showWithWords: Boolean = true,
    private val onAmountChanged: ((String) -> Unit)? = null
) : TextWatcher {

    private var isFormatting = false

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

    override fun afterTextChanged(s: Editable?) {
        if (isFormatting) return
        if (s == null) return

        isFormatting = true

        try {
            val currentText = s.toString()
            val cleanInput = currentText.replace(",", "").replace("٬", "").trim()

            // اگر ورودی خالی است
            if (cleanInput.isEmpty()) {
                // پاک کردن امن
                if (s.isNotEmpty()) {
                    s.clear()
                }
                updateDisplayText("")
                onAmountChanged?.invoke("")
                return
            }

            // فقط اعداد مجاز هستند
            val digitsOnly = cleanInput.filter { it.isDigit() }
            
            if (digitsOnly.isEmpty()) {
                // اگر هیچ عددی نیست، پاک کن
                s.clear()
                updateDisplayText("")
                onAmountChanged?.invoke("")
                return
            }

            // اگر کاراکتر غیرعددی وجود دارد، حذف کن
            if (digitsOnly != cleanInput) {
                // استفاده از روش امن‌تر برای جایگزینی
                val currentSelection = editText.selectionStart
                s.clear()
                if (digitsOnly.isNotEmpty()) {
                    s.append(digitsOnly)
                }
                editText.post {
                    try {
                        val newPosition = minOf(currentSelection, digitsOnly.length)
                        editText.setSelection(newPosition)
                    } catch (e: Exception) {
                        // نادیده بگیر
                    }
                }
                updateDisplayText(digitsOnly)
                onAmountChanged?.invoke(digitsOnly)
                return
            }

            // فرمت کردن عدد
            try {
                val number = digitsOnly.toLong()
                val formatted = CurrencyFormatter.formatWithoutUnit(number)

                // اگر فرمت تغییر کرده، اعمال کن
                if (currentText != formatted) {
                    val cursorPosition = editText.selectionStart

                    // جایگزینی محتوا با روش امن
                    s.clear()
                    s.append(formatted)

                    // محاسبه موقعیت cursor
                    val newPosition = calculateCursorPosition(
                        cursorPosition,
                        currentText,
                        formatted
                    )

                    // تنظیم cursor
                    editText.post {
                        try {
                            val safePosition = minOf(newPosition, formatted.length)
                            if (safePosition >= 0 && safePosition <= editText.text?.length ?: 0) {
                                editText.setSelection(safePosition)
                            }
                        } catch (e: Exception) {
                            try {
                                editText.setSelection(editText.text?.length ?: 0)
                            } catch (e2: Exception) {
                                // نادیده بگیر
                            }
                        }
                    }
                }

                // نمایش به حروف
                updateDisplayText(digitsOnly)
                
                // اطلاع‌رسانی تغییر مبلغ
                onAmountChanged?.invoke(digitsOnly)

            } catch (e: NumberFormatException) {
                // عدد خیلی بزرگ است
                updateDisplayText(digitsOnly)
                onAmountChanged?.invoke(digitsOnly)
            }

        } catch (e: Exception) {
            // در صورت خطای غیرمنتظره
        } finally {
            isFormatting = false
        }
    }

    /**
     * محاسبه موقعیت صحیح cursor
     */
    private fun calculateCursorPosition(
        currentPosition: Int,
        oldText: String,
        newText: String
    ): Int {
        // اگر cursor در انتها بود، در انتها نگه دار
        if (currentPosition >= oldText.length) {
            return newText.length
        }

        // محاسبه تعداد کاماهای اضافه شده
        val oldCommaCount = oldText.take(currentPosition).count { it == ',' }
        val newCommaCount = newText.take(currentPosition + (newText.length - oldText.length)).count { it == ',' }
        val commaDiff = newCommaCount - oldCommaCount

        return (currentPosition + commaDiff).coerceIn(0, newText.length)
    }

    /**
     * نمایش مبلغ به حروف
     */
    private fun updateDisplayText(cleanInput: String) {
        displayTextView?.let { textView ->
            try {
                if (cleanInput.isEmpty()) {
                    textView.text = ""
                    textView.visibility = android.view.View.GONE
                    return
                }

                val amount = cleanInput.toLong()
                val displayText = if (showWithWords) {
                    CurrencyFormatter.toWordsOnly(amount)
                } else {
                    CurrencyFormatter.formatToToman(amount.toDouble())
                }

                textView.text = displayText
                textView.visibility = android.view.View.VISIBLE

            } catch (e: Exception) {
                textView.text = ""
                textView.visibility = android.view.View.GONE
            }
        }
    }
}
