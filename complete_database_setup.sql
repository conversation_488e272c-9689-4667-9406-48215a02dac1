-- ایجاد جداول کامل برای سیستم حسابداری شراکتی

-- حذف جداول قدیمی اگر وجود دارند
DROP TABLE IF EXISTS account_balances CASCADE;
DROP TABLE IF EXISTS inventory CASCADE;
DROP TABLE IF EXISTS withdrawals CASCADE;
DROP TABLE IF EXISTS expenses CASCADE;
DROP TABLE IF EXISTS sales CASCADE;

-- 1. جدول فروش‌ها
CREATE TABLE sales (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    payment_type VARCHAR(20) CHECK (payment_type IN ('cash', 'card')),
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. جدول هزینه‌ها
CREATE TABLE expenses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('business', 'shared_personal')),
    category VARCHAR(50),
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. جدول برداشت‌ها
CREATE TABLE withdrawals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    withdrawal_type VARCHAR(20) CHECK (withdrawal_type IN ('card', 'cash')),
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. جدول انبار
CREATE TABLE inventory (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    change_type VARCHAR(20) CHECK (change_type IN ('increase', 'decrease')),
    quantity INTEGER NOT NULL,
    current_stock INTEGER NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. جدول موجودی حساب‌ها
CREATE TABLE account_balances (
    user_id VARCHAR(50) PRIMARY KEY,
    company_balance DECIMAL(15,2) DEFAULT 0,
    personal_card_balance DECIMAL(15,2) DEFAULT 0,
    personal_cash_balance DECIMAL(15,2) DEFAULT 0,
    profit_share DECIMAL(15,2) DEFAULT 0,
    total_sales DECIMAL(15,2) DEFAULT 0,
    total_expenses_paid DECIMAL(15,2) DEFAULT 0,
    total_withdrawals DECIMAL(15,2) DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ایندکس‌ها برای بهبود عملکرد
CREATE INDEX idx_sales_user ON sales(user_id);
CREATE INDEX idx_sales_status ON sales(status);
CREATE INDEX idx_sales_created ON sales(created_at DESC);

CREATE INDEX idx_expenses_user ON expenses(user_id);
CREATE INDEX idx_expenses_type ON expenses(type);
CREATE INDEX idx_expenses_status ON expenses(status);

CREATE INDEX idx_withdrawals_user ON withdrawals(user_id);
CREATE INDEX idx_withdrawals_status ON withdrawals(status);

CREATE INDEX idx_inventory_user ON inventory(user_id);
CREATE INDEX idx_inventory_status ON inventory(status);

-- تریگرهای بروزرسانی updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sales_updated_at BEFORE UPDATE ON sales FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_expenses_updated_at BEFORE UPDATE ON expenses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_withdrawals_updated_at BEFORE UPDATE ON withdrawals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_account_balances_updated_at BEFORE UPDATE ON account_balances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- اضافه کردن کاربران به جدول موجودی
INSERT INTO account_balances (user_id) VALUES 
('miladnasiri'),
('alikakai')
ON CONFLICT (user_id) DO NOTHING;

-- تنظیم موجودی اولیه انبار
INSERT INTO inventory (user_id, change_type, quantity, current_stock, description, status) VALUES
('miladnasiri', 'increase', 20, 20, 'موجودی اولیه انبار', 'approved');

-- داده‌های نمونه برای تست
INSERT INTO sales (user_id, amount, quantity, payment_type, description, status) VALUES
('miladnasiri', 500000, 2, 'cash', 'فروش نقدی محصول', 'approved'),
('alikakai', 750000, 3, 'card', 'فروش کارتی محصول', 'pending');

INSERT INTO expenses (user_id, amount, type, category, description, status) VALUES
('miladnasiri', 200000, 'business', 'مواد اولیه', 'خرید مواد اولیه', 'approved'),
('alikakai', 150000, 'shared_personal', 'غذا', 'شام مشترک', 'pending');

INSERT INTO withdrawals (user_id, amount, withdrawal_type, description, status) VALUES
('miladnasiri', 100000, 'cash', 'برداشت شخصی', 'pending');

-- نمایش جداول ایجاد شده
SELECT 'Database setup completed successfully' as message;
