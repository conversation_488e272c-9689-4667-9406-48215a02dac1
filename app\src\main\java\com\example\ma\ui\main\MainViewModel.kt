package com.example.ma.ui.main

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.ma.data.model.Transaction
import com.example.ma.data.model.TransactionType
import com.example.ma.data.repository.TransactionRepository
import kotlinx.coroutines.launch
import java.util.Date

/**
 * ViewModel برای صفحه اصلی اپلیکیشن
 * این کلاس منطق کسب‌وکار مربوط به فروش و آمارها را مدیریت می‌کند
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val transactionRepository = TransactionRepository()
    
    private val _transactions = MutableLiveData<List<Transaction>>()
    val transactions: LiveData<List<Transaction>> = _transactions
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    /**
     * ثبت تراکنش فروش جدید
     * @param amount مبلغ کل فروش
     * @param description توضیحات تراکنش
     * @param userId شناسه کاربر فروشنده
     * @param bottleCount تعداد بطری فروخته شده
     * @param paymentType نوع پرداخت (نقدی/کارت)
     * @param receiver دریافت کننده پول
     * @return true در صورت موفقیت
     */
    suspend fun registerSaleTransaction(
        amount: Double,
        description: String,
        userId: String,
        bottleCount: Int,
        paymentType: String,
        receiver: String
    ): Boolean {
        println("🔍 MainViewModel.registerSaleTransaction: شروع")
        println("🔍 MainViewModel.registerSaleTransaction: userId = $userId")
        println("🔍 MainViewModel.registerSaleTransaction: amount = $amount")

        _isLoading.value = true

        return try {
            // ابتدا فروش را ثبت کن
            val saleId = UUID.randomUUID().toString()
            val saleData = mapOf(
                "id" to saleId,
                "user_id" to userId,
                "amount" to amount,
                "quantity" to bottleCount,
                "payment_type" to paymentType,
                "description" to description,
                "status" to "pending"
            )

            println("🔍 MainViewModel.registerSaleTransaction: ثبت فروش در جدول sales")
            val saleSuccess = SupabaseClient.insertSale(saleData)
            println("🔍 MainViewModel.registerSaleTransaction: insertSale result = $saleSuccess")

            if (!saleSuccess) {
                _errorMessage.value = "خطا در ثبت فروش"
                return@try false
            }

            // سپس تراکنش مرتبط را ثبت کن
            val transaction = Transaction(
                id = "sale_${saleId}",
                userId = userId,
                type = "sale",
                amount = amount,
                description = description,
                category = paymentType,
                status = "pending",
                createdAt = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", java.util.Locale.ENGLISH).format(java.util.Date())
            )

            println("🔍 MainViewModel.registerSaleTransaction: ثبت تراکنش در دیتابیس")
            val success = transactionRepository.insertTransaction(transaction)
            println("🔍 MainViewModel.registerSaleTransaction: insertTransaction result = $success")

            if (success) {
                // ارسال اعلان به شریک
                try {
                    val partnerUserId = if (userId == "Alikakai") "Miladnasiri" else "Alikakai"
                    val notificationSent = com.example.ma.data.remote.SupabaseClient.createNotification(
                        fromUserId = userId,
                        toUserId = partnerUserId,
                        transactionType = "sale",
                        amount = amount,
                        description = "درخواست تایید فروش: $description"
                    )

                    if (notificationSent) {
                        println("✅ اعلان فروش ارسال شد به $partnerUserId")
                    } else {
                        println("❌ خطا در ارسال اعلان فروش")
                    }
                } catch (e: Exception) {
                    println("❌ خطا در ارسال اعلان: ${e.message}")
                    e.printStackTrace()
                }

                // بروزرسانی لیست تراکنش‌ها
                loadTransactions(userId)
                _errorMessage.value = null
            } else {
                _errorMessage.value = "خطا در ثبت تراکنش"
            }
            
            success
        } catch (e: Exception) {
            _errorMessage.value = "خطا در اتصال به سرور: ${e.message}"
            false
        } finally {
            _isLoading.value = false
        }
    }
    
    /**
     * بارگذاری تراکنش‌های یک کاربر
     * @param userId شناسه کاربر
     */
    fun loadTransactions(userId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val userTransactions = transactionRepository.getTransactionsByUser(userId)
                _transactions.value = userTransactions
                _errorMessage.value = null
            } catch (e: Exception) {
                _errorMessage.value = "خطا در بارگذاری تراکنش‌ها: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * بارگذاری همه تراکنش‌ها
     */
    fun loadAllTransactions() {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val allTransactions = transactionRepository.getAllTransactions()
                _transactions.value = allTransactions
                _errorMessage.value = null
            } catch (e: Exception) {
                _errorMessage.value = "خطا در بارگذاری تراکنش‌ها: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * محاسبه آمارهای مالی
     * @param transactions لیست تراکنش‌ها
     * @return آمارهای محاسبه شده
     */
    fun calculateFinancialStats(transactions: List<Transaction>): FinancialStats {
        val approvedTransactions = transactions.filter { it.status == "approved" }
        
        val totalSales = approvedTransactions
            .filter { it.type == "sale" }
            .sumOf { it.amount }
        
        val totalExpenses = approvedTransactions
            .filter { it.type == "expense" }
            .sumOf { it.amount }
        
        val totalWithdrawals = approvedTransactions
            .filter { it.type == "withdrawal" }
            .sumOf { it.amount }
        
        val netProfit = totalSales - totalExpenses - totalWithdrawals
        
        return FinancialStats(
            totalSales = totalSales,
            totalExpenses = totalExpenses,
            totalWithdrawals = totalWithdrawals,
            netProfit = netProfit,
            transactionCount = approvedTransactions.size
        )
    }
}

/**
 * کلاس داده برای آمارهای مالی
 */
data class FinancialStats(
    val totalSales: Double,
    val totalExpenses: Double,
    val totalWithdrawals: Double,
    val netProfit: Double,
    val transactionCount: Int
)
