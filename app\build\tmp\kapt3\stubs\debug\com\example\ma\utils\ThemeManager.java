package com.example.ma.utils;

/**
 * مدیریت تم‌های اپلیکیشن (روشن/تیره)
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rJ\u000e\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u0007J\u000e\u0010\u0010\u001a\u00020\u00072\u0006\u0010\f\u001a\u00020\rJ\u0016\u0010\u0011\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u0007J\u000e\u0010\u0012\u001a\u00020\u00132\u0006\u0010\f\u001a\u00020\rJ\u0016\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/example/ma/utils/ThemeManager;", "", "()V", "KEY_THEME_MODE", "", "PREFS_NAME", "THEME_DARK", "", "THEME_LIGHT", "THEME_SYSTEM", "applySavedTheme", "", "context", "Landroid/content/Context;", "applyTheme", "themeMode", "getCurrentTheme", "getThemeName", "isDarkTheme", "", "setTheme", "app_debug"})
public final class ThemeManager {
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String PREFS_NAME = "theme_prefs";
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String KEY_THEME_MODE = "theme_mode";
    public static final int THEME_LIGHT = 0;
    public static final int THEME_DARK = 1;
    public static final int THEME_SYSTEM = 2;
    @org.jetbrains.annotations.NotNull
    public static final com.example.ma.utils.ThemeManager INSTANCE = null;
    
    private ThemeManager() {
        super();
    }
    
    /**
     * دریافت تم فعلی
     */
    public final int getCurrentTheme(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return 0;
    }
    
    /**
     * تنظیم تم جدید
     */
    public final void setTheme(@org.jetbrains.annotations.NotNull
    android.content.Context context, int themeMode) {
    }
    
    /**
     * اعمال تم
     */
    public final void applyTheme(int themeMode) {
    }
    
    /**
     * اعمال تم ذخیره شده
     */
    public final void applySavedTheme(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
    }
    
    /**
     * دریافت نام تم
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getThemeName(@org.jetbrains.annotations.NotNull
    android.content.Context context, int themeMode) {
        return null;
    }
    
    /**
     * بررسی اینکه آیا تم تیره فعال است
     */
    public final boolean isDarkTheme(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return false;
    }
}