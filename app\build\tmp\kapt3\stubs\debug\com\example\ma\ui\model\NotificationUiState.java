package com.example.ma.ui.model;

/**
 * UI State برای صفحه اعلانات
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BU\u0012 \b\u0002\u0010\u0002\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00050\u00040\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u0012\b\b\u0002\u0010\n\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\fJ!\u0010\u0014\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00050\u00040\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\bH\u00c6\u0003J\u000f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0006H\u00c6\u0003JY\u0010\u0019\u001a\u00020\u00002 \b\u0002\u0010\u0002\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00050\u00040\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\b\b\u0002\u0010\n\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\b2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0006H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0010R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R)\u0010\u0002\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00050\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012\u00a8\u0006\u001f"}, d2 = {"Lcom/example/ma/ui/model/NotificationUiState;", "", "notifications", "Lcom/example/ma/ui/model/UiState;", "", "", "", "isProcessingNotification", "", "lastActionResult", "currentFilter", "currentStatus", "(Lcom/example/ma/ui/model/UiState;ZLcom/example/ma/ui/model/UiState;Ljava/lang/String;Ljava/lang/String;)V", "getCurrentFilter", "()Ljava/lang/String;", "getCurrentStatus", "()Z", "getLastActionResult", "()Lcom/example/ma/ui/model/UiState;", "getNotifications", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class NotificationUiState {
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.ui.model.UiState<java.util.List<java.util.Map<java.lang.String, java.lang.Object>>> notifications = null;
    private final boolean isProcessingNotification = false;
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.ui.model.UiState<java.lang.Boolean> lastActionResult = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String currentFilter = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String currentStatus = null;
    
    public NotificationUiState(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> notifications, boolean isProcessingNotification, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<java.lang.Boolean> lastActionResult, @org.jetbrains.annotations.NotNull
    java.lang.String currentFilter, @org.jetbrains.annotations.NotNull
    java.lang.String currentStatus) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.List<java.util.Map<java.lang.String, java.lang.Object>>> getNotifications() {
        return null;
    }
    
    public final boolean isProcessingNotification() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.lang.Boolean> getLastActionResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCurrentFilter() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCurrentStatus() {
        return null;
    }
    
    public NotificationUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.List<java.util.Map<java.lang.String, java.lang.Object>>> component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.lang.Boolean> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.NotificationUiState copy(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> notifications, boolean isProcessingNotification, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<java.lang.Boolean> lastActionResult, @org.jetbrains.annotations.NotNull
    java.lang.String currentFilter, @org.jetbrains.annotations.NotNull
    java.lang.String currentStatus) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}