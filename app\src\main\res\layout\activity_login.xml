<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="60dp"
            android:layout_marginBottom="40dp">

            <!-- App Icon -->
            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_person_modern"
                android:background="@drawable/card_background"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:tint="@color/primary_color" />

            <!-- Welcome Text -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="خوش آمدید به حسابداری"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/text_white"
                android:layout_marginBottom="8dp"
                android:fontFamily="sans-serif-medium" />

            <!-- App Name -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="سیستم مدیریت مالی"
                android:textSize="16sp"
                android:textColor="@color/text_white"
                android:alpha="0.8"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- Login Form Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="12dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="32dp">

                <!-- Form Title -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ورود به حساب کاربری"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_gravity="center"
                    android:layout_marginBottom="24dp"
                    android:fontFamily="sans-serif-medium" />

                <!-- Username Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:hint="نام کاربری"
                    app:startIconDrawable="@drawable/ic_person_modern"
                    app:startIconTint="@color/primary_color"
                    app:hintTextColor="@color/text_secondary"
                    app:boxStrokeColor="@color/primary_color"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etUsername"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Password Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="32dp"
                    android:hint="رمز عبور"
                    app:startIconDrawable="@drawable/ic_lock_modern"
                    app:startIconTint="@color/primary_color"
                    app:endIconMode="password_toggle"
                    app:endIconTint="@color/text_secondary"
                    app:hintTextColor="@color/text_secondary"
                    app:boxStrokeColor="@color/primary_color"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:fontFamily="sans-serif" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Login Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLogin"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="ورود به سیستم"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_white"
                    android:background="@drawable/button_primary"
                    app:cornerRadius="12dp"
                    android:elevation="4dp"
                    android:fontFamily="sans-serif-medium"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Error Message -->
        <TextView
            android:id="@+id/tvError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textColor="@color/error_color"
            android:textSize="14sp"
            android:visibility="gone"
            android:background="@drawable/card_background"
            android:padding="12dp"
            android:drawablePadding="8dp"
            android:fontFamily="sans-serif" />

        <!-- Footer -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="نسخه 1.0.0"
            android:textSize="12sp"
            android:textColor="@color/text_white"
            android:alpha="0.6"
            android:layout_marginTop="40dp"
            android:fontFamily="sans-serif" />

    </LinearLayout>

</ScrollView>
