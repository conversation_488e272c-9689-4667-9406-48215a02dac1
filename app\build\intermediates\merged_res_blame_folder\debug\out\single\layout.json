[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\nav_header_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\nav_header_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\item_notification.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\item_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_withdrawal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_withdrawal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\item_transaction.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\item_transaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\item_notification_new.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\item_notification_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_inventory.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_inventory.xml"}, {"merged": "com.example.ma.app-mergeDebugResources-53:/layout/item_notification_new.xml", "source": "com.example.ma.app-main-56:/layout/item_notification_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_financial.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_financial.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\dialog_theme_selection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\dialog_theme_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_expense.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_expense.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_statistics.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_statistics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_transactions.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_transactions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_database_setup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_database_setup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-mergeDebugResources-53:\\layout\\activity_notification.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.example.ma.app-main-56:\\layout\\activity_notification.xml"}]