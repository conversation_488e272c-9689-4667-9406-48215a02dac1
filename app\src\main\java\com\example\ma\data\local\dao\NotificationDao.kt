package com.example.ma.data.local.dao

import androidx.room.*
import com.example.ma.data.local.entity.NotificationEntity
import com.example.ma.data.local.entity.NotificationStatus
import com.example.ma.data.local.entity.TransactionType
import com.example.ma.data.local.entity.SyncStatus
import kotlinx.coroutines.flow.Flow

/**
 * DAO برای عملیات اعلانات
 */
@Dao
interface NotificationDao {
    
    /**
     * دریافت همه اعلانات
     */
    @Query("SELECT * FROM notifications ORDER BY created_at DESC")
    fun getAllNotifications(): Flow<List<NotificationEntity>>
    
    /**
     * دریافت اعلانات کاربر
     */
    @Query("SELECT * FROM notifications WHERE to_user_id = :userId ORDER BY created_at DESC")
    fun getNotificationsForUser(userId: String): Flow<List<NotificationEntity>>
    
    /**
     * دریافت اعلانات ارسالی کاربر
     */
    @Query("SELECT * FROM notifications WHERE from_user_id = :userId ORDER BY created_at DESC")
    fun getNotificationsSentByUser(userId: String): Flow<List<NotificationEntity>>
    
    /**
     * دریافت اعلانات بر اساس وضعیت
     */
    @Query("SELECT * FROM notifications WHERE status = :status ORDER BY created_at DESC")
    fun getNotificationsByStatus(status: NotificationStatus): Flow<List<NotificationEntity>>
    
    /**
     * دریافت اعلانات در انتظار
     */
    @Query("SELECT * FROM notifications WHERE status = 'PENDING' ORDER BY created_at DESC")
    fun getPendingNotifications(): Flow<List<NotificationEntity>>
    
    /**
     * دریافت اعلانات خوانده نشده
     */
    @Query("SELECT * FROM notifications WHERE read_at IS NULL ORDER BY created_at DESC")
    fun getUnreadNotifications(): Flow<List<NotificationEntity>>
    
    /**
     * دریافت اعلانات نیازمند همگام‌سازی
     */
    @Query("SELECT * FROM notifications WHERE sync_status != 'SYNCED'")
    suspend fun getNotificationsNeedingSync(): List<NotificationEntity>
    
    /**
     * دریافت اعلان بر اساس ID
     */
    @Query("SELECT * FROM notifications WHERE id = :notificationId")
    suspend fun getNotificationById(notificationId: String): NotificationEntity?
    
    /**
     * درج اعلان جدید
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNotification(notification: NotificationEntity): Long
    
    /**
     * درج چندین اعلان
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNotifications(notifications: List<NotificationEntity>): List<Long>
    
    /**
     * بروزرسانی اعلان
     */
    @Update
    suspend fun updateNotification(notification: NotificationEntity): Int
    
    /**
     * حذف اعلان
     */
    @Delete
    suspend fun deleteNotification(notification: NotificationEntity): Int
    
    /**
     * بروزرسانی وضعیت اعلان
     */
    @Query("UPDATE notifications SET status = :status, updated_at = :timestamp WHERE id = :notificationId")
    suspend fun updateNotificationStatus(
        notificationId: String,
        status: NotificationStatus,
        timestamp: Long = System.currentTimeMillis()
    ): Int
    
    /**
     * علامت‌گذاری به عنوان خوانده شده
     */
    @Query("UPDATE notifications SET read_at = :timestamp WHERE id = :notificationId")
    suspend fun markAsRead(notificationId: String, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * علامت‌گذاری همه اعلانات کاربر به عنوان خوانده شده
     */
    @Query("UPDATE notifications SET read_at = :timestamp WHERE to_user_id = :userId AND read_at IS NULL")
    suspend fun markAllAsReadForUser(userId: String, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * بروزرسانی وضعیت همگام‌سازی
     */
    @Query("UPDATE notifications SET sync_status = :status, last_sync = :timestamp WHERE id = :notificationId")
    suspend fun updateSyncStatus(
        notificationId: String,
        status: SyncStatus,
        timestamp: Long = System.currentTimeMillis()
    ): Int
    
    /**
     * تنظیم remote ID
     */
    @Query("UPDATE notifications SET remote_id = :remoteId WHERE id = :notificationId")
    suspend fun setRemoteId(notificationId: String, remoteId: String): Int
    
    /**
     * شمارش اعلانات خوانده نشده کاربر
     */
    @Query("SELECT COUNT(*) FROM notifications WHERE to_user_id = :userId AND read_at IS NULL")
    suspend fun getUnreadCountForUser(userId: String): Int
    
    /**
     * شمارش اعلانات در انتظار کاربر
     */
    @Query("SELECT COUNT(*) FROM notifications WHERE to_user_id = :userId AND status = 'PENDING'")
    suspend fun getPendingCountForUser(userId: String): Int
    
    /**
     * دریافت اعلانات بر اساس نوع تراکنش
     */
    @Query("SELECT * FROM notifications WHERE transaction_type = :type ORDER BY created_at DESC")
    fun getNotificationsByTransactionType(type: TransactionType): Flow<List<NotificationEntity>>
    
    /**
     * جستجو در اعلانات
     */
    @Query("SELECT * FROM notifications WHERE description LIKE :query ORDER BY created_at DESC")
    fun searchNotifications(query: String): Flow<List<NotificationEntity>>
    
    /**
     * دریافت اعلانات در بازه زمانی
     */
    @Query("SELECT * FROM notifications WHERE created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    fun getNotificationsInRange(startTime: Long, endTime: Long): Flow<List<NotificationEntity>>
    
    /**
     * حذف اعلانات قدیمی
     */
    @Query("DELETE FROM notifications WHERE created_at < :cutoffTime")
    suspend fun deleteOldNotifications(cutoffTime: Long): Int
    
    /**
     * پاک کردن همه اعلانات
     */
    @Query("DELETE FROM notifications")
    suspend fun clearAllNotifications(): Int
    
    /**
     * شمارش کل اعلانات
     */
    @Query("SELECT COUNT(*) FROM notifications")
    suspend fun getNotificationCount(): Int
}
