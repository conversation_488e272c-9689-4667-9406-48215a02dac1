package com.example.ma.ui.model;

/**
 * UI State برای صفحه آمار
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u000e\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bc\u0012\u001a\b\u0002\u0010\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00040\u0003\u0012\u001a\b\u0002\u0010\u0007\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00040\u0003\u0012\u001a\b\u0002\u0010\b\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00040\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\u001b\u0010\u0011\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00040\u0003H\u00c6\u0003J\u001b\u0010\u0012\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00040\u0003H\u00c6\u0003J\u001b\u0010\u0013\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00040\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\nH\u00c6\u0003Jg\u0010\u0015\u001a\u00020\u00002\u001a\b\u0002\u0010\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00040\u00032\u001a\b\u0002\u0010\u0007\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00040\u00032\u001a\b\u0002\u0010\b\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00040\u00032\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\n2\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0005H\u00d6\u0001R#\u0010\u0007\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR#\u0010\b\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u000fR#\u0010\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\r\u00a8\u0006\u001b"}, d2 = {"Lcom/example/ma/ui/model/StatisticsUiState;", "", "personalStats", "Lcom/example/ma/ui/model/UiState;", "", "", "", "globalStats", "inventoryStats", "isRefreshing", "", "(Lcom/example/ma/ui/model/UiState;Lcom/example/ma/ui/model/UiState;Lcom/example/ma/ui/model/UiState;Z)V", "getGlobalStats", "()Lcom/example/ma/ui/model/UiState;", "getInventoryStats", "()Z", "getPersonalStats", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class StatisticsUiState {
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> personalStats = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> globalStats = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Object>> inventoryStats = null;
    private final boolean isRefreshing = false;
    
    public StatisticsUiState(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, java.lang.Double>> personalStats, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, java.lang.Double>> globalStats, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> inventoryStats, boolean isRefreshing) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> getPersonalStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> getGlobalStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Object>> getInventoryStats() {
        return null;
    }
    
    public final boolean isRefreshing() {
        return false;
    }
    
    public StatisticsUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Double>> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.UiState<java.util.Map<java.lang.String, java.lang.Object>> component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.ma.ui.model.StatisticsUiState copy(@org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, java.lang.Double>> personalStats, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, java.lang.Double>> globalStats, @org.jetbrains.annotations.NotNull
    com.example.ma.ui.model.UiState<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> inventoryStats, boolean isRefreshing) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}