<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:background="@color/primary_color"
            android:elevation="4dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="آمار و گزارشات"
            app:titleTextColor="@color/text_white" />

        <!-- آمار شخصی -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👤 آمار شخصی شما"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp"
                    android:fontFamily="sans-serif-medium" />

                <!-- فروش‌های شخصی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📈 فروش‌های ثبت شده:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvPersonalSales"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="2,500,000 تومان" />

                </LinearLayout>

                <!-- هزینه‌های پرداختی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💼 هزینه‌های پرداختی:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvPersonalExpenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/error_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="1,200,000 تومان" />

                </LinearLayout>

                <!-- برداشت‌های انجام شده -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="👤 برداشت‌های انجام شده:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvPersonalWithdrawals"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/warning_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="500,000 تومان" />

                </LinearLayout>

                <!-- خط جداکننده -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/outline"
                    android:layout_marginVertical="12dp" />

                <!-- سود شخصی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💎 سود قابل برداشت:"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:fontFamily="sans-serif-medium" />

                    <TextView
                        android:id="@+id/tvPersonalProfit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="4,500,000 تومان" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- آمار کلی شرکت -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/surface_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🏢 آمار کلی شرکت"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp"
                    android:fontFamily="sans-serif-medium" />

                <!-- کل فروش‌ها -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📊 کل فروش‌ها:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvTotalSales"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/success_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="10,000,000 تومان" />

                </LinearLayout>

                <!-- کل هزینه‌ها -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💸 کل هزینه‌ها:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvTotalExpenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/error_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="4,000,000 تومان" />

                </LinearLayout>

                <!-- موجودی انبار -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📦 موجودی انبار:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tvCurrentStock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 عدد"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/info_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="20 عدد" />

                </LinearLayout>

                <!-- خط جداکننده -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/outline"
                    android:layout_marginVertical="12dp" />

                <!-- سود خالص -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💰 سود خالص:"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:fontFamily="sans-serif-medium" />

                    <TextView
                        android:id="@+id/tvNetProfit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 تومان"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:fontFamily="sans-serif-medium"
                        tools:text="6,000,000 تومان" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- عملیات سریع -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⚡ عملیات سریع"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp"
            android:fontFamily="sans-serif-medium" />

        <!-- ردیف اول عملیات -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp">

            <!-- ثبت هزینه -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardAddExpense"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/error_color"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="💼"
                        android:textSize="24sp"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ثبت هزینه"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_white"
                        android:gravity="center"
                        android:fontFamily="sans-serif-medium" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- برداشت شخصی -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardAddWithdrawal"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/info_color"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="👤"
                        android:textSize="24sp"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="برداشت"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_white"
                        android:gravity="center"
                        android:fontFamily="sans-serif-medium" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- ردیف دوم عملیات -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp">

            <!-- مدیریت انبار -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardManageInventory"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/warning_color"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📦"
                        android:textSize="24sp"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="انبار"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_white"
                        android:gravity="center"
                        android:fontFamily="sans-serif-medium" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- مشاهده تراکنش‌ها -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardViewTransactions"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/success_color"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📋"
                        android:textSize="24sp"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تراکنش‌ها"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_white"
                        android:gravity="center"
                        android:fontFamily="sans-serif-medium" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
