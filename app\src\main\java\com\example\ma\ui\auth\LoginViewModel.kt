package com.example.ma.ui.auth

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.ma.data.model.User
import com.example.ma.data.repository.AuthRepository
import kotlinx.coroutines.launch

/**
 * ViewModel برای مدیریت منطق ورود کاربران
 * این کلاس رابط بین UI و Repository برای احراز هویت است
 */
class LoginViewModel(application: Application) : AndroidViewModel(application) {

    private val authRepository = AuthRepository(application)

    private val _loginResult = MutableLiveData<LoginResult>()
    val loginResult: LiveData<LoginResult> = _loginResult

    private val _currentUser = MutableLiveData<User?>()
    val currentUser: LiveData<User?> = _currentUser
    
    init {
        viewModelScope.launch {
            try {
                _currentUser.value = authRepository.getCurrentUser()
            } catch (e: Exception) {
                _currentUser.value = null
            }
        }
    }

    /**
     * تلاش برای ورود کاربر به سیستم
     * @param username نام کاربری
     * @param password رمز عبور
     */
    fun login(username: String, password: String) {
        _loginResult.value = LoginResult.Loading

        viewModelScope.launch {
            val user = authRepository.login(username, password)
            if (user != null) {
                _currentUser.value = user
                _loginResult.value = LoginResult.Success(user)
            } else {
                _loginResult.value = LoginResult.Error("نام کاربری یا رمز عبور اشتباه است")
            }
        }
    }

    /**
     * بررسی وضعیت ورود کاربر
     * @return true اگر کاربر وارد شده باشد
     */
    fun isUserLoggedIn(): Boolean {
        return authRepository.isLoggedIn()
    }

    /**
     * خروج کاربر از سیستم
     */
    fun logout() {
        authRepository.logout()
        _currentUser.value = null
    }
}


